import { <PERSON>ada<PERSON> } from "next";
import { <PERSON> } from "@/i18n/routing";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowRight,
  Brain,
  Zap,
  BarChart3,
  Globe,
  Database,
  Settings,
  TrendingUp,
  Users,
  Shield,
  Clock,
  DollarSign,
} from "lucide-react";

export const metadata: Metadata = {
  title: "AI & Automation",
  description: "Learn about SME Analytica's AI capabilities, automation features, and the technology powering intelligent business insights.",
};

export default function AIAutomationPage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-background via-background to-muted/20 py-20 md:py-32">
        <div className="container relative">
          <div className="mx-auto max-w-4xl text-center">
            <Badge variant="outline" className="mb-4">
              AI & Automation
            </Badge>
            <h1 className="mb-6 text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl">
              Intelligent Business Automation
            </h1>
            <p className="mb-8 text-xl text-muted-foreground sm:text-2xl">
              Advanced AI orchestration engine designed to process 50+ data sources with high prediction accuracy, 
              delivering automated insights with the potential to generate significant business value for SMEs.
            </p>
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button size="lg" asChild>
                <Link href="/modules">
                  Explore AI Features
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/integrations">
                  API Documentation
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* AI Engine Overview */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-4xl">
            <h2 className="mb-8 text-3xl font-bold tracking-tight sm:text-4xl">
              Custom AI Engine & Model Orchestra
            </h2>
            <div className="mb-8 space-y-4 text-lg text-muted-foreground">
              <p>
                SME Analytica features a proprietary AI orchestration engine that combines multiple state-of-the-art language models 
                through advanced ensemble learning and confidence weighting. Our intelligent model selection system chooses optimal models 
                for each business scenario, with specialized fine-tuning for industry-specific applications.
              </p>
              <p>
                The engine features intelligent model selection based on task complexity, real-time confidence scoring, 
                automated accuracy monitoring, and adaptive learning that continuously improves based on actual business outcomes. 
                The system is engineered to deliver significantly faster insights compared to single-model approaches.
              </p>
            </div>
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <Brain className="h-8 w-8 text-primary" />
                  <CardTitle>Custom AI Engine</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold">Intelligent Model Selection</h4>
                    <p className="text-sm text-muted-foreground">Proprietary algorithm selects optimal AI models from our model orchestra based on query complexity, data type, and industry context</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Confidence Scoring</h4>
                    <p className="text-sm text-muted-foreground">Real-time confidence scoring targets high prediction accuracy through weighted ensemble learning and performance monitoring</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Multi-Model Orchestration</h4>
                    <p className="text-sm text-muted-foreground">Proprietary ensemble learning combines outputs from 4+ AI models, designed to deliver significantly faster insights than single-model approaches</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Adaptive Learning</h4>
                    <p className="text-sm text-muted-foreground">Continuous learning system designed to improve prediction accuracy and optimize model selection algorithms based on business outcomes</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <Settings className="h-8 w-8 text-primary" />
                  <CardTitle>Model Orchestra</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold">Dynamic Pricing Models</h4>
                    <p className="text-sm text-muted-foreground">Specialized models optimized for pricing analysis, competitive intelligence, and revenue optimization</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Market Analysis Models</h4>
                    <p className="text-sm text-muted-foreground">Industry-specific models for strategic insights, market research, and complex business intelligence</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Growth Strategy Models</h4>
                    <p className="text-sm text-muted-foreground">Trend analysis and pattern recognition models optimized for business growth opportunities</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Sentiment Analysis Models</h4>
                    <p className="text-sm text-muted-foreground">Customer feedback processing and brand sentiment models with industry-specific training</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* AI Engine Architecture */}
      <section className="bg-muted/30 py-20">
        <div className="container">
          <div className="mx-auto max-w-4xl">
            <h2 className="mb-8 text-3xl font-bold tracking-tight sm:text-4xl text-center">
              AI Engine Architecture
            </h2>
            <div className="grid gap-8 md:grid-cols-3">
              <Card>
                <CardHeader>
                  <Database className="h-8 w-8 text-primary" />
                  <CardTitle>Data Quality Assessment</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold">Accuracy Manager</h4>
                    <p className="text-sm text-muted-foreground">Evaluates data quality and freshness</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Volume Scoring</h4>
                    <p className="text-sm text-muted-foreground">Assesses data coverage and completeness</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Competitor Coverage</h4>
                    <p className="text-sm text-muted-foreground">Measures competitive intelligence depth</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <Brain className="h-8 w-8 text-primary" />
                  <CardTitle>Model Selection Logic</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold">Analysis-Specific Models</h4>
                    <p className="text-sm text-muted-foreground">Different models optimized for different analysis types</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Tier-Based Access</h4>
                    <p className="text-sm text-muted-foreground">Premium users get access to advanced models</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Fallback Systems</h4>
                    <p className="text-sm text-muted-foreground">Automatic fallback to alternative models</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <TrendingUp className="h-8 w-8 text-primary" />
                  <CardTitle>Confidence Weighting</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold">Multi-Factor Scoring</h4>
                    <p className="text-sm text-muted-foreground">Combines data quality, model confidence, and freshness</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Dynamic Weights</h4>
                    <p className="text-sm text-muted-foreground">Analysis-specific weight factors for optimal results</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Continuous Learning</h4>
                    <p className="text-sm text-muted-foreground">Improves accuracy based on real-world outcomes</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Specialized Analysis Types */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-4xl">
            <h2 className="mb-8 text-3xl font-bold tracking-tight sm:text-4xl text-center">
              Specialized Analysis Capabilities
            </h2>
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <BarChart3 className="h-8 w-8 text-primary" />
                  <CardTitle>Business Intelligence</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold">Market Analysis</h4>
                    <p className="text-sm text-muted-foreground">Advanced AI-powered market intelligence with optimized parameters</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Competitor Analysis</h4>
                    <p className="text-sm text-muted-foreground">AI-driven competitive landscape analysis</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Growth Strategy</h4>
                    <p className="text-sm text-muted-foreground">Advanced algorithms for trend analysis and growth opportunities</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Sales Forecasting</h4>
                    <p className="text-sm text-muted-foreground">Predictive analytics for revenue planning</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <Users className="h-8 w-8 text-primary" />
                  <CardTitle>Customer Intelligence</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold">Sentiment Analysis</h4>
                    <p className="text-sm text-muted-foreground">Advanced algorithms for precise sentiment detection</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Review Analysis</h4>
                    <p className="text-sm text-muted-foreground">Multi-source review aggregation and insights</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Customer Behavior</h4>
                    <p className="text-sm text-muted-foreground">Pattern recognition in customer interactions</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Trend Detection</h4>
                    <p className="text-sm text-muted-foreground">Early identification of customer preference shifts</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Dynamic Pricing Engine */}
      <section className="bg-muted/30 py-20">
        <div className="container">
          <div className="mx-auto max-w-4xl">
            <h2 className="mb-8 text-3xl font-bold tracking-tight sm:text-4xl text-center">
              Dynamic Pricing AI Engine
            </h2>
            <div className="mb-8 text-center">
              <p className="text-lg text-muted-foreground">
                Our sophisticated pricing engine uses multiple AI models to optimize pricing in real-time
              </p>
            </div>
            <div className="grid gap-6 md:grid-cols-3">
              <Card>
                <CardHeader>
                  <Brain className="h-8 w-8 text-primary" />
                  <CardTitle>Multi-Model Analysis</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold">Market Analysis</h4>
                    <p className="text-sm text-muted-foreground">Comprehensive market condition assessment</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Optimization Engine</h4>
                    <p className="text-sm text-muted-foreground">Technical pricing optimization algorithms</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Trend Analysis</h4>
                    <p className="text-sm text-muted-foreground">Future demand and trend predictions</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <TrendingUp className="h-8 w-8 text-primary" />
                  <CardTitle>Real-time Factors</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold">Market Conditions</h4>
                    <p className="text-sm text-muted-foreground">Live market data and competitor pricing</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Business Context</h4>
                    <p className="text-sm text-muted-foreground">Location, type, and historical performance</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Demand Patterns</h4>
                    <p className="text-sm text-muted-foreground">Traffic analysis and occupancy rates</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <DollarSign className="h-8 w-8 text-primary" />
                  <CardTitle>Optimization Goals</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold">Revenue Maximization</h4>
                    <p className="text-sm text-muted-foreground">Optimal pricing for maximum profit</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Customer Satisfaction</h4>
                    <p className="text-sm text-muted-foreground">Balanced pricing to maintain loyalty</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Market Position</h4>
                    <p className="text-sm text-muted-foreground">Competitive positioning strategy</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Automation Features */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              Intelligent Automation
            </h2>
            <p className="mb-12 text-lg text-muted-foreground">
              Streamline your business operations with AI-powered automation
            </p>
          </div>
          <div className="grid gap-6 md:grid-cols-3">
            {[
              {
                icon: Zap,
                title: "Real-time Processing",
                description: "Instant analysis and insights as data becomes available",
              },
              {
                icon: TrendingUp,
                title: "Dynamic Pricing",
                description: "Automatic price adjustments based on market conditions",
              },
              {
                icon: Globe,
                title: "Multi-source Data",
                description: "Automated data collection from Google Places, TripAdvisor, Yelp",
              },
              {
                icon: Users,
                title: "Customer Insights",
                description: "Automated sentiment tracking and customer behavior analysis",
              },
              {
                icon: Shield,
                title: "Security & Privacy",
                description: "Automated data protection and compliance monitoring",
              },
              {
                icon: Clock,
                title: "Scheduled Reports",
                description: "Automated generation and delivery of business reports",
              },
            ].map((feature, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <feature.icon className="mx-auto h-12 w-12 text-primary" />
                  <CardTitle className="text-lg">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">{feature.description}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Data Sources */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-4xl">
            <h2 className="mb-8 text-3xl font-bold tracking-tight sm:text-4xl text-center">
              Data Sources & Integration
            </h2>
            <div className="grid gap-8 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <Database className="h-8 w-8 text-primary" />
                  <CardTitle>External Data Sources</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold">Google Places API</h4>
                    <p className="text-sm text-muted-foreground">Business information, reviews, and location data</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">TripAdvisor API</h4>
                    <p className="text-sm text-muted-foreground">Travel and hospitality industry insights</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Yelp API</h4>
                    <p className="text-sm text-muted-foreground">Local business reviews and ratings</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Real-time Market Data</h4>
                    <p className="text-sm text-muted-foreground">Live market conditions and competitor pricing</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <Settings className="h-8 w-8 text-primary" />
                  <CardTitle>Processing Pipeline</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold">Data Ingestion</h4>
                    <p className="text-sm text-muted-foreground">Automated collection and normalization</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">AI Analysis</h4>
                    <p className="text-sm text-muted-foreground">Multi-model processing for comprehensive insights</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Quality Assurance</h4>
                    <p className="text-sm text-muted-foreground">Automated validation and confidence scoring</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Delivery</h4>
                    <p className="text-sm text-muted-foreground">Real-time updates via API and mobile app</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-muted/30 py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              Ready to Harness AI for Your Business?
            </h2>
            <p className="mb-8 text-lg text-muted-foreground">
              Discover how SME Analytica's AI and automation can transform your business operations
            </p>
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button size="lg" asChild>
                <Link href="/modules">
                  Explore AI Features
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/for-businesses">
                  For Businesses
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
