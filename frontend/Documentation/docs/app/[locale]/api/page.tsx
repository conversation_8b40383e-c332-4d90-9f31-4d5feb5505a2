import { useTranslations } from 'next-intl';
import { Metada<PERSON> } from 'next';
import Link from 'next/link';
import { FiCode, FiBook, FiZap, FiShield, FiGlobe, FiDownload } from 'react-icons/fi';

export const metadata: Metadata = {
  title: 'API Reference - SME Analytica',
  description: 'Comprehensive API documentation for SME Analytica\'s AI-powered business intelligence platform. Integrate AI analysis, pricing optimization, and market insights into your applications.',
  keywords: ['API', 'REST API', 'AI API', 'business intelligence API', 'pricing API', 'market analysis API'],
  openGraph: {
    title: 'SME Analytica API Reference',
    description: 'Integrate AI-powered business intelligence into your applications with our comprehensive REST API.',
    type: 'website',
  },
};

// Add structured data for API documentation
const apiStructuredData = {
  '@context': 'https://schema.org',
  '@type': 'APIReference',
  name: 'SME Analytica API',
  description: 'AI-powered business intelligence platform API for small and medium enterprises',
  url: 'https://docs.smeanalytica.dev/api',
  documentation: 'https://docs.smeanalytica.dev/api',
  programmingModel: 'REST',
  applicationCategory: 'BusinessApplication',
  operatingSystem: 'Any',
  provider: {
    '@type': 'Organization',
    name: 'SME Analytica',
    url: 'https://smeanalytica.dev'
  },
  featureList: [
    'AI-Powered Market Analysis',
    'Dynamic Pricing Optimization', 
    'Customer Sentiment Analysis',
    'Growth Forecasting',
    'Competitor Analysis',
    'Real-time Analytics'
  ]
};

const endpoints = [
  {
    method: 'POST',
    path: '/analysis/{type}',
    description: 'Submit AI analysis request for pricing, market, sentiment, or growth analysis',
    category: 'Analysis'
  },
  {
    method: 'GET',
    path: '/analysis/{id}',
    description: 'Retrieve analysis results by ID',
    category: 'Analysis'
  },
  {
    method: 'POST',
    path: '/auth/signup',
    description: 'Register new user account',
    category: 'Authentication'
  },
  {
    method: 'POST',
    path: '/auth/signin',
    description: 'Authenticate user and receive JWT token',
    category: 'Authentication'
  },
  {
    method: 'GET',
    path: '/user/profile',
    description: 'Get current user profile information',
    category: 'User'
  },
  {
    method: 'GET',
    path: '/user/subscription',
    description: 'Get subscription status and usage information',
    category: 'User'
  }
];

const features = [
  {
    icon: FiZap,
    title: 'AI-Powered Analysis',
    description: 'Access advanced AI models including Claude Opus 4, GPT-4.1, and Gemini 2.0 Flash for business intelligence'
  },
  {
    icon: FiShield,
    title: 'Enterprise Security',
    description: 'JWT authentication, rate limiting, and enterprise-grade security for all API endpoints'
  },
  {
    icon: FiGlobe,
    title: 'Global Scale',
    description: 'Production-ready API with 99.9% uptime, global CDN, and automatic scaling'
  },
  {
    icon: FiCode,
    title: 'Developer Friendly',
    description: 'RESTful design, comprehensive documentation, and SDKs for popular programming languages'
  }
];

export default function APIPage() {
  const t = useTranslations('api');

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800">
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(apiStructuredData, null, 2)
        }}
      />

      <div className="container mx-auto px-6 py-12">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center gap-3 mb-6">
            <FiCode className="w-12 h-12 text-blue-600" />
            <h1 className="text-4xl md:text-5xl font-bold text-slate-900 dark:text-white">
              API Reference
            </h1>
          </div>
          <p className="text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto mb-8">
            Integrate AI-powered business intelligence into your applications with our comprehensive REST API.
            Access market analysis, pricing optimization, and sentiment analysis capabilities.
          </p>
          
          <div className="flex flex-wrap gap-4 justify-center">
            <Link
              href="/openapi.yaml"
              className="inline-flex items-center gap-2 bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
            >
              <FiDownload className="w-5 h-5" />
              Download OpenAPI Spec
            </Link>
            <Link
              href="/api/authentication"
              className="inline-flex items-center gap-2 border border-blue-600 text-blue-600 px-6 py-3 rounded-lg font-medium hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
            >
              <FiBook className="w-5 h-5" />
              Get Started
            </Link>
          </div>
        </div>

        {/* Features */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {features.map((feature, index) => (
            <div
              key={index}
              className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg border border-slate-200 dark:border-slate-700"
            >
              <feature.icon className="w-8 h-8 text-blue-600 mb-4" />
              <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">
                {feature.title}
              </h3>
              <p className="text-slate-600 dark:text-slate-300 text-sm">
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        {/* Quick Start */}
        <div className="bg-white dark:bg-slate-800 rounded-xl p-8 shadow-lg border border-slate-200 dark:border-slate-700 mb-16">
          <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-6">
            Quick Start
          </h2>
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-4">
                1. Authentication
              </h3>
              <div className="bg-slate-100 dark:bg-slate-900 rounded-lg p-4 font-mono text-sm">
                <div className="text-green-600 dark:text-green-400">POST</div>
                <div className="text-slate-600 dark:text-slate-300">/api/v1/auth/signin</div>
                <div className="mt-2 text-slate-500 dark:text-slate-400">
                  Get your JWT token for API access
                </div>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-4">
                2. Submit Analysis
              </h3>
              <div className="bg-slate-100 dark:bg-slate-900 rounded-lg p-4 font-mono text-sm">
                <div className="text-blue-600 dark:text-blue-400">POST</div>
                <div className="text-slate-600 dark:text-slate-300">/api/v1/analysis/pricing</div>
                <div className="mt-2 text-slate-500 dark:text-slate-400">
                  Submit AI analysis request
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* API Endpoints */}
        <div className="bg-white dark:bg-slate-800 rounded-xl p-8 shadow-lg border border-slate-200 dark:border-slate-700 mb-16">
          <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-6">
            API Endpoints
          </h2>
          <div className="space-y-4">
            {endpoints.map((endpoint, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-4 bg-slate-50 dark:bg-slate-900 rounded-lg border border-slate-200 dark:border-slate-700"
              >
                <div className="flex items-center gap-4">
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    endpoint.method === 'GET' 
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                  }`}>
                    {endpoint.method}
                  </span>
                  <code className="text-slate-900 dark:text-white font-mono">
                    {endpoint.path}
                  </code>
                </div>
                <div className="text-right">
                  <div className="text-sm text-slate-600 dark:text-slate-300">
                    {endpoint.description}
                  </div>
                  <div className="text-xs text-slate-500 dark:text-slate-400">
                    {endpoint.category}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Base URL */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-8 border border-blue-200 dark:border-blue-800">
          <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-4">
            Base URL
          </h2>
          <div className="bg-white dark:bg-slate-800 rounded-lg p-4 border border-slate-200 dark:border-slate-700">
            <code className="text-lg font-mono text-blue-600 dark:text-blue-400">
              https://api.smeanalytica.dev/api/v1
            </code>
          </div>
          <p className="text-slate-600 dark:text-slate-300 mt-4">
            All API requests should be made to this base URL. Authentication is required for all endpoints
            except signup and signin.
          </p>
        </div>

        {/* Rate Limits */}
        <div className="mt-16 bg-amber-50 dark:bg-amber-900/20 rounded-xl p-8 border border-amber-200 dark:border-amber-800">
          <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-4">
            Rate Limits
          </h2>
          <div className="grid md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-amber-600 dark:text-amber-400">12</div>
              <div className="text-sm text-slate-600 dark:text-slate-300">Basic Plan</div>
              <div className="text-xs text-slate-500 dark:text-slate-400">analyses/month</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-amber-600 dark:text-amber-400">50</div>
              <div className="text-sm text-slate-600 dark:text-slate-300">Premium Plan</div>
              <div className="text-xs text-slate-500 dark:text-slate-400">analyses/month</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-amber-600 dark:text-amber-400">∞</div>
              <div className="text-sm text-slate-600 dark:text-slate-300">Enterprise Plan</div>
              <div className="text-xs text-slate-500 dark:text-slate-400">unlimited</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
