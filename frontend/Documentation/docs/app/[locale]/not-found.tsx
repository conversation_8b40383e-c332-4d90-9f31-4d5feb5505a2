'use client';

import { useTranslations } from 'next-intl';
import { Link } from '@/i18n/routing';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Image from 'next/image';
import { 
  Home, 
  Search, 
  Mail, 
  BookOpen, 
  Smartphone, 
  Zap, 
  Building2,
  Bot,
  Calendar,
  ArrowRight,
  AlertTriangle
} from 'lucide-react';

export default function NotFound() {
  const t = useTranslations('notFound');

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="container mx-auto px-4 py-16">
        {/* Header Section */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div className="relative">
              {/* Animated glow effect */}
              <div className="absolute inset-0 bg-primary/20 rounded-full blur-xl animate-pulse" />
              <div className="relative bg-card border-2 border-primary/20 rounded-full p-6">
                <Image
                  src="/sme-logo.png"
                  alt="SME Analytica"
                  width={80}
                  height={80}
                  className="h-20 w-20"
                />
              </div>
            </div>
          </div>
          
          <div className="space-y-4">
            <h1 className="text-6xl font-bold text-primary">404</h1>
            <h2 className="text-3xl font-semibold text-foreground">
              {t('subtitle')}
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              {t('description')}
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          <Button asChild size="lg" className="gap-2">
            <Link href="/">
              <Home className="h-4 w-4" />
              {t('backHome')}
            </Link>
          </Button>
          
          <Button asChild variant="outline" size="lg" className="gap-2">
            <Link href="/modules">
              <Zap className="h-4 w-4" />
              {t('exploreModules')}
            </Link>
          </Button>
          
          <Button asChild variant="outline" size="lg" className="gap-2">
            <Link href="/integrations">
              <Search className="h-4 w-4" />
              {t('searchDocs')}
            </Link>
          </Button>
          
          <Button asChild variant="ghost" size="lg" className="gap-2">
            <Link href="mailto:<EMAIL>">
              <Mail className="h-4 w-4" />
              {t('contactSupport')}
            </Link>
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Popular Destinations */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5 text-primary" />
                {t('suggestions.title')}
              </CardTitle>
              <CardDescription>
                {t('availablePages')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Link 
                href="/platform" 
                className="flex items-center justify-between p-3 rounded-lg border hover:bg-accent/50 transition-colors group"
              >
                <div className="flex items-center gap-3">
                  <BookOpen className="h-4 w-4 text-primary" />
                  <span className="font-medium">{t('suggestions.platform')}</span>
                </div>
                <ArrowRight className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />
              </Link>
              
              <Link 
                href="/modules/menuflow" 
                className="flex items-center justify-between p-3 rounded-lg border hover:bg-accent/50 transition-colors group"
              >
                <div className="flex items-center gap-3">
                  <Zap className="h-4 w-4 text-primary" />
                  <span className="font-medium">{t('suggestions.menuflow')}</span>
                </div>
                <ArrowRight className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />
              </Link>
              
              <Link 
                href="/modules/sme-app" 
                className="flex items-center justify-between p-3 rounded-lg border hover:bg-accent/50 transition-colors group"
              >
                <div className="flex items-center gap-3">
                  <Smartphone className="h-4 w-4 text-primary" />
                  <span className="font-medium">{t('suggestions.smeApp')}</span>
                </div>
                <ArrowRight className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />
              </Link>
              
              <Link 
                href="/integrations" 
                className="flex items-center justify-between p-3 rounded-lg border hover:bg-accent/50 transition-colors group"
              >
                <div className="flex items-center gap-3">
                  <Search className="h-4 w-4 text-primary" />
                  <span className="font-medium">{t('suggestions.apiDocs')}</span>
                </div>
                <ArrowRight className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />
              </Link>
              
              <Link 
                href="/for-businesses" 
                className="flex items-center justify-between p-3 rounded-lg border hover:bg-accent/50 transition-colors group"
              >
                <div className="flex items-center gap-3">
                  <Building2 className="h-4 w-4 text-primary" />
                  <span className="font-medium">{t('suggestions.business')}</span>
                </div>
                <ArrowRight className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />
              </Link>
            </CardContent>
          </Card>

          {/* Missing Modules */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-orange-500" />
                Upcoming Modules
              </CardTitle>
              <CardDescription>
                Modules currently in development
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Connecto Module */}
              <div className="p-4 rounded-lg border border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950/20">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Bot className="h-5 w-5 text-orange-600" />
                    <h3 className="font-semibold text-orange-900 dark:text-orange-100">
                      {t('missingModules.connecto.title')}
                    </h3>
                  </div>
                  <Badge variant="secondary" className="bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                    {t('missingModules.connecto.status')}
                  </Badge>
                </div>
                <p className="text-sm text-orange-700 dark:text-orange-300">
                  {t('missingModules.connecto.description')}
                </p>
              </div>

              {/* Ticketing Module */}
              <div className="p-4 rounded-lg border border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950/20">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-5 w-5 text-blue-600" />
                    <h3 className="font-semibold text-blue-900 dark:text-blue-100">
                      {t('missingModules.ticketing.title')}
                    </h3>
                  </div>
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                    {t('missingModules.ticketing.status')}
                  </Badge>
                </div>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  {t('missingModules.ticketing.description')}
                </p>
              </div>

              {/* External Links */}
              <div className="pt-4 border-t">
                <h4 className="font-medium mb-3 text-sm text-muted-foreground uppercase tracking-wide">
                  Live Applications
                </h4>
                <div className="space-y-2">
                  <a 
                    href="https://restaurants.smeanalytica.dev" 
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center justify-between p-2 rounded-md hover:bg-accent/50 transition-colors group text-sm"
                  >
                    <div className="flex items-center gap-2">
                      <Zap className="h-3 w-3 text-primary" />
                      <span>MenuFlow Demo</span>
                    </div>
                    <ArrowRight className="h-3 w-3 text-muted-foreground group-hover:text-primary transition-colors" />
                  </a>
                  
                  <a 
                    href="https://testflight.apple.com/join/kCjhqR4Q" 
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center justify-between p-2 rounded-md hover:bg-accent/50 transition-colors group text-sm"
                  >
                    <div className="flex items-center gap-2">
                      <Smartphone className="h-3 w-3 text-primary" />
                      <span>SME App (TestFlight)</span>
                    </div>
                    <ArrowRight className="h-3 w-3 text-muted-foreground group-hover:text-primary transition-colors" />
                  </a>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Footer Message */}
        <div className="text-center mt-12 pt-8 border-t">
          <p className="text-sm text-muted-foreground">
            Need help finding something specific? 
            <Link href="mailto:<EMAIL>" className="text-primary hover:underline ml-1">
              Contact our support team
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
