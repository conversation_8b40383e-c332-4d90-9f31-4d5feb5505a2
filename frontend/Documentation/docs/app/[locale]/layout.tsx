import { Inter } from 'next/font/google';
import { NextIntlClientProvider } from 'next-intl';
import { notFound } from 'next/navigation';
import { ReactNode } from 'react';
import '../globals.css';
import { ThemeProvider } from '@/components/theme-provider';
import { Navigation } from '@/components/navigation';
import { Footer } from '@/components/footer';

const inter = Inter({ subsets: ['latin'] });

type Props = {
  children: ReactNode;
  params: { locale: string };
};

async function getMessages(locale: string) {
  try {
    return (await import(`../../messages/${locale}.json`)).default;
  } catch (error) {
    notFound();
  }
}

export function generateStaticParams() {
  return [{ locale: 'en' }, { locale: 'es' }];
}

export async function generateMetadata({ params: { locale } }: Props) {
  const messages = await getMessages(locale);
  
  return {
    title: {
      template: '%s | SME Analytica Docs',
      default: 'SME Analytica - AI Business Intelligence Platform Documentation',
    },
    description: messages.meta?.description || 'Comprehensive documentation for SME Analytica - AI-powered business intelligence platform for small and medium enterprises.',
    keywords: ['SME Analytica', 'Business Intelligence', 'AI Analytics', 'Documentation', 'API'],
    authors: [{ name: 'SME Analytica Team' }],
    creator: 'SME Analytica',
    publisher: 'SME Analytica',
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL('https://docs.smeanalytica.dev'),
    alternates: {
      canonical: '/',
      languages: {
        'en-US': '/en',
        'es-ES': '/es',
      },
    },
    openGraph: {
      title: 'SME Analytica Documentation',
      description: 'AI-powered business intelligence platform for SMEs',
      url: 'https://docs.smeanalytica.dev',
      siteName: 'SME Analytica Docs',
      images: [
        {
          url: '/og-image.png',
          width: 1200,
          height: 630,
          alt: 'SME Analytica Documentation',
        },
      ],
      locale: locale,
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: 'SME Analytica Documentation',
      description: 'AI-powered business intelligence platform for SMEs',
      images: ['/og-image.png'],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}

export default async function LocaleLayout({ children, params: { locale } }: Props) {
  const messages = await getMessages(locale);

  return (
    <html lang={locale} suppressHydrationWarning>
      <body className={inter.className}>
        <NextIntlClientProvider locale={locale} messages={messages}>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          >
            <div className="relative flex min-h-screen flex-col">
              <Navigation />
              <main className="flex-1">{children}</main>
              <Footer />
            </div>
          </ThemeProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
