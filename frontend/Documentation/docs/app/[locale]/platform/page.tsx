import { Metada<PERSON> } from "next";
import { <PERSON> } from "@/i18n/routing";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useTranslations } from "next-intl";
import {
  ArrowRight,
  BarChart3,
  Brain,
  Database,
  Globe,
  Smartphone,
  Zap,
  TrendingUp,
  Users,
  Shield,
  Clock,
  Target,
} from "lucide-react";

export const metadata: Metadata = {
  title: "Platform Overview",
  description: "Learn about SME Analytica's AI-powered business intelligence platform, features, and capabilities.",
};

export default function PlatformPage() {
  const t = useTranslations('platform');

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-background via-background to-muted/20 py-20 md:py-32">
        <div className="container relative">
          <div className="mx-auto max-w-4xl text-center">
            <Badge variant="outline" className="mb-4">
              Platform Overview
            </Badge>
            <h1 className="mb-6 text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl">
              {t('title')}
            </h1>
            <p className="mb-8 text-xl text-muted-foreground sm:text-2xl">
              {t('description')}
            </p>
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button size="lg" asChild>
                <Link href="/modules">
                  Explore Modules
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="https://api.smeanalytica.dev/docs" target="_blank">
                  API Documentation
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* What is SME Analytica */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-4xl">
            <h2 className="mb-8 text-3xl font-bold tracking-tight sm:text-4xl">
              {t('whatIs.title')}
            </h2>
            <div className="space-y-4 text-lg text-muted-foreground">
              <p>
                {t('whatIs.description1')}
              </p>
              <p>
                {t('whatIs.description2')}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Core Benefits */}
      <section className="bg-muted/30 py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              Core Benefits
            </h2>
            <p className="mb-12 text-lg text-muted-foreground">
              Discover how SME Analytica transforms your business operations
            </p>
          </div>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {[
              {
                icon: Brain,
                title: "AI-Powered Insights",
                description: "Advanced machine learning models analyze your business data to provide actionable insights and predictions.",
              },
              {
                icon: TrendingUp,
                title: "Real-time Analytics",
                description: "Monitor your business performance with live dashboards and instant notifications for critical changes.",
              },
              {
                icon: Target,
                title: "Dynamic Pricing",
                description: "Optimize your pricing strategy with AI-driven recommendations based on market conditions and demand.",
              },
              {
                icon: Globe,
                title: "Multi-Platform Support",
                description: "Access your business intelligence from anywhere with our web, mobile, and API interfaces.",
              },
              {
                icon: Shield,
                title: "Enterprise Security",
                description: "Bank-level security with end-to-end encryption and compliance with industry standards.",
              },
              {
                icon: Clock,
                title: "Quick Deployment",
                description: "Get up and running in minutes with our plug-and-play modules and intuitive setup process.",
              },
            ].map((benefit, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <benefit.icon className="mx-auto h-12 w-12 text-primary" />
                  <CardTitle className="text-lg">{benefit.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">{benefit.description}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Tech Stack */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              Technology Stack
            </h2>
            <p className="mb-12 text-lg text-muted-foreground">
              Built with modern, scalable, and reliable technologies
            </p>
          </div>
          <div className="grid gap-8 md:grid-cols-2">
            <Card>
              <CardHeader>
                <Database className="h-8 w-8 text-primary" />
                <CardTitle>Backend Infrastructure</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-semibold">FastAPI Framework</h4>
                  <p className="text-sm text-muted-foreground">High-performance Python web framework with automatic API documentation</p>
                </div>
                <div>
                  <h4 className="font-semibold">Supabase Database</h4>
                  <p className="text-sm text-muted-foreground">PostgreSQL-based backend with real-time subscriptions and authentication</p>
                </div>
                <div>
                  <h4 className="font-semibold">OpenRouter AI</h4>
                  <p className="text-sm text-muted-foreground">Access to multiple AI models including GPT, Claude, and DeepSeek</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Smartphone className="h-8 w-8 text-primary" />
                <CardTitle>Frontend Applications</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-semibold">React Native Mobile</h4>
                  <p className="text-sm text-muted-foreground">Cross-platform mobile app for iOS and Android</p>
                </div>
                <div>
                  <h4 className="font-semibold">React Web Applications</h4>
                  <p className="text-sm text-muted-foreground">Modern web interfaces built with React and TypeScript</p>
                </div>
                <div>
                  <h4 className="font-semibold">Next.js Documentation</h4>
                  <p className="text-sm text-muted-foreground">Server-side rendered documentation with internationalization</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Brain className="h-8 w-8 text-primary" />
                <CardTitle>AI & Machine Learning</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-semibold">Multiple AI Models</h4>
                  <p className="text-sm text-muted-foreground">Multiple state-of-the-art AI models orchestrated for different use cases</p>
                </div>
                <div>
                  <h4 className="font-semibold">Dynamic Pricing Engine</h4>
                  <p className="text-sm text-muted-foreground">Real-time pricing optimization based on market conditions</p>
                </div>
                <div>
                  <h4 className="font-semibold">Sentiment Analysis</h4>
                  <p className="text-sm text-muted-foreground">Customer sentiment tracking from reviews and feedback</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Globe className="h-8 w-8 text-primary" />
                <CardTitle>Data Sources & Integrations</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-semibold">Google Places API</h4>
                  <p className="text-sm text-muted-foreground">Business information and customer reviews</p>
                </div>
                <div>
                  <h4 className="font-semibold">TripAdvisor & Yelp</h4>
                  <p className="text-sm text-muted-foreground">Customer feedback and competitor analysis</p>
                </div>
                <div>
                  <h4 className="font-semibold">POS System Integration</h4>
                  <p className="text-sm text-muted-foreground">Connect with existing point-of-sale systems</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Architecture Overview */}
      <section className="bg-muted/30 py-20">
        <div className="container">
          <div className="mx-auto max-w-4xl">
            <h2 className="mb-8 text-3xl font-bold tracking-tight sm:text-4xl text-center">
              Platform Architecture
            </h2>
            <Card>
              <CardHeader>
                <CardTitle>Modular Architecture</CardTitle>
                <CardDescription>
                  SME Analytica is built with a modular architecture that allows for easy scaling and customization
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="text-center">
                    <div className="mx-auto mb-2 h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                      <Users className="h-6 w-6 text-primary" />
                    </div>
                    <h4 className="font-semibold">User Layer</h4>
                    <p className="text-sm text-muted-foreground">Web, mobile, and API interfaces</p>
                  </div>
                  <div className="text-center">
                    <div className="mx-auto mb-2 h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                      <Zap className="h-6 w-6 text-primary" />
                    </div>
                    <h4 className="font-semibold">Business Logic</h4>
                    <p className="text-sm text-muted-foreground">AI engines and analysis modules</p>
                  </div>
                  <div className="text-center">
                    <div className="mx-auto mb-2 h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                      <Database className="h-6 w-6 text-primary" />
                    </div>
                    <h4 className="font-semibold">Data Layer</h4>
                    <p className="text-sm text-muted-foreground">Secure data storage and processing</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20">
        <div className="container">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight sm:text-4xl">
              Ready to Get Started?
            </h2>
            <p className="mb-8 text-lg text-muted-foreground">
              Explore our modules and see how SME Analytica can transform your business
            </p>
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button size="lg" asChild>
                <Link href="/modules">
                  Explore Modules
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/for-businesses">
                  For Businesses
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
