"use client";

import * as React from "react";
import { useTranslations } from "next-intl";
import { Link, usePathname } from "@/i18n/routing";
import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ThemeToggle } from "@/components/theme-toggle";
import { LanguageToggle } from "@/components/language-toggle";
import Image from "next/image";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Menu, BookOpen, Zap, Settings, Users, Code, Building } from "lucide-react";

const navigationItems = [
  {
    title: "Platform",
    href: "/platform",
    description: "Overview of SME Analytica platform",
    icon: BookOpen,
  },
  {
    title: "Modules",
    href: "/modules",
    description: "Explore our business modules",
    icon: Zap,
    items: [
      {
        title: "MenuFlow",
        href: "/modules/menuflow",
        description: "Dynamic pricing for restaurants",
      },
      {
        title: "SME App",
        href: "/modules/sme-app",
        description: "Mobile business analytics",
      },
      {
        title: "Connecto",
        href: "/modules/connecto",
        description: "AI voice receptionist",
      },
      {
        title: "Event Ticketing",
        href: "/modules/ticketing",
        description: "Event management platform",
      },
    ],
  },
  {
    title: "AI & Automation",
    href: "/ai-automation",
    description: "AI capabilities and automation",
    icon: Settings,
  },
  {
    title: "Integrations",
    href: "/integrations",
    description: "API docs and integrations",
    icon: Code,
  },
  {
    title: "For Businesses",
    href: "/for-businesses",
    description: "Use cases and benefits",
    icon: Building,
  },
  {
    title: "About",
    href: "/about",
    description: "About SME Analytica",
    icon: Users,
  },
];

export function Navigation() {
  const t = useTranslations("navigation");
  const pathname = usePathname();

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center">
        <div className="mr-4 hidden md:flex">
          <Link href="/" className="mr-6 flex items-center space-x-2">
            <Image
              src="/sme-logo.png"
              alt="SME Analytica"
              width={32}
              height={32}
              className="h-8 w-8"
            />
            <span className="hidden font-bold sm:inline-block">
              SME Analytica
            </span>
          </Link>
          <NavigationMenu>
            <NavigationMenuList>
              {navigationItems.map((item) => (
                <NavigationMenuItem key={item.href}>
                  {item.items ? (
                    <>
                      <NavigationMenuTrigger>{item.title}</NavigationMenuTrigger>
                      <NavigationMenuContent>
                        <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                          {item.items.map((subItem) => (
                            <ListItem
                              key={subItem.href}
                              title={subItem.title}
                              href={subItem.href}
                            >
                              {subItem.description}
                            </ListItem>
                          ))}
                        </ul>
                      </NavigationMenuContent>
                    </>
                  ) : (
                    <NavigationMenuLink asChild>
                      <Link
                        href={item.href}
                        className={cn(
                          "group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50",
                          pathname === item.href && "bg-accent"
                        )}
                      >
                        {item.title}
                      </Link>
                    </NavigationMenuLink>
                  )}
                </NavigationMenuItem>
              ))}
            </NavigationMenuList>
          </NavigationMenu>
        </div>
        <Sheet>
          <SheetTrigger asChild>
            <Button
              variant="ghost"
              className="mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden"
            >
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle Menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="pr-0">
            <SheetHeader>
              <SheetTitle>
                <Link href="/" className="flex items-center space-x-2">
                  <Image
                    src="/sme-logo.png"
                    alt="SME Analytica"
                    width={24}
                    height={24}
                    className="h-6 w-6"
                  />
                  <span className="font-bold">SME Analytica</span>
                </Link>
              </SheetTitle>
              <SheetDescription>
                AI Business Intelligence Platform
              </SheetDescription>
            </SheetHeader>
            <div className="my-4 h-[calc(100vh-8rem)] pb-10 pl-6">
              <div className="flex flex-col space-y-3">
                {navigationItems.map((item) => (
                  <div key={item.href}>
                    <Link
                      href={item.href}
                      className={cn(
                        "flex items-center space-x-2 text-sm font-medium",
                        pathname === item.href && "text-primary"
                      )}
                    >
                      <item.icon className="h-4 w-4" />
                      <span>{item.title}</span>
                    </Link>
                    {item.items && (
                      <div className="ml-6 mt-2 space-y-2">
                        {item.items.map((subItem) => (
                          <Link
                            key={subItem.href}
                            href={subItem.href}
                            className="block text-sm text-muted-foreground hover:text-foreground"
                          >
                            {subItem.title}
                          </Link>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </SheetContent>
        </Sheet>
        <div className="flex flex-1 items-center justify-between space-x-2 md:justify-end">
          <div className="w-full flex-1 md:w-auto md:flex-none">
            <Link href="/" className="flex items-center space-x-2 md:hidden">
              <Image
                src="/sme-logo.png"
                alt="SME Analytica"
                width={24}
                height={24}
                className="h-6 w-6"
              />
              <span className="font-bold">SME Analytica</span>
            </Link>
          </div>
          <nav className="flex items-center space-x-2">
            <LanguageToggle />
            <ThemeToggle />
            <Button asChild size="sm">
              <Link href="https://api.smeanalytica.dev/docs" target="_blank">
                API Docs
              </Link>
            </Button>
          </nav>
        </div>
      </div>
    </header>
  );
}

const ListItem = React.forwardRef<
  React.ElementRef<typeof Link>,
  React.ComponentPropsWithoutRef<typeof Link> & {
    title: string;
    children: React.ReactNode;
  }
>(({ className, title, children, ...props }, ref) => {
  return (
    <li>
      <NavigationMenuLink asChild>
        <Link
          ref={ref}
          className={cn(
            "block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
            className
          )}
          {...props}
        >
          <div className="text-sm font-medium leading-none">{title}</div>
          <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
            {children}
          </p>
        </Link>
      </NavigationMenuLink>
    </li>
  );
});
ListItem.displayName = "ListItem";
