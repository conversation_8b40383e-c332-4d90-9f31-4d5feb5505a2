# Frontend Documentation - Restaurant Management System

This document consolidates all frontend documentation for the Restaurant Management System (`/frontend/menu-flow-dynamo/`).

## Overview

The Restaurant Management System is a React + TypeScript application built with Vite, providing comprehensive restaurant operations management including:

- Menu management with dynamic pricing
- Order management and real-time notifications
- Table management and QR code generation
- Staff request system
- Customer feedback collection
- POS integration capabilities

## Key Features

### Dynamic Pricing System
- AI-powered real-time pricing adjustments
- Traffic-based pricing optimization
- Restaurant-specific configuration
- Integration with traffic analytics

### Collaborative Ordering
- Shared cart functionality for table-based ordering
- Real-time synchronization across devices
- Table participant management
- Order session handling

### Menu Management
- Hierarchical menu structure (Sections > Items)
- Image upload and management
- Multilingual support
- Nutritional information tracking

### Order Management
- Real-time order status updates
- Kitchen workflow integration
- Customer notification system
- Order history and analytics

## Technical Architecture

### Frontend Stack
- **React 18.3** with TypeScript 5.5
- **Vite 5.4** for build tooling
- **Tailwind CSS** for styling
- **shadcn/ui** component library
- **TanStack React Query** for state management
- **Supabase** for backend integration

### Key Services
- `menuService` - Menu data management
- `orderService` - Order lifecycle management
- `restaurantDbService` - Restaurant data operations
- `dynamicPricingService` - Pricing calculations
- `feedbackService` - Customer feedback handling

### State Management
- **RestaurantContext** - Restaurant selection and data
- **LanguageContext** - Internationalization
- **ThemeContext** - UI theming
- **AuthContext** - Authentication state

## Implementation History

### Phase 1: Core Restaurant Features
- Basic menu and order management
- Table management system
- QR code generation
- Staff request functionality

### Phase 2: Advanced Features
- Dynamic pricing engine
- Collaborative ordering
- Real-time notifications
- Customer feedback system

### Phase 3: Integration & Analytics
- POS system integration
- Advanced analytics dashboard
- Multi-restaurant support
- Performance optimization

## Security Features

### Row Level Security (RLS)
- Restaurant-specific data isolation
- User permission management
- Secure API endpoints
- Token-based authentication

### Data Protection
- Input validation and sanitization
- Secure image upload handling
- Rate limiting implementation
- CORS configuration

## Development Guidelines

### Code Standards
- TypeScript strict mode enabled
- ESLint + Prettier configuration
- Component-based architecture
- Service layer pattern for business logic

### Testing Strategy
- Jest unit tests for components
- Integration tests for services
- End-to-end testing with Playwright
- Mock data for development

### Deployment
- Vercel deployment for frontend
- Environment-specific configurations
- Build optimization and caching
- Health check endpoints

## Database Schema

### Core Tables
- `restaurants` - Restaurant information
- `menus` - Menu structure
- `menu_items` - Individual menu items
- `orders` - Order management
- `tables` - Table management
- `customer_feedback` - Feedback collection

### Pricing Tables
- `traffic_heatmap` - Traffic analytics
- `dynamic_pricing_config` - Pricing settings
- `pricing_history` - Historical pricing data

## API Integration

### Supabase Integration
- Real-time subscriptions for orders
- File upload for menu images
- Row Level Security policies
- Database functions for complex operations

### External APIs
- Stripe for payment processing
- Google Places for location data
- Image optimization services
- Notification delivery services

## Performance Optimization

### Frontend Optimizations
- Code splitting and lazy loading
- Image optimization and caching
- Bundle size optimization
- Performance monitoring

### Database Optimizations
- Query optimization with indexes
- Connection pooling
- Caching strategies
- Real-time subscription management

## Troubleshooting

### Common Issues
1. **Dynamic Pricing Not Working**
   - Check traffic data availability
   - Verify pricing configuration
   - Ensure real-time subscriptions are active

2. **Order Notifications Failing**
   - Verify WebSocket connections
   - Check notification service configuration
   - Confirm user permissions

3. **Image Upload Issues**
   - Check Supabase storage configuration
   - Verify file size limits
   - Confirm upload policies

### Development Setup
```bash
cd frontend/menu-flow-dynamo/
npm install
npm run dev  # Start development server
npm test     # Run tests
npm run build # Production build
```

## Future Enhancements

### Planned Features
- Advanced analytics dashboard
- Multi-language menu support
- Integration with additional POS systems
- Mobile app synchronization
- Advanced reporting capabilities

### Technical Improvements
- Performance optimization
- Enhanced error handling
- Improved accessibility
- Progressive Web App features

---

*This document consolidates information from multiple implementation guides and status reports. For specific technical details, refer to the codebase and inline documentation.*