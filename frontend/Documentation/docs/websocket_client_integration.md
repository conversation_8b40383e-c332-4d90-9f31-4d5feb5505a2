# WebSocket Client Integration Guide

## Overview
This guide provides examples and best practices for integrating with the SME Analytica WebSocket notification system. The notification system provides real-time updates for analysis results, market changes, and premium alerts.

## Connection Setup

### JavaScript/TypeScript Example
```typescript
class AnalyticaWebSocket {
  private ws: WebSocket;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private reconnectDelay: number = 1000; // Start with 1 second

  constructor(userId: string, apiKey: string) {
    this.connect(userId, apiKey);
  }

  private connect(userId: string, apiKey: string) {
    this.ws = new WebSocket(`wss://api.analytica.com/v1/notifications/ws/${userId}`);
    
    this.ws.onopen = () => {
      console.log('Connected to Analytica WebSocket');
      this.reconnectAttempts = 0;
      this.reconnectDelay = 1000;
      
      // Send authentication
      this.ws.send(JSON.stringify({
        type: 'auth',
        apiKey: apiKey
      }));
    };

    this.ws.onmessage = (event) => {
      const notification = JSON.parse(event.data);
      this.handleNotification(notification);
    };

    this.ws.onclose = () => {
      console.log('WebSocket connection closed');
      this.handleReconnection(userId, apiKey);
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
  }

  private handleReconnection(userId: string, apiKey: string) {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      setTimeout(() => {
        console.log(`Attempting to reconnect (${this.reconnectAttempts + 1}/${this.maxReconnectAttempts})`);
        this.reconnectAttempts++;
        this.reconnectDelay *= 2; // Exponential backoff
        this.connect(userId, apiKey);
      }, this.reconnectDelay);
    } else {
      console.error('Max reconnection attempts reached');
      this.handleConnectionFailure();
    }
  }

  private handleNotification(notification: any) {
    switch (notification.type) {
      case 'dynamic_pricing_complete':
        this.handlePricingUpdate(notification);
        break;
      case 'market_analysis_complete':
        this.handleMarketUpdate(notification);
        break;
      case 'sentiment_analysis_complete':
        this.handleSentimentUpdate(notification);
        break;
      case 'premium_alert':
        this.handlePremiumAlert(notification);
        break;
      default:
        console.log('Received notification:', notification);
    }
  }

  private handlePricingUpdate(notification: any) {
    // Handle dynamic pricing updates
    console.log('Pricing update:', notification.data);
  }

  private handleMarketUpdate(notification: any) {
    // Handle market analysis updates
    console.log('Market update:', notification.data);
  }

  private handleSentimentUpdate(notification: any) {
    // Handle sentiment analysis updates
    console.log('Sentiment update:', notification.data);
  }

  private handlePremiumAlert(notification: any) {
    // Handle premium alerts
    console.log('Premium alert:', notification.data);
  }

  private handleConnectionFailure() {
    // Implement fallback mechanism (e.g., polling API)
    console.log('Switching to fallback mechanism');
  }
}
```

### Python Client Example
```python
import asyncio
import websockets
import json
import logging
from datetime import datetime
from typing import Optional, Dict, Any

class AnalyticaWebSocketClient:
    def __init__(
        self,
        user_id: str,
        api_key: str,
        max_retries: int = 5,
        initial_delay: float = 1.0
    ):
        self.user_id = user_id
        self.api_key = api_key
        self.max_retries = max_retries
        self.initial_delay = initial_delay
        self.current_delay = initial_delay
        self.retry_count = 0
        self.websocket: Optional[websockets.WebSocketClientProtocol] = None
        self.logger = logging.getLogger(__name__)

    async def connect(self):
        """Establish WebSocket connection with retry logic."""
        while self.retry_count < self.max_retries:
            try:
                self.websocket = await websockets.connect(
                    f"wss://api.analytica.com/v1/notifications/ws/{self.user_id}"
                )
                
                # Send authentication
                await self.websocket.send(json.dumps({
                    "type": "auth",
                    "api_key": self.api_key
                }))
                
                self.logger.info("Connected to Analytica WebSocket")
                self.retry_count = 0
                self.current_delay = self.initial_delay
                return True
                
            except Exception as e:
                self.logger.error(f"Connection failed: {str(e)}")
                await self.handle_reconnection()

        self.logger.error("Max reconnection attempts reached")
        return False

    async def handle_reconnection(self):
        """Handle reconnection with exponential backoff."""
        self.retry_count += 1
        await asyncio.sleep(self.current_delay)
        self.current_delay *= 2  # Exponential backoff

    async def listen(self):
        """Listen for notifications."""
        while True:
            try:
                if not self.websocket:
                    if not await self.connect():
                        break
                
                async for message in self.websocket:
                    notification = json.loads(message)
                    await self.handle_notification(notification)
                    
            except websockets.ConnectionClosed:
                self.logger.warning("Connection closed, attempting to reconnect...")
                self.websocket = None
                await asyncio.sleep(1)
                
            except Exception as e:
                self.logger.error(f"Error in WebSocket connection: {str(e)}")
                self.websocket = None
                await asyncio.sleep(1)

    async def handle_notification(self, notification: Dict[str, Any]):
        """Handle different types of notifications."""
        handlers = {
            "dynamic_pricing_complete": self.handle_pricing_update,
            "market_analysis_complete": self.handle_market_update,
            "sentiment_analysis_complete": self.handle_sentiment_update,
            "premium_alert": self.handle_premium_alert
        }
        
        handler = handlers.get(notification["type"])
        if handler:
            await handler(notification)
        else:
            self.logger.info(f"Received notification: {notification}")

    async def handle_pricing_update(self, notification: Dict[str, Any]):
        """Handle dynamic pricing updates."""
        self.logger.info(f"Pricing update: {notification['data']}")
        # Implement your pricing update logic here

    async def handle_market_update(self, notification: Dict[str, Any]):
        """Handle market analysis updates."""
        self.logger.info(f"Market update: {notification['data']}")
        # Implement your market update logic here

    async def handle_sentiment_update(self, notification: Dict[str, Any]):
        """Handle sentiment analysis updates."""
        self.logger.info(f"Sentiment update: {notification['data']}")
        # Implement your sentiment update logic here

    async def handle_premium_alert(self, notification: Dict[str, Any]):
        """Handle premium alerts."""
        self.logger.info(f"Premium alert: {notification['data']}")
        # Implement your premium alert logic here

# Usage example
async def main():
    client = AnalyticaWebSocketClient(
        user_id="your_user_id",
        api_key="your_api_key"
    )
    await client.listen()

if __name__ == "__main__":
    asyncio.run(main())
```

## Error Handling and Reconnection Strategy

### Best Practices
1. **Exponential Backoff**: Implement exponential backoff for reconnection attempts to avoid overwhelming the server.
2. **Connection Monitoring**: Keep track of connection state and automatically reconnect when disconnected.
3. **Message Queue**: Buffer messages during disconnection periods and resend them after reconnecting.
4. **Heartbeat**: Implement a heartbeat mechanism to detect connection issues early.

### Error Response Examples
```json
{
  "type": "error",
  "code": "connection_error",
  "message": "WebSocket connection lost",
  "timestamp": "2025-02-15T09:54:49+01:00"
}

{
  "type": "error",
  "code": "auth_error",
  "message": "Invalid authentication credentials",
  "timestamp": "2025-02-15T09:54:49+01:00"
}

{
  "type": "error",
  "code": "subscription_error",
  "message": "Premium subscription required for this notification type",
  "timestamp": "2025-02-15T09:54:49+01:00"
}
```

## Notification Types and Payloads

### Analysis Completion Notifications
```json
{
  "type": "dynamic_pricing_complete",
  "message": "Dynamic pricing analysis completed",
  "data": {
    "result_summary": "Price optimization suggestions available",
    "recommendation": "Increase prices by 5% during peak hours",
    "confidence_score": 0.85
  },
  "timestamp": "2025-02-15T09:54:49+01:00"
}
```

### Premium Alerts
```json
{
  "type": "premium_market_alert",
  "message": "Significant market change detected",
  "data": {
    "change_type": "competitor_price_drop",
    "magnitude": "significant",
    "affected_products": ["product_1", "product_2"],
    "recommended_action": "Review pricing strategy"
  },
  "timestamp": "2025-02-15T09:54:49+01:00"
}
```

### Scheduled Notifications
```json
{
  "type": "scheduled_report",
  "message": "Daily analysis summary",
  "data": {
    "report_type": "daily_summary",
    "metrics": {
      "pricing_changes": 5,
      "market_alerts": 2,
      "sentiment_score": 0.75
    },
    "period": "2025-02-15"
  },
  "timestamp": "2025-02-15T09:54:49+01:00"
}
```

## Security Considerations

1. **Authentication**: Always use secure WebSocket connections (WSS) and include authentication tokens.
2. **Data Validation**: Validate all incoming messages before processing.
3. **Rate Limiting**: Implement rate limiting for reconnection attempts and message sending.
4. **Error Handling**: Handle errors gracefully and maintain security during error states.

## Mobile Integration Tips

1. **Battery Optimization**: Implement smart reconnection strategies to minimize battery usage.
2. **Network Changes**: Handle network type changes (WiFi/Cellular) gracefully.
3. **Background Mode**: Maintain connections in background mode for critical notifications.
4. **Push Fallback**: Use push notifications as a fallback when WebSocket connection is unavailable.
