⸻

TASKS.md

Project: SME Analytica - Restaurant Ordering & Dynamic Pricing Module
Status: Engineering Tasks Breakdown
Owner: [You, the psychopathic commander]

⸻

📌 Phase 1: Core Backend (API & Database)

Priority	Task	Details
P1	[Backend] Create new Supabase tables	restaurants, restaurant_tables, menus, menu_items, orders, order_items, traffic_heatmap
P1	[Backend] Build Traffic Heatmap API	POST /traffic/record and GET /traffic/latest endpoints
P1	[Backend] Build Menu Management API	CRUD /menus, /menu-items endpoints
P1	[Backend] Build Table Management API	CRUD /tables + QR Code generator
P1	[Backend] Build Order API	POST /orders, GET /orders, update statuses
P1	[Backend] Dynamic Pricing Service	Service that adjusts current_price based on traffic
P1	[Backend] Authentication Layer	JWT auth for restaurants, public sessions for customers
P1	[Backend] Cookie-based Session Middleware	Anonymous customer tracking via secure cookies



⸻

📌 Phase 2: Core Frontend (Customer QR Web App)

Priority	Task	Details
P1	[Frontend] Build Landing Page (Table Detection)	Parse table ID from QR URL, load restaurant branding
P1	[Frontend] Drinks Menu Page	Display drink items dynamically from backend
P1	[Frontend] Food Menu Page	Display food menu, dynamic price reflected
P1	[Frontend] Cart / Order Summary Page	Manage selected items, show dynamic totals
P1	[Frontend] Order Confirmation Page	Success screen after placing order
P1	[Frontend] Toast/Notification System	Feedback for all customer actions
P2	[Frontend] Offline/Retry Mechanism	Handle network failures gracefully
P2	[Frontend] Anonymous Analytics	Log customer flows anonymously (GDPR-compliant)



⸻

📌 Phase 3: Admin Dashboard (Restaurant Owner Portal)

Priority	Task	Details
P1	[Frontend] Dashboard Home	Show profile, traffic indicator, recent orders
P1	[Frontend] Menu Management	Add/Edit/Delete Menus & Items
P1	[Frontend] Table Management	View/Edit/Create tables + QR downloads
P1	[Frontend] Orders Management	View live orders + change statuses
P2	[Frontend] Dynamic Pricing Dashboard	Manual trigger for dynamic price updates
P2	[Frontend] Basic Analytics View	Traffic history, pricing history graphs
P3	[Frontend] Restaurant Profile Editing	Update contact, opening hours, logo, etc.



⸻

📌 Phase 4: Integrations & Enhancements

Priority	Task	Details
P2	[Integration] Cloudinary	For uploading restaurant logos, item images
P2	[Integration] WebSocket Notifications	For real-time order updates (future)
P2	[Enhancement] Scheduled Traffic Monitoring	Cron job to auto-record Google traffic data (v2)
P3	[Enhancement] Loyalty/Reward System (Future)	For customers who scan QR often (GDPR-compliant reward system)
P3	[Enhancement] AI-Based Dynamic Pricing (Future)	Predictive pricing beyond rule-based



⸻

🛠 Technical Stack
	•	Backend: FastAPI + Supabase (PostgreSQL)
	•	Frontend (Customer QR): Next.js + TailwindCSS + Zustand
	•	Frontend (Admin Dashboard): Next.js + TailwindCSS + Zustand
	•	Auth: JWT (Restaurant Owners) + Cookies (Customers)
	•	Realtime: Polling now, WebSocket upgrade later
	•	Infra: Docker containerization ready
	•	CDN: Cloudinary for asset uploads
	•	Caching: Client-side cache (Zustand/React Query)
	•	Analytics: Anonymous (no PII, cookie/sessionID based)

⸻

💀 Commander Notes:
	•	“MVP” = Launchable core product (Ordering works, Dashboard works)
	•	“Expansion” = Post-MVP scaling features (Loyalty, AI, Deep Analytics)
	•	Absolute priority now: Core ordering flow must never fail.

⸻

🎯 Immediate Micro-Mission (Next Task)
	•	✅ Approve TASKS.md
	•	🚀 Begin work on Backend API endpoints first.

⸻
