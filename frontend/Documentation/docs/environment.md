# Environment Setup Guide

## Overview
This document describes all environment variables required to run the application. Each component (API, WebSocket, Mobile) has its own set of required variables.

## Core Environment Variables

### API Configuration
```bash
# Server Configuration
API_HOST=0.0.0.0
API_PORT=8000
WS_PORT=8001

# Database Configuration
DATABASE_URL=postgresql://postgres:[password]@[host]:[port]/[database]
REDIS_URL=redis://[username]:[password]@[host]:[port]

# Authentication
JWT_SECRET=your-secure-jwt-secret
JWT_ALGORITHM=HS256
```

### Rate Limiting
```bash
# Rate Limits (requests per minute)
PREMIUM_RATE_LIMIT=100
STANDARD_RATE_LIMIT=10

# Analysis Tokens
PREMIUM_MAX_TOKENS=100
STANDARD_MAX_TOKENS=10
```

### Monitoring
```bash
# Error Tracking
SENTRY_DSN=https://[your-sentry-dsn]

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json
```

## Mobile App Environment

Create a `.env` file in the mobile directory:

```bash
# API Configuration
EXPO_PUBLIC_API_URL=http://production-alb-934338734.eu-west-1.elb.amazonaws.com=http://[api-host]:8000
EXPO_PUBLIC_WS_URL=ws://[ws-host]:8001

# Supabase
EXPO_PUBLIC_SUPABASE_URL=your-supabase-project-url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# Feature Flags
EXPO_PUBLIC_ENABLE_ANALYTICS=true
EXPO_PUBLIC_ENABLE_CRASH_REPORTING=true
```

## Development vs Production

### Development
- Use local PostgreSQL and Redis instances
- Set `LOG_LEVEL=DEBUG` for verbose logging
- Enable development features in mobile app

### Production
- Use managed database services
- Configure proper rate limits
- Enable error tracking and monitoring
- Use secure secrets and keys

## Environment File Templates

### Backend (.env.example)
```bash
# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
WS_PORT=8001

# Database
DATABASE_URL=
REDIS_URL=

# Authentication
JWT_SECRET=
JWT_ALGORITHM=HS256

# Rate Limiting
PREMIUM_RATE_LIMIT=100
STANDARD_RATE_LIMIT=10

# Analysis
PREMIUM_MAX_TOKENS=100
STANDARD_MAX_TOKENS=10

# Monitoring
SENTRY_DSN=
```

### Mobile (.env.example)
```bash
EXPO_PUBLIC_API_URL=http://production-alb-934338734.eu-west-1.elb.amazonaws.com=http://localhost:8000
EXPO_PUBLIC_WS_URL=ws://localhost:8001
EXPO_PUBLIC_SUPABASE_URL=
EXPO_PUBLIC_SUPABASE_ANON_KEY=
EXPO_PUBLIC_ENABLE_ANALYTICS=false
EXPO_PUBLIC_ENABLE_CRASH_REPORTING=false
```

## Security Considerations

1. Never commit real environment files
2. Use strong, unique secrets in production
3. Rotate sensitive keys regularly
4. Use different values for development and production
5. Restrict access to environment files

## Validation

The application validates environment variables on startup:
- Required variables must be present
- Values must match expected formats
- Sensitive values must meet security requirements

## Troubleshooting

### Common Issues

1. Database Connection
   - Verify DATABASE_URL format
   - Check network access
   - Confirm database credentials

2. Rate Limiting
   - Redis must be running
   - REDIS_URL must be valid
   - Rate limit values must be numbers

3. Authentication
   - JWT_SECRET must be set
   - JWT_ALGORITHM must be valid

### Environment Check

Run the environment validation script:
```bash
python -m app.core.config --validate
```

## Updates and Changes

When adding new environment variables:
1. Update both .env.example files
2. Document in this guide
3. Add to validation logic
4. Update deployment documentation
5. Notify team members
