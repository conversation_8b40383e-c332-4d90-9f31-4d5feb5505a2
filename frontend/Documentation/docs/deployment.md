# Deployment Guide

## Prerequisites
- <PERSON><PERSON> and Docker Compose
- Python 3.13+
- Node.js 18+
- Supabase account and project

## Environment Setup

1. Clone the repository and set up environment variables:
   ```bash
   cp .env.example .env
   ```
   See [environment.md](./environment.md) for detailed configuration options.

2. Configure Supabase:
   - Create a new project in Supabase
   - Run migrations:
     ```bash
     supabase db reset
     ```

## Docker Deployment

1. Build and start services:
   ```bash
   docker-compose up -d --build
   ```

2. Verify services:
   - API: http://localhost:8000/health
   - WebSocket: ws://localhost:8001
   - Supabase: Check connection in app logs

3. Scale services (optional):
   ```bash
   docker-compose up -d --scale api=3
   ```

## Manual Deployment

### Backend (Python FastAPI)

1. Create and activate virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # Unix
   # or
   .\venv\Scripts\activate  # Windows
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Start API server:
   ```bash
   uvicorn app.main:app --host 0.0.0.0 --port 8000
   ```

4. Start WebSocket server:
   ```bash
   python -m app.websocket_server
   ```

### Mobile App (React Native)

1. Install dependencies:
   ```bash
   cd mobile
   npm install
   ```

2. Start development server:
   ```bash
   npm start
   ```

3. Build for production:
   ```bash
   # iOS
   npm run build:ios
   
   # Android
   npm run build:android
   ```

## Health Checks and Monitoring

- API health check endpoint: `/health`
- WebSocket health check: `ws://host:8001/health`
- Monitoring via Sentry (configure SENTRY_DSN in environment)

## Load Balancing

The application supports horizontal scaling:
- API servers can be scaled using Docker Compose
- WebSocket connections require sticky sessions
- Redis handles rate limiting and session management

## Backup and Recovery

1. Database backups:
   ```bash
   # Automated daily backups
   supabase db dump -f backup.sql
   ```

2. Recovery procedure:
   ```bash
   supabase db reset
   supabase db push backup.sql
   ```

## Troubleshooting

See [troubleshooting.md](./troubleshooting.md) for common issues and solutions.

## Security Considerations

- All API endpoints require authentication
- Rate limiting is enforced based on subscription tier
- WebSocket connections are authenticated via JWT
- Environment variables are properly sanitized
- CORS is configured for allowed origins only

## Maintenance

1. Update dependencies regularly:
   ```bash
   # Backend
   pip install --upgrade -r requirements.txt
   
   # Mobile
   npm update
   ```

2. Monitor logs:
   ```bash
   docker-compose logs -f
   ```

3. Check system health:
   ```bash
   docker-compose ps
   docker-compose top
