#!/bin/bash

echo "🚀 Starting SME Analytica Documentation..."

# Kill any existing Next.js processes on common ports
echo "🔄 Checking for existing processes..."
lsof -ti:3000,3001,3002,3003 | xargs kill -9 2>/dev/null || true

# Clear Next.js cache
echo "🧹 Clearing cache..."
rm -rf .next

# Start development server
echo "▶️  Starting development server..."
npm run dev

echo ""
echo "🎉 Documentation should be available at:"
echo "   English: http://localhost:3000/en (or 3001 if 3000 is busy)"
echo "   Spanish: http://localhost:3000/es"
echo ""
echo "📄 Key pages to test:"
echo "   MenuFlow: http://localhost:3000/en/modules/menuflow"
echo "   SME App:  http://localhost:3000/en/modules/sme-app"
echo "   Platform: http://localhost:3000/en/platform"
echo "   AI & Automation: http://localhost:3000/en/ai-automation"
echo "   For Businesses: http://localhost:3000/en/for-businesses"
