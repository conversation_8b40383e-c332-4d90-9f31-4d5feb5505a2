# SME Analytica Documentation - Route Testing

## 🌐 Available Routes

### English Routes
- **Home**: http://localhost:3001/en
- **Platform**: http://localhost:3001/en/platform
- **Modules Overview**: http://localhost:3001/en/modules
- **MenuFlow**: http://localhost:3001/en/modules/menuflow
- **SME App**: http://localhost:3001/en/modules/sme-app
- **Integrations**: http://localhost:3001/en/integrations
- **About**: http://localhost:3001/en/about

### Spanish Routes
- **Home**: http://localhost:3001/es
- **Platform**: http://localhost:3001/es/platform
- **Modules Overview**: http://localhost:3001/es/modules
- **MenuFlow**: http://localhost:3001/es/modules/menuflow
- **SME App**: http://localhost:3001/es/modules/sme-app
- **Integrations**: http://localhost:3001/es/integrations
- **About**: http://localhost:3001/es/about

## 🎯 Key Features Documented

### MenuFlow (restaurants.smeanalytica.dev)
- ✅ Table-based dynamic pricing system
- ✅ React + TypeScript technology stack
- ✅ Real-time price adjustments based on occupancy
- ✅ QR menu integration
- ✅ POS middleware integration (Deliverect, Chift, GetOrder)
- ✅ Admin dashboard for table management
- ✅ Live deployment information

### SME App (TestFlight: https://testflight.apple.com/join/kCjhqR4Q)
- ✅ React Native + Expo mobile app
- ✅ AI-powered business analytics
- ✅ Multiple analysis types (market, competitor, sentiment, etc.)
- ✅ Multi-language support (EN, ES, FR)
- ✅ Profile management with Cloudinary image upload
- ✅ Subscription management (Premium: 50 analyses/month)
- ✅ Supabase authentication and database
- ✅ TestFlight beta testing information

## 🔗 External Links
- **MenuFlow Live Demo**: https://restaurants.smeanalytica.dev
- **SME App TestFlight**: https://testflight.apple.com/join/kCjhqR4Q
- **API Documentation**: https://api.smeanalytica.dev/docs
- **Main Platform**: https://smeanalytica.dev

## 📱 Mobile App Details
- **Bundle ID**: com.olayinka.analytica (needs to be changed)
- **Platform**: iOS 15+ (TestFlight), Android (planned)
- **Languages**: English, Spanish, French
- **Backend**: api.smeanalytica.dev
- **Database**: Supabase
- **Image Storage**: Cloudinary

## 🍽️ MenuFlow Details
- **Technology**: React, TypeScript, Supabase
- **Deployment**: restaurants.smeanalytica.dev
- **Features**: Dynamic pricing, QR menus, table management
- **Target**: Restaurants, cafes, food service businesses
- **Integration**: POS systems via middleware

## ✅ Testing Checklist
- [ ] All routes load without 404 errors
- [ ] Language switching works correctly
- [ ] External links open in new tabs
- [ ] Mobile responsive design
- [ ] Dark/light theme switching
- [ ] Navigation menu functionality
- [ ] Footer links work correctly
