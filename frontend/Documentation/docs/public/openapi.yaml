openapi: 3.0.3
info:
  title: SME Analytica API
  description: |
    AI-powered business intelligence platform API for small and medium enterprises.
    
    ## Overview
    SME Analytica provides comprehensive AI analysis including:
    - **Dynamic Pricing Optimization**: AI-powered pricing strategies
    - **Market Analysis**: Trend analysis and market insights
    - **Sentiment Analysis**: Customer sentiment from reviews and feedback
    - **Growth Forecasting**: Business growth predictions and recommendations
    - **Competitor Analysis**: Competitive landscape insights
    
    ## AI Models
    Our platform uses advanced AI models including:
    - Claude Opus 4 for complex business analysis
    - GPT-4.1 for dynamic pricing optimization
    - Gemini 2.0 Flash for market trend analysis
    - DeepSeek R1 for sentiment analysis
    
    ## Authentication
    All endpoints require JWT authentication via the Authorization header:
    ```
    Authorization: Bearer your-jwt-token
    ```
    
    ## Rate Limiting
    API access is rate-limited based on subscription tier:
    - **Basic**: 12 analyses per month
    - **Premium**: 50 analyses per month  
    - **Enterprise**: Unlimited analyses
    
    ## Support
    - Documentation: https://docs.smeanalytica.dev
    - Support: <EMAIL>
    - Status: https://status.smeanalytica.dev
  version: 2.0.0
  contact:
    name: SME Analytica Support
    email: <EMAIL>
    url: https://smeanalytica.dev/contact
  license:
    name: Proprietary
    url: https://smeanalytica.dev/terms
  termsOfService: https://smeanalytica.dev/terms

servers:
  - url: https://api.smeanalytica.dev/api/v1
    description: Production server
  - url: https://staging-api.smeanalytica.dev/api/v1
    description: Staging server

security:
  - BearerAuth: []

paths:
  /analysis/{analysis_type}:
    post:
      summary: Submit AI Analysis Request
      description: |
        Submit a new AI analysis request for your business. The analysis will be processed
        asynchronously and results will be available via WebSocket or polling.
        
        **Analysis Types:**
        - `pricing`: Dynamic pricing optimization with competitor analysis
        - `market`: Market trend analysis and opportunities
        - `sentiment`: Customer sentiment analysis from reviews
        - `growth`: Growth forecasting and recommendations
        - `competitor`: Competitive landscape analysis
        - `forecast`: Business performance forecasting
      operationId: submitAnalysis
      tags:
        - Analysis
      parameters:
        - name: analysis_type
          in: path
          required: true
          schema:
            type: string
            enum: [pricing, market, sentiment, growth, competitor, forecast]
          description: Type of AI analysis to perform
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AnalysisRequest'
            examples:
              pricing_analysis:
                summary: Dynamic Pricing Analysis
                value:
                  business_name: "Mario's Italian Restaurant"
                  location:
                    latitude: 37.7749
                    longitude: -122.4194
                    address: "123 Market St, San Francisco, CA"
                  business_type: "restaurant"
                  timeframe: "3m"
                  additional_notes: "Focus on weekend pricing strategy"
                  pricing_constraints:
                    min_price: 15.00
                    max_price: 45.00
                    competitor_radius_km: 3
              market_analysis:
                summary: Market Analysis
                value:
                  business_name: "TechStart Solutions"
                  location:
                    latitude: 40.7128
                    longitude: -74.0060
                    address: "456 Broadway, New York, NY"
                  business_type: "technology"
                  timeframe: "6m"
                  additional_notes: "Expanding into new markets"
      responses:
        '202':
          description: Analysis request accepted and queued for processing
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnalysisResponse'
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '429':
          description: Rate limit exceeded
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /analysis/{analysis_id}:
    get:
      summary: Get Analysis Results
      description: Retrieve the results of a completed analysis by ID
      operationId: getAnalysisResults
      tags:
        - Analysis
      parameters:
        - name: analysis_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: Unique identifier for the analysis
      responses:
        '200':
          description: Analysis results retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnalysisResult'
        '404':
          description: Analysis not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '202':
          description: Analysis still processing
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnalysisStatus'

  /auth/signup:
    post:
      summary: Register New User
      description: Create a new user account with email and password
      operationId: signUp
      tags:
        - Authentication
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SignUpRequest'
      responses:
        '201':
          description: User created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '400':
          description: Invalid request data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '409':
          description: User already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/signin:
    post:
      summary: Authenticate User
      description: Sign in with email and password to receive JWT token
      operationId: signIn
      tags:
        - Authentication
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SignInRequest'
      responses:
        '200':
          description: Authentication successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '401':
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /user/profile:
    get:
      summary: Get User Profile
      description: Retrieve current user's profile information
      operationId: getUserProfile
      tags:
        - User
      responses:
        '200':
          description: User profile retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'

  /user/subscription:
    get:
      summary: Get Subscription Status
      description: Retrieve current user's subscription information and usage
      operationId: getSubscription
      tags:
        - User
      responses:
        '200':
          description: Subscription information retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Subscription'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    AnalysisRequest:
      type: object
      required:
        - business_name
        - location
        - business_type
      properties:
        business_name:
          type: string
          description: Name of the business to analyze
          example: "Mario's Italian Restaurant"
        location:
          $ref: '#/components/schemas/Location'
        business_type:
          type: string
          description: Type of business
          enum: [restaurant, retail, ecommerce, service, technology, healthcare, fitness]
          example: "restaurant"
        timeframe:
          type: string
          description: Analysis timeframe
          enum: ["1m", "3m", "6m", "1y"]
          default: "3m"
        additional_notes:
          type: string
          description: Additional context or specific requirements
          example: "Focus on weekend pricing strategy"
        pricing_constraints:
          $ref: '#/components/schemas/PricingConstraints'

    Location:
      type: object
      required:
        - latitude
        - longitude
        - address
      properties:
        latitude:
          type: number
          format: double
          minimum: -90
          maximum: 90
          example: 37.7749
        longitude:
          type: number
          format: double
          minimum: -180
          maximum: 180
          example: -122.4194
        address:
          type: string
          description: Full business address
          example: "123 Market St, San Francisco, CA 94103"

    PricingConstraints:
      type: object
      properties:
        min_price:
          type: number
          format: double
          minimum: 0
          description: Minimum acceptable price
          example: 15.00
        max_price:
          type: number
          format: double
          minimum: 0
          description: Maximum acceptable price
          example: 45.00
        competitor_radius_km:
          type: number
          format: double
          minimum: 0.5
          maximum: 50
          description: Radius for competitor analysis in kilometers
          default: 5
          example: 3

    AnalysisResponse:
      type: object
      properties:
        analysis_id:
          type: string
          format: uuid
          description: Unique identifier for the analysis
        status:
          type: string
          enum: [queued, processing, completed, failed]
          description: Current status of the analysis
        estimated_completion:
          type: string
          format: date-time
          description: Estimated completion time
        type:
          type: string
          description: Type of analysis requested

    AnalysisResult:
      type: object
      properties:
        analysis_id:
          type: string
          format: uuid
        status:
          type: string
          enum: [completed]
        type:
          type: string
        results:
          oneOf:
            - $ref: '#/components/schemas/PricingAnalysisResult'
            - $ref: '#/components/schemas/MarketAnalysisResult'
            - $ref: '#/components/schemas/SentimentAnalysisResult'
        created_at:
          type: string
          format: date-time
        completed_at:
          type: string
          format: date-time

    PricingAnalysisResult:
      type: object
      properties:
        suggested_price:
          type: number
          format: double
          description: AI-recommended optimal price
        confidence_score:
          type: number
          format: double
          minimum: 0
          maximum: 1
          description: Confidence level of the recommendation
        factors:
          type: object
          description: Factors influencing the pricing decision
        insights:
          type: array
          items:
            type: string
          description: Key insights and recommendations

    MarketAnalysisResult:
      type: object
      properties:
        market_size:
          type: number
          format: double
          description: Estimated market size
        growth_rate:
          type: number
          format: double
          description: Market growth rate percentage
        opportunities:
          type: array
          items:
            type: string
          description: Identified market opportunities
        threats:
          type: array
          items:
            type: string
          description: Potential market threats

    SentimentAnalysisResult:
      type: object
      properties:
        overall_sentiment:
          type: string
          enum: [positive, neutral, negative]
        sentiment_score:
          type: number
          format: double
          minimum: -1
          maximum: 1
        key_themes:
          type: array
          items:
            type: string
        recommendations:
          type: array
          items:
            type: string

    AnalysisStatus:
      type: object
      properties:
        analysis_id:
          type: string
          format: uuid
        status:
          type: string
          enum: [queued, processing]
        progress:
          type: number
          format: double
          minimum: 0
          maximum: 100
        estimated_completion:
          type: string
          format: date-time

    SignUpRequest:
      type: object
      required:
        - email
        - password
        - name
      properties:
        email:
          type: string
          format: email
          example: "<EMAIL>"
        password:
          type: string
          minLength: 8
          example: "secure-password"
        name:
          type: string
          example: "John Doe"

    SignInRequest:
      type: object
      required:
        - email
        - password
      properties:
        email:
          type: string
          format: email
          example: "<EMAIL>"
        password:
          type: string
          example: "secure-password"

    AuthResponse:
      type: object
      properties:
        user:
          $ref: '#/components/schemas/User'
        token:
          type: string
          description: JWT authentication token
        expires_at:
          type: string
          format: date-time

    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
        email:
          type: string
          format: email
        name:
          type: string
        created_at:
          type: string
          format: date-time

    UserProfile:
      allOf:
        - $ref: '#/components/schemas/User'
        - type: object
          properties:
            subscription:
              $ref: '#/components/schemas/Subscription'
            usage:
              $ref: '#/components/schemas/Usage'

    Subscription:
      type: object
      properties:
        tier:
          type: string
          enum: [basic, premium, enterprise]
        status:
          type: string
          enum: [active, cancelled, expired]
        current_period_start:
          type: string
          format: date-time
        current_period_end:
          type: string
          format: date-time
        cancel_at_period_end:
          type: boolean

    Usage:
      type: object
      properties:
        analyses_used:
          type: integer
          description: Number of analyses used this period
        analyses_limit:
          type: integer
          description: Maximum analyses allowed this period
        reset_date:
          type: string
          format: date-time
          description: When usage counters reset

    Error:
      type: object
      properties:
        error:
          type: string
          description: Error type
        message:
          type: string
          description: Human-readable error message
        details:
          type: object
          description: Additional error details

tags:
  - name: Analysis
    description: AI-powered business analysis endpoints
  - name: Authentication
    description: User authentication and authorization
  - name: User
    description: User profile and subscription management

externalDocs:
  description: Full API Documentation
  url: https://docs.smeanalytica.dev
