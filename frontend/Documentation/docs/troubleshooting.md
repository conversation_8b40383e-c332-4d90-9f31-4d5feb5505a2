# Troubleshooting Guide

## Common Issues and Solutions

### API Server Issues

#### Server Won't Start
```
Error: Address already in use
```
**Solution:**
1. Check if another process is using the port:
   ```bash
   lsof -i :8000  # For API port
   lsof -i :8001  # For WebSocket port
   ```
2. Kill the process or change the port in .env

#### Database Connection Errors
```
Error: Could not connect to database
```
**Solution:**
1. Verify DATABASE_URL in .env
2. Check if database is running:
   ```bash
   docker-compose ps
   ```
3. Try connecting directly:
   ```bash
   psql $DATABASE_URL
   ```

### WebSocket Issues

#### Connection Failures
```
WebSocket connection failed
```
**Solution:**
1. Verify WS_PORT in .env
2. Check WebSocket server logs:
   ```bash
   docker-compose logs websocket
   ```
3. Ensure client URL matches server:
   ```javascript
   // Should match WS_PORT
   ws://localhost:8001
   ```

#### Authentication Errors
```
WebSocket: Unauthorized
```
**Solution:**
1. Check JWT token in request
2. Verify JWT_SECRET matches between services
3. Ensure token hasn't expired

### Mobile App Issues

#### API Connection Errors
```
Failed to fetch from API
```
**Solution:**
1. Check EXPO_PUBLIC_API_URL=http://production-alb-934338734.eu-west-1.elb.amazonaws.com in mobile/.env
2. Verify network connectivity
3. Check API server status
4. Review API client logs

#### Build Failures
```
Build failed with exit code 1
```
**Solution:**
1. Clear build cache:
   ```bash
   cd mobile
   npm clean-install
   ```
2. Update dependencies:
   ```bash
   npm update
   ```
3. Check build logs for specific errors

### Rate Limiting Issues

#### Too Many Requests
```
Error: 429 Too Many Requests
```
**Solution:**
1. Check current rate limits:
   ```bash
   curl -I http://localhost:8000/api/v1/rate-limits
   ```
2. Verify Redis connection
3. Adjust limits in .env if needed

### Performance Issues

#### Slow API Responses
**Solution:**
1. Check database query performance:
   ```sql
   SELECT * FROM pg_stat_activity;
   ```
2. Monitor system resources:
   ```bash
   docker stats
   ```
3. Review slow query logs

#### Memory Issues
```
Container exited: Out of memory
```
**Solution:**
1. Check container limits:
   ```bash
   docker inspect [container_id]
   ```
2. Adjust memory limits in docker-compose.yml
3. Monitor memory usage:
   ```bash
   docker stats
   ```

### Authentication Issues

#### Login Failures
**Solution:**
1. Verify Supabase configuration
2. Check auth logs
3. Ensure correct credentials
4. Verify JWT configuration

### Monitoring and Logging

#### Missing Logs
**Solution:**
1. Check LOG_LEVEL in .env
2. Verify log configuration:
   ```python
   # app/core/logging.py
   ```
3. Ensure log directory is writable

#### Sentry Issues
**Solution:**
1. Verify SENTRY_DSN in .env
2. Check Sentry dashboard for errors
3. Test error reporting:
   ```python
   capture_exception(error)
   ```

## Debugging Tools

### API Debugging
```bash
# Enable debug mode
LOG_LEVEL=DEBUG

# Test endpoints
curl -v http://localhost:8000/health

# Monitor requests
tail -f logs/api.log
```

### WebSocket Debugging
```bash
# Test connection
wscat -c ws://localhost:8001

# Monitor WebSocket traffic
tcpdump -i lo0 port 8001
```

### Database Debugging
```bash
# Connect to database
psql $DATABASE_URL

# Show active connections
SELECT * FROM pg_stat_activity;

# Kill hanging queries
SELECT pg_terminate_backend(pid);
```

### Mobile App Debugging
```bash
# Start with debug mode
npm start --debug

# Clear cache
npm start -- -c

# Check build info
expo diagnostics
```

## Health Checks

### API Health
```bash
curl http://localhost:8000/health
```

### WebSocket Health
```bash
wscat -c ws://localhost:8001/health
```

### Database Health
```bash
pg_isready -U postgres -h localhost
```

## Recovery Procedures

### Database Recovery
1. Stop services:
   ```bash
   docker-compose down
   ```
2. Restore backup:
   ```bash
   supabase db reset
   ```
3. Restart services:
   ```bash
   docker-compose up -d
   ```

### Application Recovery
1. Clear all containers:
   ```bash
   docker-compose down -v
   ```
2. Rebuild:
   ```bash
   docker-compose up -d --build
   ```
3. Verify health checks

## Getting Help

1. Check application logs:
   ```bash
   docker-compose logs --tail=100
   ```

2. Review error tracking:
   - Check Sentry dashboard
   - Review application logs
   - Monitor system metrics

3. Contact support:
   - Include relevant logs
   - Describe steps to reproduce
   - Share environment details
