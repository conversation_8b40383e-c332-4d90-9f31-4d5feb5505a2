# Analytica API Reference

This document provides detailed information about the Analytica API endpoints.

## Base URL

```
http://localhost:8000/api
```

## Authentication

Most endpoints require authentication using a JWT token. Include the token in the Authorization header:

```
Authorization: Bearer <your_token>
```

## Endpoints

### Authentication

#### Login
- **POST** `/auth/login`
- **Description**: Authenticate user and get access token
- **Request Body**:
  ```json
  {
    "email": "string",
    "password": "string"
  }
  ```
- **Response**:
  ```json
  {
    "id": "string",
    "email": "string",
    "full_name": "string",
    "created_at": "datetime",
    "access_token": "string"
  }
  ```

#### Register
- **POST** `/auth/register`
- **Description**: Register a new user
- **Request Body**:
  ```json
  {
    "email": "string",
    "password": "string",
    "full_name": "string"
  }
  ```
- **Response**: Same as login response

#### Refresh Token
- **POST** `/auth/refresh-token`
- **Description**: Get a new access token
- **Response**:
  ```json
  {
    "access_token": "string",
    "token_type": "bearer"
  }
  ```

### Profile

#### Get Profile
- **GET** `/profile/me`
- **Description**: Get current user profile
- **Authentication**: Required
- **Response**:
  ```json
  {
    "id": "string",
    "email": "string",
    "full_name": "string",
    "business_name": "string",
    "business_type": "string",
    "location": "string",
    "created_at": "datetime",
    "updated_at": "datetime"
  }
  ```

#### Update Profile
- **PUT** `/profile/me`
- **Description**: Update user profile
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "full_name": "string",
    "business_name": "string",
    "business_type": "string",
    "location": "string"
  }
  ```
- **Response**: Same as Get Profile response

### Analysis

#### Create Analysis
- **POST** `/analysis/new`
- **Description**: Create a new analysis request
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "type": "string",
    "data": {}
  }
  ```
- **Supported Analysis Types**:
  - `dynamic_pricing`
  - `sentiment`
  - `market`
  - `competitor`
  - `sales_forecasting`
- **Response**:
  ```json
  {
    "id": "string",
    "type": "string",
    "status": "string",
    "result": "object",
    "created_at": "datetime",
    "updated_at": "datetime"
  }
  ```

#### Get Analysis History
- **GET** `/analysis/history`
- **Description**: Get list of previous analyses
- **Authentication**: Required
- **Response**: Array of analysis objects

#### Get Specific Analysis
- **GET** `/analysis/{analysis_id}`
- **Description**: Get details of a specific analysis
- **Authentication**: Required
- **Response**: Analysis object

## Error Responses

All endpoints may return the following error responses:

- **401 Unauthorized**: Invalid or missing authentication token
- **400 Bad Request**: Invalid request parameters
- **404 Not Found**: Requested resource not found
- **500 Internal Server Error**: Server-side error

Error response format:
```json
{
  "detail": "Error message"
}
``` 