# Authentication API Reference

## Overview
The authentication system uses JWT tokens and integrates with Supabase for user management. All authenticated endpoints require a valid JWT token in the Authorization header.

## Endpoints

### Sign Up
```http
POST /api/v1/auth/signup
```

Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "secure-password",
  "name": "<PERSON>"
}
```

**Response (201):**
```json
{
  "user": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "name": "<PERSON>"
  },
  "token": "jwt-token"
}
```

### Sign In
```http
POST /api/v1/auth/signin
```

Authenticate an existing user.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "secure-password"
}
```

**Response (200):**
```json
{
  "user": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "name": "<PERSON>",
    "subscription_tier": "standard"
  },
  "token": "jwt-token"
}
```

### Refresh Token
```http
POST /api/v1/auth/refresh
```

Get a new access token using a refresh token.

**Request Headers:**
```
Authorization: Bearer refresh-token
```

**Response (200):**
```json
{
  "token": "new-jwt-token",
  "expires_in": 3600
}
```

### Sign Out
```http
POST /api/v1/auth/signout
```

Invalidate the current session.

**Request Headers:**
```
Authorization: Bearer jwt-token
```

**Response (200):**
```json
{
  "message": "Successfully signed out"
}
```

### Password Reset Request
```http
POST /api/v1/auth/password/reset
```

Request a password reset email.

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response (200):**
```json
{
  "message": "Password reset instructions sent"
}
```

### Password Reset Confirmation
```http
POST /api/v1/auth/password/reset/confirm
```

Reset password using token from email.

**Request Body:**
```json
{
  "token": "reset-token",
  "new_password": "new-secure-password"
}
```

**Response (200):**
```json
{
  "message": "Password successfully reset"
}
```

## Error Responses

### Invalid Credentials (401)
```json
{
  "error": "invalid_credentials",
  "message": "Invalid email or password"
}
```

### Token Expired (401)
```json
{
  "error": "token_expired",
  "message": "Authentication token has expired"
}
```

### Invalid Token (401)
```json
{
  "error": "invalid_token",
  "message": "Invalid authentication token"
}
```

### Email Already Exists (409)
```json
{
  "error": "email_exists",
  "message": "Email address already registered"
}
```

## Authentication Flow

1. **Registration:**
   - Client calls `/auth/signup` with email and password
   - Server creates user and returns JWT token
   - Client stores token for subsequent requests

2. **Login:**
   - Client calls `/auth/signin` with credentials
   - Server validates and returns JWT token
   - Client stores token for subsequent requests

3. **Using Protected Endpoints:**
   - Client includes token in Authorization header
   - Server validates token for each request
   - Server checks user permissions and rate limits

4. **Token Refresh:**
   - Client detects expired token
   - Client calls `/auth/refresh` with refresh token
   - Server issues new access token

5. **Logout:**
   - Client calls `/auth/signout`
   - Server invalidates token
   - Client removes stored tokens

## Security Considerations

1. **Password Requirements:**
   - Minimum 8 characters
   - At least one uppercase letter
   - At least one number
   - At least one special character

2. **Rate Limiting:**
   - Login attempts: 5 per minute
   - Password reset requests: 3 per hour
   - Token refresh: 10 per minute

3. **Token Configuration:**
   - Access tokens expire in 1 hour
   - Refresh tokens expire in 7 days
   - Tokens are invalidated on password change

4. **CORS Policy:**
   - Only allowed origins can access endpoints
   - Credentials must be included
   - Preflight requests are handled

## WebSocket Authentication

WebSocket connections require authentication using the same JWT tokens:

```javascript
const ws = new WebSocket('ws://api.example.com', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

## Example Usage

### JavaScript/TypeScript
```typescript
async function login(email: string, password: string) {
  const response = await fetch('/api/v1/auth/signin', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ email, password })
  });
  
  if (!response.ok) {
    throw new Error('Authentication failed');
  }
  
  const { token } = await response.json();
  return token;
}
```

### Python
```python
import requests

def login(email: str, password: str) -> str:
    response = requests.post(
        '/api/v1/auth/signin',
        json={'email': email, 'password': password}
    )
    response.raise_for_status()
    return response.json()['token']
