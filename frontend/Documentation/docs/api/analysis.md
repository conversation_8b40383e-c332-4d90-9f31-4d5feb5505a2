# Analysis API Reference

## Overview
The Analysis API provides endpoints for performing various types of business analysis including pricing, market analysis, sentiment analysis, and growth forecasting. Access to these endpoints is rate-limited based on the user's subscription tier.

## Authentication
All endpoints require a valid JWT token in the Authorization header:
```
Authorization: Bearer your-jwt-token
```

## Endpoints

### Submit Analysis
```http
POST /api/v1/analysis/{analysis_type}
```

Submit a new analysis request.

**Analysis Types:**
- `pricing`: Dynamic pricing analysis
- `market`: Market analysis
- `sentiment`: Customer sentiment analysis
- `growth`: Growth potential analysis
- `competitor`: Competitor analysis
- `forecast`: Business forecasting

**Request Body:**
```json
{
  "business_name": "Example Business",
  "location": {
    "latitude": 37.7749,
    "longitude": -122.4194,
    "address": "123 Market St, San Francisco, CA"
  },
  "business_type": "restaurant",
  "timeframe": "3m",
  "additional_notes": "Focus on weekend pricing",
  "pricing_constraints": {
    "min_price": 10.00,
    "max_price": 50.00,
    "competitor_radius_km": 5
  }
}
```

**Response (202):**
```json
{
  "analysis_id": "analysis-uuid",
  "status": "processing",
  "estimated_completion": "2025-02-18T02:15:00Z",
  "type": "pricing"
}
```

### Get Analysis Results
```http
GET /api/v1/analysis/{analysis_id}
```

Retrieve results of a submitted analysis.

**Response (200):**
```json
{
  "analysis_id": "analysis-uuid",
  "status": "completed",
  "type": "pricing",
  "results": {
    "recommendations": [
      {
        "item": "Product A",
        "current_price": 25.00,
        "recommended_price": 29.99,
        "confidence": 0.85
      }
    ],
    "market_insights": {
      "average_competitor_price": 27.50,
      "price_elasticity": -1.2,
      "peak_hours": ["18:00", "19:00", "20:00"]
    },
    "forecast": {
      "revenue_impact": "+15%",
      "customer_retention": "High"
    }
  },
  "metadata": {
    "created_at": "2025-02-18T01:00:00Z",
    "completed_at": "2025-02-18T01:05:00Z",
    "data_freshness": "5m"
  }
}
```

### List Analyses
```http
GET /api/v1/analyses
```

List all analyses for the authenticated user.

**Query Parameters:**
- `type`: Filter by analysis type
- `status`: Filter by status (processing, completed, failed)
- `from`: Start date (ISO 8601)
- `to`: End date (ISO 8601)
- `limit`: Maximum number of results (default: 20)
- `offset`: Pagination offset (default: 0)

**Response (200):**
```json
{
  "analyses": [
    {
      "analysis_id": "analysis-uuid-1",
      "type": "pricing",
      "status": "completed",
      "created_at": "2025-02-18T01:00:00Z"
    },
    {
      "analysis_id": "analysis-uuid-2",
      "type": "market",
      "status": "processing",
      "created_at": "2025-02-18T01:10:00Z"
    }
  ],
  "pagination": {
    "total": 45,
    "limit": 20,
    "offset": 0
  }
}
```

### Cancel Analysis
```http
POST /api/v1/analysis/{analysis_id}/cancel
```

Cancel a processing analysis.

**Response (200):**
```json
{
  "message": "Analysis cancelled successfully",
  "analysis_id": "analysis-uuid"
}
```

## Rate Limits

Rate limits vary by subscription tier:

### Standard Tier
- 10 requests per minute
- 100 analyses per month
- Basic data refresh (24h)

### Premium Tier
- 100 requests per minute
- Unlimited analyses
- Real-time data refresh (5m)

## Error Responses

### Rate Limit Exceeded (429)
```json
{
  "error": "rate_limit_exceeded",
  "message": "Rate limit exceeded. Please upgrade your plan or try again later.",
  "reset_at": "2025-02-18T01:15:00Z"
}
```

### Invalid Analysis Type (400)
```json
{
  "error": "invalid_analysis_type",
  "message": "Unsupported analysis type. Valid types are: pricing, market, sentiment, growth, competitor, forecast"
}
```

### Analysis Not Found (404)
```json
{
  "error": "not_found",
  "message": "Analysis with ID 'analysis-uuid' not found"
}
```

### Insufficient Credits (402)
```json
{
  "error": "insufficient_credits",
  "message": "No remaining analysis credits. Please upgrade your subscription."
}
```

## WebSocket Updates

Connect to the WebSocket endpoint to receive real-time updates:

```javascript
const ws = new WebSocket('ws://api.example.com/analysis/updates');

ws.onmessage = (event) => {
  const update = JSON.parse(event.data);
  console.log(`Analysis ${update.analysis_id} status: ${update.status}`);
};
```

## Example Usage

### TypeScript
```typescript
async function submitAnalysis(businessData: BusinessData) {
  const response = await fetch('/api/v1/analysis/pricing', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(businessData)
  });
  
  if (!response.ok) {
    throw new Error('Analysis submission failed');
  }
  
  return response.json();
}
```

### Python
```python
import requests

def submit_analysis(token: str, business_data: dict) -> dict:
    response = requests.post(
        '/api/v1/analysis/pricing',
        headers={'Authorization': f'Bearer {token}'},
        json=business_data
    )
    response.raise_for_status()
    return response.json()
```

## Best Practices

1. **Polling Interval:**
   - Standard tier: Poll every 60 seconds
   - Premium tier: Use WebSocket connection
   
2. **Error Handling:**
   - Implement exponential backoff for rate limits
   - Cache analysis results when possible
   - Handle WebSocket reconnection gracefully

3. **Data Freshness:**
   - Check metadata.data_freshness for result age
   - Re-run analysis if data is stale
   - Subscribe to WebSocket updates for real-time data

4. **Resource Management:**
   - Cancel unused analyses
   - Clean up WebSocket connections
   - Monitor API usage against limits
