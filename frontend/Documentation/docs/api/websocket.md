# WebSocket API Reference

## Overview
The WebSocket API provides real-time updates for analysis results, notifications, and system status. It uses a standard WebSocket protocol with JWT authentication.

## Connection

### Endpoint
```
ws://api.example.com:8001/ws
```

### Authentication
Include the JW<PERSON> token in the connection request:
```javascript
const ws = new WebSocket('ws://api.example.com:8001/ws', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

## Message Format

All messages follow a standard format:

```json
{
  "type": "message_type",
  "payload": {},
  "id": "unique-message-id",
  "timestamp": "2025-02-18T01:00:00Z"
}
```

### Message Types

#### Server → Client

1. **Analysis Updates**
```json
{
  "type": "analysis_update",
  "payload": {
    "analysis_id": "analysis-uuid",
    "status": "processing",
    "progress": 45,
    "estimated_completion": "2025-02-18T01:15:00Z"
  }
}
```

2. **Analysis Completion**
```json
{
  "type": "analysis_complete",
  "payload": {
    "analysis_id": "analysis-uuid",
    "results": {
      "recommendations": [],
      "market_insights": {},
      "forecast": {}
    }
  }
}
```

3. **Rate Limit Updates**
```json
{
  "type": "rate_limit_update",
  "payload": {
    "remaining": 85,
    "reset_at": "2025-02-18T02:00:00Z",
    "limit": 100
  }
}
```

4. **System Notifications**
```json
{
  "type": "notification",
  "payload": {
    "level": "info",
    "message": "New feature available: Competitor Analysis",
    "action_url": "/features/competitor-analysis"
  }
}
```

#### Client → Server

1. **Subscribe to Analysis**
```json
{
  "type": "subscribe_analysis",
  "payload": {
    "analysis_id": "analysis-uuid"
  }
}
```

2. **Unsubscribe from Analysis**
```json
{
  "type": "unsubscribe_analysis",
  "payload": {
    "analysis_id": "analysis-uuid"
  }
}
```

3. **Heartbeat**
```json
{
  "type": "ping",
  "payload": {
    "timestamp": "2025-02-18T01:00:00Z"
  }
}
```

## Connection Lifecycle

### 1. Initial Connection
```javascript
const ws = new WebSocket('ws://api.example.com:8001/ws', {
  headers: { 'Authorization': `Bearer ${token}` }
});

ws.onopen = () => {
  console.log('Connected to WebSocket');
};
```

### 2. Subscribe to Updates
```javascript
ws.send(JSON.stringify({
  type: 'subscribe_analysis',
  payload: { analysis_id: 'analysis-uuid' }
}));
```

### 3. Handle Messages
```javascript
ws.onmessage = (event) => {
  const message = JSON.parse(event.data);
  
  switch (message.type) {
    case 'analysis_update':
      handleAnalysisUpdate(message.payload);
      break;
    case 'analysis_complete':
      handleAnalysisComplete(message.payload);
      break;
    case 'notification':
      handleNotification(message.payload);
      break;
  }
};
```

### 4. Handle Disconnection
```javascript
ws.onclose = (event) => {
  console.log('WebSocket disconnected:', event.code, event.reason);
  // Implement reconnection logic
};
```

## Error Handling

### Connection Errors
```json
{
  "type": "error",
  "payload": {
    "code": "auth_failed",
    "message": "Authentication failed"
  }
}
```

### Subscription Errors
```json
{
  "type": "error",
  "payload": {
    "code": "subscription_failed",
    "message": "Invalid analysis ID"
  }
}
```

## Rate Limits

### Standard Tier
- Maximum 1 connection per user
- 60 messages per minute

### Premium Tier
- Maximum 5 concurrent connections
- 600 messages per minute

## Best Practices

### 1. Connection Management
```javascript
class WSClient {
  constructor(url, token) {
    this.url = url;
    this.token = token;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.connect();
  }

  connect() {
    this.ws = new WebSocket(this.url, {
      headers: { 'Authorization': `Bearer ${this.token}` }
    });
    
    this.ws.onclose = this.handleClose.bind(this);
    this.ws.onerror = this.handleError.bind(this);
  }

  handleClose() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      setTimeout(() => {
        this.reconnectAttempts++;
        this.connect();
      }, Math.pow(2, this.reconnectAttempts) * 1000);
    }
  }
}
```

### 2. Message Handling
```javascript
function handleMessage(message) {
  // Validate message format
  if (!message.type || !message.payload) {
    console.error('Invalid message format');
    return;
  }

  // Process message
  try {
    messageHandlers[message.type](message.payload);
  } catch (error) {
    console.error('Error processing message:', error);
  }
}
```

### 3. Subscription Management
```javascript
class SubscriptionManager {
  constructor(ws) {
    this.ws = ws;
    this.subscriptions = new Set();
  }

  subscribe(analysisId) {
    if (this.subscriptions.has(analysisId)) return;
    
    this.ws.send(JSON.stringify({
      type: 'subscribe_analysis',
      payload: { analysis_id: analysisId }
    }));
    
    this.subscriptions.add(analysisId);
  }

  unsubscribe(analysisId) {
    if (!this.subscriptions.has(analysisId)) return;
    
    this.ws.send(JSON.stringify({
      type: 'unsubscribe_analysis',
      payload: { analysis_id: analysisId }
    }));
    
    this.subscriptions.delete(analysisId);
  }
}
```

## Security Considerations

1. **Token Management**
   - Refresh tokens before expiry
   - Handle token invalidation
   - Secure token storage

2. **Message Validation**
   - Validate message format
   - Verify message signatures
   - Check message timestamps

3. **Rate Limiting**
   - Monitor message frequency
   - Implement backoff strategies
   - Handle rate limit errors

4. **Connection Security**
   - Use WSS (WebSocket Secure)
   - Validate server certificates
   - Implement connection timeouts
