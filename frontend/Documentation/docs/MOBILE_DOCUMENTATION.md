# Mobile Documentation - SME Analytica Mobile App

This document consolidates all mobile app documentation for the SME Analytica Mobile App (`/mobile/`).

## Overview

The SME Analytica Mobile App is a React Native application built with Expo, providing comprehensive business analytics and insights for small and medium enterprises.

## Technical Stack

### Mobile Framework
- **React Native 0.79.2** with Expo SDK 53
- **TypeScript** for type safety
- **Expo Router** for navigation
- **Expo Application Services (EAS)** for building and deployment

### Key Dependencies
- **RevenueCat** for subscription management
- **Supabase** for backend integration
- **React Query** for state management
- **Stripe** for payment processing
- **i18next** for internationalization

## Key Features

### Business Analytics
- Comprehensive business intelligence dashboard
- Real-time analytics and insights
- Custom analysis generation
- Historical data tracking

### Subscription Management
- Multiple subscription tiers (Free, Premium, Enterprise)
- Usage tracking and limitations
- Upgrade/downgrade functionality
- Payment processing via Stripe

### AI-Powered Insights
- Executive-level business analysis
- Market sentiment analysis
- Competitor analysis
- Growth strategy recommendations

### Multi-language Support
- English and Spanish localization
- Dynamic language switching
- RTL support preparation

## App Architecture

### Context Providers
- **AuthContext** - User authentication and session management
- **SubscriptionContext** - Subscription state and usage tracking
- **ThemeContext** - Dark/light mode theming
- **NotificationContext** - Push notification handling

### Screen Structure
```
app/
├── (auth)/          # Authentication flow
├── (app)/           # Main app screens
├── (onboarding)/    # User onboarding
└── subscription/    # Subscription management
```

### Services
- **analyticsService** - Business analytics operations
- **subscriptionService** - Subscription management
- **authService** - Authentication handling
- **notificationService** - Push notifications

## Development Setup

### Prerequisites
- Node.js 18+
- Expo CLI
- iOS Simulator / Android Emulator
- EAS CLI for builds

### Installation
```bash
cd mobile/
npm install
npx expo start
```

### Building
```bash
# Development build
eas build --profile development

# Production build
eas build --profile production

# Submit to app stores
eas submit
```

## Testing Strategy

### Test Structure
- **Unit Tests** - Component and service testing
- **Integration Tests** - API and database integration
- **E2E Tests** - Complete user workflows
- **Performance Tests** - App performance metrics

### Test Categories
- Authentication flows
- Subscription workflows
- Analytics functionality
- Offline mode behavior
- UI responsiveness

## Deployment Process

### Build Profiles
- **Development** - Internal testing
- **Preview** - Stakeholder review
- **Production** - App store release

### Distribution
- **Internal** - TestFlight/Internal App Sharing
- **Beta** - Limited external testing
- **Production** - Public app store release

## Subscription Integration

### RevenueCat Configuration
- Product identifiers for different tiers
- Entitlement mapping
- Purchase flow handling
- Restoration capabilities

### Usage Tracking
- API call limitations
- Feature access control
- Usage analytics
- Upgrade prompts

## Security Features

### Authentication
- Supabase Auth integration
- JWT token management
- Secure storage for sensitive data
- Biometric authentication option

### Data Protection
- Encrypted local storage
- Secure API communication
- Privacy policy compliance
- GDPR considerations

## Performance Optimization

### App Performance
- Code splitting and lazy loading
- Image optimization
- Bundle size optimization
- Memory management

### Offline Capabilities
- Local data caching
- Offline mode functionality
- Sync when online
- Conflict resolution

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check EAS configuration
   - Verify signing certificates
   - Review native dependencies

2. **Subscription Issues**
   - Verify RevenueCat configuration
   - Check product identifiers
   - Review entitlement settings

3. **Authentication Problems**
   - Check Supabase configuration
   - Verify JWT settings
   - Review session management

### Development Tools
- **React Native Debugger** - Debugging interface
- **Flipper** - Native debugging
- **EAS Build Logs** - Build troubleshooting
- **Expo DevTools** - Development utilities

## Release Management

### Version Control
- Semantic versioning (x.y.z)
- Build number incrementation
- Release notes documentation
- Change log maintenance

### App Store Guidelines
- iOS App Store compliance
- Google Play Store requirements
- Privacy policy updates
- Content rating considerations

## Future Enhancements

### Planned Features
- Advanced analytics dashboard
- Real-time notifications
- Offline data synchronization
- Multi-business support
- Enhanced reporting capabilities

### Technical Improvements
- Performance optimization
- Enhanced error handling
- Improved accessibility
- Advanced security features

---

*This document consolidates information from multiple mobile app guides and implementation notes. For specific technical details, refer to the codebase and inline documentation.*