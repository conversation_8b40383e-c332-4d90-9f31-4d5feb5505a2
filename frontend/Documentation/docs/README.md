# SME Analytica Documentation

This is the comprehensive documentation website for SME Analytica - an AI-powered business intelligence platform for small and medium enterprises.

## 🌟 Features

- **Bilingual Support**: English and Spanish documentation
- **Modern Design**: Built with Next.js 14, Tailwind CSS, and Radix UI
- **Comprehensive Coverage**: Platform overview, modules, API docs, and business guides
- **Interactive Components**: Live demos, code examples, and API explorer
- **SEO Optimized**: Meta tags, structured data, and performance optimization

## 🏗️ Architecture

### Platform Modules Covered

1. **MenuFlow** - Dynamic pricing system for restaurants
   - QR-based digital menus
   - Real-time pricing optimization
   - POS system integration
   - Traffic and sales analytics

2. **SME App** - Mobile business analytics
   - Sales analysis and forecasting
   - Customer sentiment tracking
   - Competitor benchmarking
   - KPI dashboards

3. **Connecto** - AI voice receptionist (Coming Soon)
   - Multi-language voice AI
   - Business integration
   - 24/7 customer service

4. **Event Ticketing Platform** - Dynamic event pricing (Planned)
   - AI-powered demand forecasting
   - Revenue optimization
   - Event management tools

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd docs

# Install dependencies
npm install

# Start development server
npm run dev
```

### Environment Setup

The documentation site uses Next.js with internationalization. No additional environment variables are required for basic functionality.

## 📁 Project Structure

```
docs/
├── app/
│   ├── [locale]/           # Internationalized routes
│   │   ├── layout.tsx      # Root layout
│   │   ├── page.tsx        # Home page
│   │   ├── platform/       # Platform overview
│   │   ├── modules/        # Module documentation
│   │   ├── ai-automation/  # AI capabilities
│   │   ├── integrations/   # API documentation
│   │   ├── for-businesses/ # Business guides
│   │   └── about/          # Company information
│   └── globals.css         # Global styles
├── components/
│   ├── ui/                 # Reusable UI components
│   ├── navigation.tsx      # Main navigation
│   ├── footer.tsx          # Site footer
│   └── theme-provider.tsx  # Theme management
├── messages/
│   ├── en.json            # English translations
│   └── es.json            # Spanish translations
└── lib/
    └── utils.ts           # Utility functions
```

## 🌐 Internationalization

The site supports English and Spanish with automatic locale detection and switching:

- **English**: `/en/*`
- **Spanish**: `/es/*`

Translation files are located in the `messages/` directory.

## 🎨 Styling

- **Tailwind CSS**: Utility-first CSS framework
- **Radix UI**: Accessible component primitives
- **Dark Mode**: Automatic theme switching
- **Responsive Design**: Mobile-first approach

## 📚 Content Structure

### Target Audiences

1. **Business Owners**: Use cases, benefits, ROI information
2. **Developers**: API documentation, integration guides, SDKs
3. **VCs & Investors**: Market opportunity, technology stack, business model
4. **General Users**: Feature overviews, getting started guides

### Page Types

- **Landing Pages**: High-level overviews with clear CTAs
- **Feature Pages**: Detailed module documentation
- **API Documentation**: Technical integration guides
- **Business Guides**: Industry-specific use cases

## 🔧 Development

### Adding New Pages

1. Create page component in appropriate `app/[locale]/` directory
2. Add navigation links in `components/navigation.tsx`
3. Update translation files in `messages/`
4. Add metadata for SEO

### Styling Guidelines

- Use Tailwind utility classes
- Follow the established design system
- Ensure responsive design
- Test dark mode compatibility

### Component Development

- Use Radix UI primitives when possible
- Follow accessibility best practices
- Include TypeScript types
- Document component props

## 🚀 Deployment

The documentation site is designed to be deployed to:

- **Primary**: docs.smeanalytica.dev
- **Staging**: docs-staging.smeanalytica.dev

### Build Commands

```bash
# Production build
npm run build

# Start production server
npm start

# Type checking
npm run type-check

# Linting
npm run lint
```

## 📊 Analytics & SEO

- **Meta Tags**: Comprehensive meta tag setup
- **Open Graph**: Social media sharing optimization
- **Structured Data**: Rich snippets for search engines
- **Performance**: Optimized images and lazy loading

## 🤝 Contributing

1. Follow the established code style
2. Add translations for new content
3. Test responsive design
4. Ensure accessibility compliance
5. Update documentation as needed

## 📞 Support

For questions about the documentation site:

- **Technical Issues**: Create an issue in the repository
- **Content Updates**: Contact the SME Analytica team
- **Translation Help**: Contribute to the messages files

## 🔗 Related Links

- **Main Platform**: https://smeanalytica.dev
- **API Documentation**: https://api.smeanalytica.dev/docs
- **MenuFlow Demo**: https://restaurants.smeanalytica.dev
- **Mobile App**: Available on iOS and Android

---

Built with ❤️ by the SME Analytica team
