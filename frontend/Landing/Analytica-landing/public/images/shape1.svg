<?xml version="1.0" encoding="UTF-8"?>
<svg width="160" height="160" viewBox="0 0 160 160" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Neural Network Nodes -->
  <circle cx="80" cy="40" r="8" fill="url(#gradient1)" />
  <circle cx="50" cy="80" r="8" fill="url(#gradient1)" />
  <circle cx="110" cy="80" r="8" fill="url(#gradient1)" />
  <circle cx="80" cy="120" r="8" fill="url(#gradient1)" />
  
  <!-- Neural Network Connections -->
  <path d="M80 48L50 72" stroke="url(#gradient1)" stroke-width="2" />
  <path d="M80 48L110 72" stroke="url(#gradient1)" stroke-width="2" />
  <path d="M50 88L80 112" stroke="url(#gradient1)" stroke-width="2" />
  <path d="M110 88L80 112" stroke="url(#gradient1)" stroke-width="2" />
  
  <!-- Decorative Circuit Lines -->
  <path d="M30 40h20M30 120h20M130 40h-20M130 120h-20" stroke="url(#gradient1)" stroke-width="2" stroke-dasharray="4 4" />
  
  <!-- Outer Circle -->
  <circle cx="80" cy="80" r="60" stroke="url(#gradient1)" stroke-width="2" stroke-dasharray="8 8" />
  
  <defs>
    <linearGradient id="gradient1" x1="0" y1="0" x2="160" y2="160" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="#9333EA"/>
      <stop offset="50%" stop-color="#EC4899"/>
      <stop offset="100%" stop-color="#3B82F6"/>
    </linearGradient>
  </defs>
</svg> 