"use client";
import React from "react";
import { HoverBorderGradient } from "./hover-border-gradient";
import { cn } from "../../src/lib/utils";

interface GradientButtonProps {
  children: React.ReactNode;
  className?: string;
  href?: string;
  onClick?: () => void;
  icon?: React.ReactNode;
  size?: "sm" | "md" | "lg";
  variant?: "primary" | "secondary";
}

export function GradientButton({
  children,
  className,
  href,
  onClick,
  icon,
  size = "md",
  variant = "primary",
}: GradientButtonProps) {
  const sizeClasses = {
    sm: "text-xs py-1 px-3",
    md: "text-sm py-2 px-4",
    lg: "text-base py-3 px-6",
  };

  const variantClasses = {
    primary: "bg-[#171f31] text-[#d5dce2]",
    secondary: "bg-[#171f31]/80 text-[#d5dce2]/90",
  };

  const content = (
    <>
      {icon && <span className="mr-2">{icon}</span>}
      {children}
    </>
  );

  if (href) {
    return (
      <HoverBorderGradient
        as="a"
        containerClassName={cn("rounded-full", className)}
        className={cn(
          "flex items-center justify-center font-medium",
          sizeClasses[size],
          variantClasses[variant]
        )}
        {...{ href }} // Spread the href prop
      >
        {content}
      </HoverBorderGradient>
    );
  }

  return (
    <HoverBorderGradient
      as="button"
      containerClassName={cn("rounded-full", className)}
      className={cn(
        "flex items-center justify-center font-medium",
        sizeClasses[size],
        variantClasses[variant]
      )}
      {...(onClick ? { onClick } : {})} // Conditionally spread the onClick prop
    >
      {content}
    </HoverBorderGradient>
  );
}
