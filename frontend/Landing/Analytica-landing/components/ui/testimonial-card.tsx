"use client";
import { cn } from "../../src/lib/utils";
import Image from "next/image";

interface TestimonialCardProps {
  name: string;
  role: string;
  company: string;
  testimonial: string;
  logoSrc: string;
  className?: string;
}

export default function TestimonialCard({
  name,
  role,
  company,
  testimonial,
  logoSrc,
  className,
}: TestimonialCardProps) {
  return (
    <div className={cn("max-w-md w-full", className)}>
      <div
        className={cn(
          "group w-full cursor-pointer overflow-hidden relative card h-[400px] rounded-xl shadow-xl mx-auto flex flex-col justify-end p-6 border border-[#5f7790]/20",
          "bg-[#171f31] bg-cover",
          "transition-all duration-500 hover:shadow-2xl hover:border-[#11a5e8]/30 hover:-translate-y-1"
        )}
      >
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10 group-hover:opacity-20 transition-opacity duration-500">
          <div className="absolute top-0 right-0 w-full h-full bg-gradient-to-br from-[#11a5e8]/20 via-transparent to-[#5f7790]/20 group-hover:from-[#11a5e8]/30 group-hover:to-[#5f7790]/30 transition-colors duration-500"></div>
          <div className="absolute top-0 left-0 w-1/2 h-1/2 bg-[#11a5e8]/5 rounded-full blur-3xl group-hover:bg-[#11a5e8]/10 transition-colors duration-500"></div>
          <div className="absolute bottom-0 right-0 w-1/2 h-1/2 bg-[#5f7790]/5 rounded-full blur-3xl group-hover:bg-[#5f7790]/10 transition-colors duration-500"></div>
          <svg className="absolute inset-0 w-full h-full opacity-5 group-hover:opacity-10 transition-opacity duration-500" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                <path d="M 10 0 L 0 0 0 10" fill="none" stroke="#d5dce2" strokeWidth="0.5"/>
              </pattern>
            </defs>
            <rect width="100" height="100" fill="url(#grid)" />
          </svg>
        </div>
        {/* Logo */}
        <div className="absolute top-6 left-6 h-12 w-auto">
          <Image
            src={logoSrc}
            alt={`${company} logo`}
            width={120}
            height={48}
            className="object-contain h-full w-auto"
          />
        </div>

        {/* Content */}
        <div className="text relative z-20">
          <p className="font-normal text-base text-[#d5dce2]/90 relative mb-6 line-clamp-4">
            "{testimonial}"
          </p>
          <div className="flex flex-col">
            <h3 className="font-bold text-lg text-[#d5dce2] relative">
              {name}
            </h3>
            <p className="font-normal text-sm text-[#d5dce2]/80 relative">
              {role}, {company}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
