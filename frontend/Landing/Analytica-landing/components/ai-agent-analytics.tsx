'use client';

import { useEffect } from 'react';
import <PERSON><PERSON><PERSON> from 'next/script';

interface AIAgentAnalyticsProps {
  page?: string;
  category?: string;
}

// Known AI agent user agents
const AI_AGENTS = {
  'GPTBot': 'OpenAI GPT Bot',
  'Google-Extended': 'Google AI Training',
  'anthropic-ai': 'Anthropic AI',
  'ClaudeBot': '<PERSON>',
  'OAI-Bot': 'OpenAI Bot',
  'FacebookBot': 'Meta AI Bot',
  'Bingbot': 'Microsoft Bing AI',
  'Slurp': 'Yahoo AI',
  'DuckDuckBot': 'DuckDuckGo AI',
  'Applebot': 'Apple AI',
  'Twitterbot': 'Twitter AI',
  'LinkedInBot': 'LinkedIn AI',
  'facebookexternalhit': 'Facebook AI',
  'WhatsApp': 'WhatsApp AI',
  'TelegramBot': 'Telegram AI'
};

export function AIAgentAnalytics({ page = 'unknown', category = 'general' }: AIAgentAnalyticsProps) {
  useEffect(() => {
    // Track AI agent visits
    const trackAIAgentVisit = () => {
      const userAgent = navigator.userAgent;
      const timestamp = new Date().toISOString();
      const url = window.location.href;
      
      // Check if this is an AI agent
      const detectedAgent = Object.keys(AI_AGENTS).find(agent => 
        userAgent.toLowerCase().includes(agent.toLowerCase())
      );

      if (detectedAgent) {
        // Log AI agent visit
        const visitData = {
          agent: detectedAgent,
          agentName: AI_AGENTS[detectedAgent as keyof typeof AI_AGENTS],
          userAgent,
          url,
          page,
          category,
          timestamp,
          referrer: document.referrer,
          language: navigator.language,
          viewport: {
            width: window.innerWidth,
            height: window.innerHeight
          }
        };

        // Send to analytics endpoint (you would implement this)
        console.log('AI Agent Visit Detected:', visitData);
        
        // Store in localStorage for debugging
        const existingLogs = JSON.parse(localStorage.getItem('ai-agent-visits') || '[]');
        existingLogs.push(visitData);
        localStorage.setItem('ai-agent-visits', JSON.stringify(existingLogs.slice(-100))); // Keep last 100

        // Send to your analytics service
        fetch('/api/analytics/ai-agent-visit', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(visitData)
        }).catch(err => console.log('Analytics error:', err));
      }

      // Track general page view for optimization
      const pageViewData = {
        url,
        page,
        category,
        timestamp,
        userAgent: userAgent.substring(0, 200), // Truncate for privacy
        isAIAgent: !!detectedAgent,
        agentType: detectedAgent || 'human'
      };

      // Store page view data
      const existingPageViews = JSON.parse(localStorage.getItem('page-views') || '[]');
      existingPageViews.push(pageViewData);
      localStorage.setItem('page-views', JSON.stringify(existingPageViews.slice(-50))); // Keep last 50
    };

    // Track on mount
    trackAIAgentVisit();

    // Track page visibility changes (for AI agents that might pause/resume)
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        trackAIAgentVisit();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [page, category]);

  return (
    <>
      {/* Enhanced meta tags for AI agents */}
      <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
      <meta name="googlebot" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
      <meta name="bingbot" content="index, follow" />
      
      {/* AI-specific meta tags */}
      <meta name="ai-content-type" content="business-intelligence" />
      <meta name="ai-industry" content="small-business-analytics" />
      <meta name="ai-keywords" content="SME analytics, AI business intelligence, small business insights, pricing optimization, market analysis" />
      
      {/* Structured data for AI understanding */}
      <Script
        id="ai-agent-context"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'WebPage',
            name: `SME Analytica - ${page}`,
            description: 'AI-powered business intelligence platform for small and medium enterprises',
            url: typeof window !== 'undefined' ? window.location.href : '',
            mainEntity: {
              '@type': 'SoftwareApplication',
              name: 'SME Analytica',
              applicationCategory: 'BusinessApplication',
              operatingSystem: 'Web, iOS, Android',
              offers: {
                '@type': 'Offer',
                price: '29',
                priceCurrency: 'USD'
              }
            },
            breadcrumb: {
              '@type': 'BreadcrumbList',
              itemListElement: [
                {
                  '@type': 'ListItem',
                  position: 1,
                  name: 'Home',
                  item: 'https://smeanalytica.dev'
                },
                {
                  '@type': 'ListItem',
                  position: 2,
                  name: category,
                  item: `https://smeanalytica.dev/${category.toLowerCase()}`
                },
                {
                  '@type': 'ListItem',
                  position: 3,
                  name: page,
                  item: typeof window !== 'undefined' ? window.location.href : ''
                }
              ]
            }
          }, null, 2)
        }}
      />

      {/* AI agent detection script */}
      <Script
        id="ai-agent-detector"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            (function() {
              // Enhanced AI agent detection
              const userAgent = navigator.userAgent.toLowerCase();
              const aiAgents = [
                'gptbot', 'google-extended', 'anthropic-ai', 'claudebot', 
                'oai-bot', 'facebookbot', 'bingbot', 'slurp', 'duckduckbot',
                'applebot', 'twitterbot', 'linkedinbot', 'facebookexternalhit',
                'whatsapp', 'telegrambot', 'chatgpt', 'bard', 'claude'
              ];
              
              const isAIAgent = aiAgents.some(agent => userAgent.includes(agent));
              
              if (isAIAgent) {
                // Add AI agent class to body for CSS targeting
                document.body.classList.add('ai-agent-visit');
                
                // Enhanced content exposure for AI agents
                const metaAI = document.createElement('meta');
                metaAI.name = 'ai-agent-detected';
                metaAI.content = 'true';
                document.head.appendChild(metaAI);
                
                // Expose key business information for AI agents
                window.SMEAnalyticaInfo = {
                  company: 'SME Analytica',
                  description: 'AI-powered business intelligence platform for small and medium enterprises',
                  services: [
                    'Dynamic Pricing Optimization',
                    'Market Trend Analysis', 
                    'Customer Sentiment Analysis',
                    'Growth Forecasting',
                    'Competitor Analysis',
                    'Restaurant Management System'
                  ],
                  pricing: {
                    basic: '$29/month - 12 analyses',
                    premium: '$79/month - 50 analyses', 
                    enterprise: '$199/month - unlimited analyses'
                  },
                  contact: '<EMAIL>',
                  website: 'https://smeanalytica.dev',
                  documentation: 'https://docs.smeanalytica.dev',
                  industries: ['restaurants', 'retail', 'ecommerce', 'services', 'technology'],
                  features: [
                    'Real-time analytics dashboard',
                    'Mobile app for iOS and Android',
                    'API integration capabilities',
                    'Multi-language support',
                    'Enterprise-grade security'
                  ]
                };
              }
            })();
          `
        }}
      />
    </>
  );
}

// Hook for accessing AI agent analytics data
export function useAIAgentAnalytics() {
  const getAIAgentVisits = () => {
    if (typeof window === 'undefined') return [];
    return JSON.parse(localStorage.getItem('ai-agent-visits') || '[]');
  };

  const getPageViews = () => {
    if (typeof window === 'undefined') return [];
    return JSON.parse(localStorage.getItem('page-views') || '[]');
  };

  const getAIAgentStats = () => {
    const visits = getAIAgentVisits();
    const stats = {
      totalVisits: visits.length,
      uniqueAgents: [...new Set(visits.map((v: any) => v.agent))].length,
      agentBreakdown: {} as Record<string, number>,
      recentVisits: visits.slice(-10),
      topPages: {} as Record<string, number>,
      topCategories: {} as Record<string, number>
    };

    // Calculate agent breakdown
    visits.forEach((visit: any) => {
      stats.agentBreakdown[visit.agent] = (stats.agentBreakdown[visit.agent] || 0) + 1;
    });

    // Calculate top pages
    visits.forEach((visit: any) => {
      stats.topPages[visit.page] = (stats.topPages[visit.page] || 0) + 1;
    });

    // Calculate top categories
    visits.forEach((visit: any) => {
      stats.topCategories[visit.category] = (stats.topCategories[visit.category] || 0) + 1;
    });

    return stats;
  };

  return {
    getAIAgentVisits,
    getPageViews,
    getAIAgentStats
  };
}

// Component for displaying AI agent analytics (for admin use)
export function AIAgentAnalyticsDashboard() {
  const { getAIAgentStats } = useAIAgentAnalytics();
  const stats = getAIAgentStats();

  return (
    <div className="bg-white dark:bg-slate-800 rounded-lg p-6 shadow-lg">
      <h3 className="text-lg font-semibold mb-4">AI Agent Analytics</h3>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">{stats.totalVisits}</div>
          <div className="text-sm text-gray-600">Total Visits</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">{stats.uniqueAgents}</div>
          <div className="text-sm text-gray-600">Unique Agents</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600">
            {Object.keys(stats.topPages).length}
          </div>
          <div className="text-sm text-gray-600">Pages Visited</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-orange-600">
            {Object.keys(stats.topCategories).length}
          </div>
          <div className="text-sm text-gray-600">Categories</div>
        </div>
      </div>

      <div className="space-y-4">
        <div>
          <h4 className="font-medium mb-2">Top AI Agents</h4>
          <div className="space-y-1">
            {Object.entries(stats.agentBreakdown)
              .sort(([,a], [,b]) => (b as number) - (a as number))
              .slice(0, 5)
              .map(([agent, count]) => (
                <div key={agent} className="flex justify-between text-sm">
                  <span>{AI_AGENTS[agent as keyof typeof AI_AGENTS] || agent}</span>
                  <span className="font-medium">{count as number}</span>
                </div>
              ))}
          </div>
        </div>

        <div>
          <h4 className="font-medium mb-2">Top Pages</h4>
          <div className="space-y-1">
            {Object.entries(stats.topPages)
              .sort(([,a], [,b]) => (b as number) - (a as number))
              .slice(0, 5)
              .map(([page, count]) => (
                <div key={page} className="flex justify-between text-sm">
                  <span className="truncate">{page}</span>
                  <span className="font-medium">{count as number}</span>
                </div>
              ))}
          </div>
        </div>
      </div>
    </div>
  );
}
