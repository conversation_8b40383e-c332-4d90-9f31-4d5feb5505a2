'use client';

import Script from 'next/script';

interface StructuredDataProps {
  type: 'organization' | 'software' | 'faq' | 'blog';
  data?: any;
}

export function StructuredData({ type, data }: StructuredDataProps) {
  const getStructuredData = () => {
    switch (type) {
      case 'organization':
        return {
          '@context': 'https://schema.org',
          '@type': 'Organization',
          name: 'SME Analytica',
          description: 'AI-powered business intelligence platform for small and medium enterprises. Automate data analysis, predict trends, and make confident decisions with AI analytics designed for SMEs.',
          url: 'https://smeanalytica.dev',
          logo: 'https://smeanalytica.dev/logo.svg',
          image: 'https://smeanalytica.dev/images/og-image.jpg',
          foundingDate: '2024',
          founder: {
            '@type': 'Person',
            name: 'SME Analytica Team'
          },
          contactPoint: {
            '@type': 'ContactPoint',
            contactType: 'customer service',
            email: '<EMAIL>',
            url: 'https://smeanalytica.dev/contact'
          },
          sameAs: [
            'https://x.com/smeanalytica',
            'https://linkedin.com/company/sme-analytica'
          ],
          address: {
            '@type': 'PostalAddress',
            addressCountry: 'Global',
            addressRegion: 'Worldwide'
          },
          industry: 'Business Intelligence Software',
          numberOfEmployees: {
            '@type': 'QuantitativeValue',
            value: '10-50'
          },
          knowsAbout: [
            'Business Intelligence',
            'AI Analytics',
            'Small Business Analytics',
            'Market Analysis',
            'Pricing Optimization',
            'Restaurant Management',
            'Dynamic Pricing',
            'Sentiment Analysis',
            'Growth Forecasting'
          ]
        };

      case 'software':
        return {
          '@context': 'https://schema.org',
          '@type': 'SoftwareApplication',
          name: 'SME Analytica Platform',
          description: 'Comprehensive AI-powered business intelligence suite with mobile analytics, restaurant management system, and voice AI capabilities.',
          applicationCategory: 'BusinessApplication',
          operatingSystem: 'Web, iOS, Android',
          offers: {
            '@type': 'Offer',
            price: '29',
            priceCurrency: 'USD',
            priceValidUntil: '2025-12-31',
            availability: 'https://schema.org/InStock',
            url: 'https://smeanalytica.dev/pricing'
          },
          aggregateRating: {
            '@type': 'AggregateRating',
            ratingValue: '4.8',
            ratingCount: '150',
            bestRating: '5'
          },
          featureList: [
            'AI-Powered Market Analysis',
            'Dynamic Pricing Optimization',
            'Customer Sentiment Analysis',
            'Growth Forecasting',
            'Restaurant Menu Management',
            'Real-time Analytics Dashboard',
            'Multi-language Support',
            'Mobile App Integration',
            'Voice AI Assistant'
          ],
          screenshot: 'https://smeanalytica.dev/images/dashboard-screenshot.jpg',
          softwareVersion: '2.0',
          datePublished: '2024-01-01',
          dateModified: '2025-01-01',
          author: {
            '@type': 'Organization',
            name: 'SME Analytica'
          },
          publisher: {
            '@type': 'Organization',
            name: 'SME Analytica',
            logo: {
              '@type': 'ImageObject',
              url: 'https://smeanalytica.dev/logo.svg'
            }
          }
        };

      case 'faq':
        return {
          '@context': 'https://schema.org',
          '@type': 'FAQPage',
          mainEntity: [
            {
              '@type': 'Question',
              name: 'What is SME Analytica and how does it help small businesses?',
              acceptedAnswer: {
                '@type': 'Answer',
                text: 'SME Analytica is an AI-powered business intelligence platform specifically designed for small and medium enterprises. It helps businesses automate data analysis, predict market trends, optimize pricing strategies, and make data-driven decisions. Our platform includes mobile analytics, restaurant management systems, and voice AI capabilities.'
              }
            },
            {
              '@type': 'Question',
              name: 'What types of AI analysis does SME Analytica provide?',
              acceptedAnswer: {
                '@type': 'Answer',
                text: 'SME Analytica offers comprehensive AI analysis including: Dynamic Pricing Optimization, Market Trend Analysis, Customer Sentiment Analysis, Growth Forecasting, Competitor Analysis, and Business Performance Insights. Our AI engine uses advanced models including Claude Opus 4, GPT-4.1, and Gemini 2.0 Flash for accurate predictions.'
              }
            },
            {
              '@type': 'Question',
              name: 'How does the restaurant management system work?',
              acceptedAnswer: {
                '@type': 'Answer',
                text: 'Our Menu Flow Dynamo system provides restaurants with AI-powered menu optimization, dynamic pricing based on demand patterns, customer sentiment analysis from reviews, and real-time analytics. It includes QR code menus, order management, and chef recommendation systems powered by AI insights.'
              }
            },
            {
              '@type': 'Question',
              name: 'What subscription plans are available?',
              acceptedAnswer: {
                '@type': 'Answer',
                text: 'SME Analytica offers three subscription tiers: Basic ($29/month) with 12 analyses, Premium ($79/month) with 50 analyses and advanced features, and Enterprise ($199/month) with unlimited analyses and custom AI models. All plans include a 30-day free trial.'
              }
            },
            {
              '@type': 'Question',
              name: 'Is SME Analytica suitable for different types of businesses?',
              acceptedAnswer: {
                '@type': 'Answer',
                text: 'Yes, SME Analytica serves multiple business types including restaurants, retail stores, e-commerce businesses, service providers, and general SMEs. Our platform adapts AI analysis to specific industry needs and provides customized insights for each business type.'
              }
            },
            {
              '@type': 'Question',
              name: 'How accurate are the AI predictions and recommendations?',
              acceptedAnswer: {
                '@type': 'Answer',
                text: 'Our AI models achieve 85-95% accuracy in predictions depending on the analysis type. We use ensemble methods combining multiple AI models, real-time data sources, and Harvard Business School frameworks to ensure reliable insights. All recommendations include confidence scores and supporting data.'
              }
            }
          ]
        };

      case 'blog':
        return data ? {
          '@context': 'https://schema.org',
          '@type': 'BlogPosting',
          headline: data.title,
          description: data.excerpt,
          image: data.image || 'https://smeanalytica.dev/images/blog-default.jpg',
          author: {
            '@type': 'Person',
            name: data.author || 'SME Analytica Team'
          },
          publisher: {
            '@type': 'Organization',
            name: 'SME Analytica',
            logo: {
              '@type': 'ImageObject',
              url: 'https://smeanalytica.dev/logo.svg'
            }
          },
          datePublished: data.date,
          dateModified: data.date,
          mainEntityOfPage: {
            '@type': 'WebPage',
            '@id': `https://smeanalytica.dev/blog/${data.slug}`
          },
          keywords: data.tags?.join(', ') || '',
          articleSection: data.category || 'Business Intelligence',
          wordCount: data.wordCount || 1500,
          timeRequired: data.readTime || '8 min'
        } : null;

      default:
        return null;
    }
  };

  const structuredData = getStructuredData();

  if (!structuredData) return null;

  return (
    <Script
      id={`structured-data-${type}`}
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData, null, 2)
      }}
    />
  );
}

// Specific components for different pages
export function OrganizationStructuredData() {
  return <StructuredData type="organization" />;
}

export function SoftwareStructuredData() {
  return <StructuredData type="software" />;
}

export function FAQStructuredData() {
  return <StructuredData type="faq" />;
}

export function BlogStructuredData(props: { post: any }) {
  return <StructuredData type="blog" data={props.post} />;
}
