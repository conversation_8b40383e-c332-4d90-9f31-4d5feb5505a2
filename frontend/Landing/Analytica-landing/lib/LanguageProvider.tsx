'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { Language, LanguageManager, content } from './i18n';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (path: string) => string;
  content: typeof content;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setCurrentLanguage] = useState<Language>('es');

  useEffect(() => {
    const detected = LanguageManager.initializeLanguage();
    setCurrentLanguage(detected);
  }, []);

  const setLanguage = (lang: Language) => {
    LanguageManager.setLanguage(lang);
    setCurrentLanguage(lang);
  };

  const t = (path: string): string => {
    return LanguageManager.getText(path, language);
  };

  return (
    <LanguageContext.Provider value={{
      language,
      setLanguage,
      t,
      content
    }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}