import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://kpbfajbqblcezsxstvug.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtwYmZhamJxYmxjZXpzeHN0dnVnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDAwNzU3NTQsImV4cCI6MjA1NTY1MTc1NH0._9ODxewqHlC9AnNFmt50Iaj7cvLTbGSf_TD5CfdIIUY'

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

export interface Restaurant {
  id: string
  name: string
  location?: string
  status: 'active' | 'inactive' | 'trial'
  totalOrders: number
  revenue: number
  customerSatisfaction: number
  lastActive: string
  created_at: string
  user_id?: string
  description?: string
}

export interface RestaurantMetrics {
  restaurantId: string
  totalOrders: number
  totalRevenue: number
  averageOrderValue: number
  customerSatisfaction: number
  lastOrderDate: string
}

// Get all restaurants from the unified businesses/restaurant_details tables
export async function getAllRestaurants(): Promise<Restaurant[]> {
  try {
    // Query the businesses table with proper restaurant_details relationship
    const { data: businesses, error: businessError } = await supabase
      .from('businesses')
      .select(`
        id,
        name,
        description,
        address,
        email,
        phone,
        created_at,
        user_id,
        restaurant_details (
          id,
          cuisine_type,
          operating_hours,
          seating_capacity,
          delivery_radius,
          accepts_reservations,
          accepts_online_orders,
          dynamic_pricing_enabled
        )
      `)
      .eq('business_type_id', 'cc5b95b2-6ca8-42d0-ad7f-b172d3385af0') // Restaurant business type UUID

    if (businessError) {
      console.error('Error fetching businesses:', businessError)
      return []
    }

    // Process businesses data
    const restaurants: Restaurant[] = await Promise.all(
      (businesses || []).map(async (business) => {
        // Get metrics for this restaurant
        const metrics = await getRestaurantMetrics(business.id)

        return {
          id: business.id,
          name: business.name || 'Unknown Restaurant',
          location: business.address || 'Unknown Location',
          status: 'active' as const, // You can implement status logic
          totalOrders: metrics?.totalOrders || 0,
          revenue: metrics?.totalRevenue || 0,
          customerSatisfaction: metrics?.customerSatisfaction || 4.0,
          lastActive: metrics?.lastOrderDate || business.created_at || 'Unknown',
          created_at: business.created_at || new Date().toISOString(),
          user_id: business.user_id,
          description: business.description
        }
      })
    )

    return restaurants
  } catch (error) {
    console.error('Error in getAllRestaurants:', error)
    return []
  }
}

// Get detailed metrics for a specific restaurant
export async function getRestaurantMetrics(restaurantId: string): Promise<RestaurantMetrics | null> {
  try {
    // Get orders for this restaurant
    const { data: orders, error: ordersError } = await supabase
      .from('orders')
      .select(`
        id,
        total_amount,
        created_at,
        status,
        order_items (
          quantity,
          unit_price
        )
      `)
      .eq('restaurant_id', restaurantId)
      .order('created_at', { ascending: false })

    if (ordersError) {
      console.warn('Error fetching orders for restaurant:', restaurantId, ordersError)
    }

    // Get customer feedback for satisfaction rating
    const { data: feedback, error: feedbackError } = await supabase
      .from('customer_feedback')
      .select('food_rating, service_rating, app_rating')
      .eq('restaurant_id', restaurantId)

    if (feedbackError) {
      console.warn('Error fetching feedback for restaurant:', restaurantId, feedbackError)
    }

    // Calculate metrics
    const totalOrders = orders?.length || 0
    const totalRevenue = orders?.reduce((sum, order) => {
      // Calculate order total from order_items if total_amount is not available
      const orderTotal = order.total_amount || 
        order.order_items?.reduce((itemSum: number, item: any) => 
          itemSum + (item.quantity * item.unit_price), 0) || 0
      return sum + orderTotal
    }, 0) || 0

    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0
    
    // Calculate average customer satisfaction
    const avgSatisfaction = feedback?.length ? 
      feedback.reduce((sum, f) => sum + (f.food_rating || 0), 0) / feedback.length : 4.0

    const lastOrderDate = orders?.[0]?.created_at || new Date().toISOString()

    return {
      restaurantId,
      totalOrders,
      totalRevenue,
      averageOrderValue,
      customerSatisfaction: avgSatisfaction,
      lastOrderDate
    }
  } catch (error) {
    console.error('Error getting restaurant metrics:', error)
    return null
  }
}

// Get platform-wide statistics
export async function getPlatformStats() {
  try {
    const [restaurantsResult, ordersResult, usersResult] = await Promise.all([
      supabase.from('businesses').select('id', { count: 'exact' }),
      supabase.from('orders').select('id', { count: 'exact' }),
      supabase.from('profiles').select('id', { count: 'exact' })
    ])

    return {
      totalRestaurants: restaurantsResult.count || 0,
      totalOrders: ordersResult.count || 0,
      totalUsers: usersResult.count || 0,
      platformUptime: 99.9 // This would come from monitoring system
    }
  } catch (error) {
    console.error('Error getting platform stats:', error)
    return {
      totalRestaurants: 0,
      totalOrders: 0,
      totalUsers: 0,
      platformUptime: 99.9
    }
  }
}