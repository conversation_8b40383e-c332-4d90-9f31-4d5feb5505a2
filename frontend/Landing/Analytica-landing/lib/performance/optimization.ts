/**
 * Performance Optimization Utilities
 * Tools for maintaining 90+ Lighthouse scores and 60fps animations
 */

import { gsap } from 'gsap';

export class PerformanceOptimizer {
  private static instance: PerformanceOptimizer;
  private observers: IntersectionObserver[] = [];
  private rafId: number | null = null;
  private isReducedMotion = false;

  static getInstance(): PerformanceOptimizer {
    if (!PerformanceOptimizer.instance) {
      PerformanceOptimizer.instance = new PerformanceOptimizer();
    }
    return PerformanceOptimizer.instance;
  }

  constructor() {
    this.detectReducedMotion();
    this.setupFrameRateMonitoring();
    this.optimizeGSAP();
  }

  // Detect user's reduced motion preference
  private detectReducedMotion() {
    if (typeof window !== 'undefined') {
      const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
      this.isReducedMotion = mediaQuery.matches;
      
      mediaQuery.addEventListener('change', (e) => {
        this.isReducedMotion = e.matches;
        this.updateAnimationSettings();
      });
    }
  }

  // Update animation settings based on user preference
  private updateAnimationSettings() {
    if (this.isReducedMotion) {
      gsap.globalTimeline.timeScale(0.1);
      gsap.defaults({ duration: 0.01 });
    } else {
      gsap.globalTimeline.timeScale(1);
      gsap.defaults({ duration: 1 });
    }
  }

  // Optimize GSAP settings for performance
  private optimizeGSAP() {
    // Enable hardware acceleration for better performance
    gsap.set('body', { force3D: true });
    
    // Set optimal refresh rate
    gsap.ticker.fps(60);
    
    // Use will-change CSS property for animated elements
    gsap.set('[data-animate]', { willChange: 'transform, opacity' });
  }

  // Monitor frame rate and adjust quality accordingly
  private setupFrameRateMonitoring() {
    if (typeof window === 'undefined') return;

    let lastTime = 0;
    let frameCount = 0;
    let fps = 60;

    const measureFrameRate = (currentTime: number) => {
      frameCount++;
      
      if (currentTime - lastTime >= 1000) {
        fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        frameCount = 0;
        lastTime = currentTime;
        
        // Adjust animation quality based on FPS
        this.adjustAnimationQuality(fps);
      }
      
      this.rafId = requestAnimationFrame(measureFrameRate);
    };

    this.rafId = requestAnimationFrame(measureFrameRate);
  }

  // Adjust animation quality based on performance
  private adjustAnimationQuality(fps: number) {
    if (fps < 30) {
      // Low performance - reduce animation complexity
      gsap.globalTimeline.timeScale(0.5);
      document.documentElement.classList.add('low-performance');
    } else if (fps < 50) {
      // Medium performance - moderate animations
      gsap.globalTimeline.timeScale(0.8);
      document.documentElement.classList.remove('low-performance');
      document.documentElement.classList.add('medium-performance');
    } else {
      // High performance - full animations
      gsap.globalTimeline.timeScale(1);
      document.documentElement.classList.remove('low-performance', 'medium-performance');
    }
  }

  // Lazy load images with intersection observer
  lazyLoadImages(selector: string = 'img[data-src]') {
    if (typeof window === 'undefined') return;

    const images = document.querySelectorAll(selector);
    
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            const src = img.dataset.src;
            
            if (src) {
              // Create a new image to preload
              const newImg = new Image();
              newImg.onload = () => {
                img.src = src;
                img.classList.remove('lazy');
                img.classList.add('loaded');
              };
              newImg.src = src;
              
              observer.unobserve(img);
            }
          }
        });
      },
      {
        rootMargin: '50px 0px',
        threshold: 0.1
      }
    );

    images.forEach((img) => observer.observe(img));
    this.observers.push(observer);
  }

  // Defer non-critical animations
  deferAnimation(callback: () => void, delay: number = 100) {
    if (typeof window === 'undefined') {
      callback();
      return;
    }

    // Use requestIdleCallback if available, otherwise use setTimeout
    if ('requestIdleCallback' in window) {
      (window as any).requestIdleCallback(callback, { timeout: delay });
    } else {
      setTimeout(callback, delay);
    }
  }

  // Optimize scroll performance
  optimizeScrolling() {
    if (typeof window === 'undefined') return;

    let ticking = false;

    const updateScrollEffects = () => {
      // Batch scroll-dependent updates
      const scrollY = window.scrollY;
      
      // Update CSS custom property for scroll-based animations
      document.documentElement.style.setProperty('--scroll-y', scrollY.toString());
      
      ticking = false;
    };

    window.addEventListener('scroll', () => {
      if (!ticking) {
        requestAnimationFrame(updateScrollEffects);
        ticking = true;
      }
    }, { passive: true });
  }

  // Preload critical resources
  preloadResources(resources: Array<{ type: 'image' | 'font' | 'script'; url: string }>) {
    if (typeof document === 'undefined') return;

    resources.forEach(({ type, url }) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = url;
      
      switch (type) {
        case 'image':
          link.as = 'image';
          break;
        case 'font':
          link.as = 'font';
          link.type = 'font/woff2';
          link.crossOrigin = 'anonymous';
          break;
        case 'script':
          link.as = 'script';
          break;
      }
      
      document.head.appendChild(link);
    });
  }

  // Optimize third-party scripts
  optimizeThirdPartyScripts() {
    if (typeof document === 'undefined') return;

    // Delay third-party scripts until user interaction
    let hasInteracted = false;
    
    const loadThirdPartyScripts = () => {
      if (hasInteracted) return;
      hasInteracted = true;
      
      // Load deferred scripts
      const deferredScripts = document.querySelectorAll('script[data-defer]');
      deferredScripts.forEach((script) => {
        const newScript = document.createElement('script');
        newScript.src = script.getAttribute('data-defer') || '';
        newScript.async = true;
        document.head.appendChild(newScript);
      });
    };

    // Load on first user interaction
    ['click', 'scroll', 'keydown', 'touchstart'].forEach((event) => {
      document.addEventListener(event, loadThirdPartyScripts, { once: true, passive: true });
    });

    // Fallback: load after 3 seconds
    setTimeout(loadThirdPartyScripts, 3000);
  }

  // Memory management for animations
  cleanupAnimations() {
    // Kill all GSAP animations
    gsap.killTweensOf('*');
    
    // Clear observers
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    
    // Cancel frame rate monitoring
    if (this.rafId) {
      cancelAnimationFrame(this.rafId);
      this.rafId = null;
    }
  }

  // Critical rendering path optimization
  optimizeCriticalPath() {
    if (typeof document === 'undefined') return;

    // Inline critical CSS
    const criticalStyles = document.querySelector('style[data-critical]');
    if (criticalStyles) {
      criticalStyles.textContent = criticalStyles.textContent?.replace(/\s+/g, ' ') || '';
    }

    // Preconnect to external domains
    const domains = [
      'https://fonts.googleapis.com',
      'https://fonts.gstatic.com'
    ];

    domains.forEach(domain => {
      const link = document.createElement('link');
      link.rel = 'preconnect';
      link.href = domain;
      document.head.appendChild(link);
    });
  }

  // Get performance metrics
  getPerformanceMetrics() {
    if (typeof window === 'undefined') return {};

    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    
    return {
      // Core Web Vitals
      fcp: this.getFCP(),
      lcp: this.getLCP(),
      cls: this.getCLS(),
      fid: this.getFID(),
      
      // Loading metrics
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
      loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
      
      // Network metrics
      dns: navigation.domainLookupEnd - navigation.domainLookupStart,
      connection: navigation.connectEnd - navigation.connectStart,
      request: navigation.responseEnd - navigation.requestStart,
      
      // Memory usage (if available)
      memory: (performance as any).memory ? {
        used: (performance as any).memory.usedJSHeapSize,
        total: (performance as any).memory.totalJSHeapSize,
        limit: (performance as any).memory.jsHeapSizeLimit
      } : null
    };
  }

  // First Contentful Paint
  private getFCP() {
    const fcpEntry = performance.getEntriesByName('first-contentful-paint')[0];
    return fcpEntry ? fcpEntry.startTime : 0;
  }

  // Largest Contentful Paint
  private getLCP() {
    return new Promise((resolve) => {
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        resolve(lastEntry.startTime);
      }).observe({ entryTypes: ['largest-contentful-paint'] });
    });
  }

  // Cumulative Layout Shift
  private getCLS() {
    return new Promise((resolve) => {
      let clsValue = 0;
      new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
          }
        }
        resolve(clsValue);
      }).observe({ entryTypes: ['layout-shift'] });
    });
  }

  // First Input Delay
  private getFID() {
    return new Promise((resolve) => {
      new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          resolve((entry as any).processingStart - entry.startTime);
        }
      }).observe({ entryTypes: ['first-input'] });
    });
  }

  // Check if reduced motion is preferred
  get prefersReducedMotion() {
    return this.isReducedMotion;
  }
}

// Export singleton instance
export const performanceOptimizer = PerformanceOptimizer.getInstance();