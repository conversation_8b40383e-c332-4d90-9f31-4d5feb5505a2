/**
 * GSAP Configuration and Registration
 * Premium animation system for SME Analytica Landing Page
 */

import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { TextPlugin } from 'gsap/TextPlugin';
import { MorphSVGPlugin } from 'gsap/MorphSVGPlugin';

// Register GSAP plugins (ensure you have commercial license for MorphSVG)
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger, TextPlugin);
  // gsap.registerPlugin(MorphSVGPlugin); // Uncomment when you have commercial license
}

// Global GSAP configuration
export const initGSAP = () => {
  // Set global defaults
  gsap.defaults({
    duration: 1,
    ease: "power2.out"
  });

  // Configure ScrollTrigger defaults
  ScrollTrigger.defaults({
    toggleActions: "restart pause resume pause",
    scroller: typeof window !== 'undefined' ? window : undefined
  });

  // Add custom eases
  gsap.registerEase("customOut", "power2.out");
  gsap.registerEase("customInOut", "power2.inOut");
  gsap.registerEase("elastic", "elastic.out(1, 0.3)");
  gsap.registerEase("back", "back.out(1.7)");
};

// Animation configuration constants
export const ANIMATION_CONFIG = {
  // Timing
  durations: {
    fast: 0.3,
    normal: 0.6,
    slow: 1.0,
    slower: 1.5,
    slowest: 2.0
  },
  
  // Eases (using GSAP's ease strings)
  eases: {
    smooth: "power2.out",
    bounce: "back.out(1.7)",
    elastic: "elastic.out(1, 0.3)",
    sharp: "power3.inOut",
    gentle: "sine.inOut"
  },
  
  // Stagger timing
  stagger: {
    fast: 0.1,
    normal: 0.15,
    slow: 0.2,
    slower: 0.3
  },
  
  // Viewport triggers
  triggers: {
    start: "top 80%",
    startCenter: "top 50%",
    startBottom: "bottom 80%",
    end: "bottom 20%",
    endCenter: "bottom 50%"
  }
} as const;

// Utility function to create responsive animations
export const createResponsiveAnimation = (
  desktop: gsap.TweenVars,
  tablet?: Partial<gsap.TweenVars>,
  mobile?: Partial<gsap.TweenVars>
) => {
  const mm = gsap.matchMedia();
  
  mm.add("(min-width: 1024px)", () => desktop);
  
  if (tablet) {
    mm.add("(min-width: 768px) and (max-width: 1023px)", () => ({
      ...desktop,
      ...tablet
    }));
  }
  
  if (mobile) {
    mm.add("(max-width: 767px)", () => ({
      ...desktop,
      ...mobile
    }));
  }
  
  return mm;
};

// Performance optimization utilities
export const optimizeForPerformance = () => {
  // Enable hardware acceleration
  gsap.set("body", { force3D: true });
  
  // Optimize ScrollTrigger refresh
  ScrollTrigger.config({
    autoRefreshEvents: "visibilitychange,DOMContentLoaded,load,resize"
  });
};

export { gsap, ScrollTrigger };