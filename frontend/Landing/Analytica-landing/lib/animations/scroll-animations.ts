/**
 * Scroll-based Animations
 * Professional scroll-triggered animations for premium UX
 */

import { gsap, ScrollTrigger, ANIMATION_CONFIG } from './gsap-config';

export class ScrollAnimations {
  private static instance: ScrollAnimations;
  private registeredAnimations: Set<string> = new Set();

  static getInstance(): ScrollAnimations {
    if (!ScrollAnimations.instance) {
      ScrollAnimations.instance = new ScrollAnimations();
    }
    return ScrollAnimations.instance;
  }

  // Premium fade-up animation with stagger
  fadeUpStagger(
    selector: string | HTMLElement[] | NodeList,
    options: {
      start?: string;
      stagger?: number;
      y?: number;
      duration?: number;
      delay?: number;
    } = {}
  ) {
    const {
      start = ANIMATION_CONFIG.triggers.start,
      stagger = ANIMATION_CONFIG.stagger.normal,
      y = 60,
      duration = ANIMATION_CONFIG.durations.normal,
      delay = 0
    } = options;

    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: typeof selector === 'string' ? selector : selector[0],
        start,
        toggleActions: "play none none reverse"
      }
    });

    tl.fromTo(selector, 
      {
        opacity: 0,
        y: y,
        scale: 0.95
      },
      {
        opacity: 1,
        y: 0,
        scale: 1,
        duration,
        delay,
        stagger: {
          amount: stagger,
          from: "start"
        },
        ease: ANIMATION_CONFIG.eases.smooth
      }
    );

    return tl;
  }

  // Parallax scrolling effect
  parallaxScroll(
    selector: string,
    options: {
      speed?: number;
      direction?: 'up' | 'down';
      scrub?: boolean | number;
    } = {}
  ) {
    const { speed = 0.5, direction = 'up', scrub = 1 } = options;
    const yPercent = direction === 'up' ? speed * -100 : speed * 100;

    gsap.to(selector, {
      yPercent,
      ease: "none",
      scrollTrigger: {
        trigger: selector,
        start: "top bottom",
        end: "bottom top",
        scrub: scrub
      }
    });
  }

  // Text reveal animation (character by character)
  textReveal(
    selector: string,
    options: {
      start?: string;
      stagger?: number;
      duration?: number;
      splitType?: 'chars' | 'words' | 'lines';
    } = {}
  ) {
    const {
      start = ANIMATION_CONFIG.triggers.start,
      stagger = 0.03,
      duration = ANIMATION_CONFIG.durations.normal,
      splitType = 'chars'
    } = options;

    // Note: This would work with SplitText plugin (GSAP premium)
    // For now, we'll use a fallback approach
    const element = document.querySelector(selector) as HTMLElement;
    if (!element) return;

    gsap.fromTo(element.children || element,
      {
        opacity: 0,
        y: 20,
        rotationX: 90
      },
      {
        opacity: 1,
        y: 0,
        rotationX: 0,
        duration,
        stagger,
        ease: ANIMATION_CONFIG.eases.bounce,
        scrollTrigger: {
          trigger: selector,
          start,
          toggleActions: "play none none reverse"
        }
      }
    );
  }

  // Scale on scroll animation
  scaleOnScroll(
    selector: string,
    options: {
      scale?: [number, number];
      scrub?: boolean | number;
      pin?: boolean;
    } = {}
  ) {
    const { scale = [0.8, 1], scrub = 1, pin = false } = options;

    gsap.fromTo(selector,
      { scale: scale[0] },
      {
        scale: scale[1],
        ease: "none",
        scrollTrigger: {
          trigger: selector,
          start: "top bottom",
          end: "bottom top",
          scrub,
          pin
        }
      }
    );
  }

  // Magnetic hover effect
  magneticHover(selector: string, strength: number = 0.3) {
    const elements = gsap.utils.toArray(selector) as HTMLElement[];
    
    elements.forEach((element) => {
      const handleMouseMove = (e: MouseEvent) => {
        const rect = element.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        
        const deltaX = (e.clientX - centerX) * strength;
        const deltaY = (e.clientY - centerY) * strength;
        
        gsap.to(element, {
          x: deltaX,
          y: deltaY,
          duration: 0.3,
          ease: ANIMATION_CONFIG.eases.smooth
        });
      };

      const handleMouseLeave = () => {
        gsap.to(element, {
          x: 0,
          y: 0,
          duration: 0.5,
          ease: ANIMATION_CONFIG.eases.elastic
        });
      };

      element.addEventListener('mousemove', handleMouseMove);
      element.addEventListener('mouseleave', handleMouseLeave);
    });
  }

  // Counter animation
  counterAnimation(
    selector: string,
    options: {
      start?: number;
      end: number;
      duration?: number;
      ease?: string;
      trigger?: string;
      format?: (value: number) => string;
    }
  ) {
    const {
      start = 0,
      end,
      duration = 2,
      ease = ANIMATION_CONFIG.eases.smooth,
      trigger = selector,
      format = (value) => Math.round(value).toString()
    } = options;

    const element = document.querySelector(selector) as HTMLElement;
    if (!element) return;

    const counter = { value: start };

    gsap.to(counter, {
      value: end,
      duration,
      ease,
      onUpdate: () => {
        element.textContent = format(counter.value);
      },
      scrollTrigger: {
        trigger,
        start: ANIMATION_CONFIG.triggers.start,
        toggleActions: "play none none reverse"
      }
    });
  }

  // Morphing path animation (for SVG)
  morphPath(
    selector: string,
    paths: string[],
    options: {
      duration?: number;
      repeat?: number;
      yoyo?: boolean;
    } = {}
  ) {
    const { duration = 2, repeat = -1, yoyo = true } = options;
    
    // Note: This requires MorphSVGPlugin (GSAP premium)
    // For now, we'll create a fallback opacity animation
    const element = document.querySelector(selector);
    if (!element) return;

    gsap.to(element, {
      duration,
      repeat,
      yoyo,
      ease: "sine.inOut",
      // This would be: morphSVG: paths[1] with the plugin
      opacity: 0.8,
      scale: 1.05,
      scrollTrigger: {
        trigger: selector,
        start: ANIMATION_CONFIG.triggers.start,
        toggleActions: "play none none reverse"
      }
    });
  }

  // Cleanup method
  killAll() {
    ScrollTrigger.killAll();
    this.registeredAnimations.clear();
  }

  // Refresh ScrollTrigger (useful for dynamic content)
  refresh() {
    ScrollTrigger.refresh();
  }
}

// Export singleton instance
export const scrollAnimations = ScrollAnimations.getInstance();