/**
 * Page Transitions
 * Smooth page transition animations for premium UX
 */

import { gsap, ANIMATION_CONFIG } from './gsap-config';

export class PageTransitions {
  private static instance: PageTransitions;
  private isTransitioning = false;
  private transitionOverlay: HTMLElement | null = null;

  static getInstance(): PageTransitions {
    if (!PageTransitions.instance) {
      PageTransitions.instance = new PageTransitions();
    }
    return PageTransitions.instance;
  }

  constructor() {
    this.createTransitionOverlay();
    this.bindEvents();
  }

  private createTransitionOverlay() {
    if (typeof window === 'undefined') return;

    this.transitionOverlay = document.createElement('div');
    this.transitionOverlay.className = 'page-transition-overlay';
    this.transitionOverlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #171f31 0%, #5f7790 50%, #11a5e8 100%);
      pointer-events: none;
      z-index: 9999;
      transform: translateY(-100%);
    `;
    
    document.body.appendChild(this.transitionOverlay);
  }

  private bindEvents() {
    if (typeof window === 'undefined') return;

    // Listen for route changes (Next.js specific)
    window.addEventListener('beforeunload', this.handlePageExit.bind(this));
  }

  // Slide transition
  slideTransition(
    direction: 'left' | 'right' | 'up' | 'down' = 'right',
    duration: number = 0.8
  ): Promise<void> {
    return new Promise((resolve) => {
      if (!this.transitionOverlay) {
        resolve();
        return;
      }

      this.isTransitioning = true;

      const directions = {
        left: { from: 'translateX(100%)', to: 'translateX(-100%)' },
        right: { from: 'translateX(-100%)', to: 'translateX(100%)' },
        up: { from: 'translateY(100%)', to: 'translateY(-100%)' },
        down: { from: 'translateY(-100%)', to: 'translateY(100%)' }
      };

      const { from, to } = directions[direction];

      const tl = gsap.timeline({
        onComplete: () => {
          this.isTransitioning = false;
          resolve();
        }
      });

      tl.set(this.transitionOverlay, { transform: from })
        .to(this.transitionOverlay, {
          transform: 'translate(0, 0)',
          duration: duration / 2,
          ease: ANIMATION_CONFIG.eases.sharp
        })
        .to(this.transitionOverlay, {
          transform: to,
          duration: duration / 2,
          ease: ANIMATION_CONFIG.eases.sharp,
          delay: 0.1
        });
    });
  }

  // Wipe transition
  wipeTransition(
    direction: 'horizontal' | 'vertical' = 'horizontal',
    duration: number = 1
  ): Promise<void> {
    return new Promise((resolve) => {
      if (!this.transitionOverlay) {
        resolve();
        return;
      }

      this.isTransitioning = true;

      const tl = gsap.timeline({
        onComplete: () => {
          this.isTransitioning = false;
          resolve();
        }
      });

      if (direction === 'horizontal') {
        tl.set(this.transitionOverlay, {
          clipPath: 'inset(0 100% 0 0)',
          transform: 'translate(0, 0)'
        })
        .to(this.transitionOverlay, {
          clipPath: 'inset(0 0% 0 0)',
          duration: duration / 2,
          ease: ANIMATION_CONFIG.eases.sharp
        })
        .to(this.transitionOverlay, {
          clipPath: 'inset(0 0% 0 100%)',
          duration: duration / 2,
          ease: ANIMATION_CONFIG.eases.sharp,
          delay: 0.1
        });
      } else {
        tl.set(this.transitionOverlay, {
          clipPath: 'inset(100% 0 0 0)',
          transform: 'translate(0, 0)'
        })
        .to(this.transitionOverlay, {
          clipPath: 'inset(0% 0 0 0)',
          duration: duration / 2,
          ease: ANIMATION_CONFIG.eases.sharp
        })
        .to(this.transitionOverlay, {
          clipPath: 'inset(0 0 100% 0)',
          duration: duration / 2,
          ease: ANIMATION_CONFIG.eases.sharp,
          delay: 0.1
        });
      }
    });
  }

  // Fade transition with scale
  fadeScaleTransition(duration: number = 0.6): Promise<void> {
    return new Promise((resolve) => {
      if (!this.transitionOverlay) {
        resolve();
        return;
      }

      this.isTransitioning = true;

      const currentPage = document.querySelector('main') || document.body;

      const tl = gsap.timeline({
        onComplete: () => {
          this.isTransitioning = false;
          resolve();
        }
      });

      tl.set(this.transitionOverlay, {
        opacity: 0,
        scale: 0.9,
        transform: 'translate(0, 0)'
      })
      .to([currentPage, this.transitionOverlay], {
        opacity: 0,
        scale: 0.95,
        duration: duration / 2,
        ease: ANIMATION_CONFIG.eases.smooth,
        stagger: 0.1
      })
      .set(currentPage, { opacity: 1, scale: 1 })
      .fromTo(currentPage, 
        { opacity: 0, scale: 1.05 },
        {
          opacity: 1,
          scale: 1,
          duration: duration / 2,
          ease: ANIMATION_CONFIG.eases.smooth
        }
      );
    });
  }

  // Page entrance animation
  pageEntrance(elements: string[] = ['header', 'main', 'footer']) {
    const tl = gsap.timeline();

    elements.forEach((selector, index) => {
      const element = document.querySelector(selector);
      if (element) {
        tl.fromTo(element,
          {
            opacity: 0,
            y: 50,
            scale: 0.95
          },
          {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: ANIMATION_CONFIG.durations.slow,
            ease: ANIMATION_CONFIG.eases.smooth
          },
          index * 0.1
        );
      }
    });

    return tl;
  }

  // Page exit animation
  pageExit(elements: string[] = ['main']): Promise<void> {
    return new Promise((resolve) => {
      const tl = gsap.timeline({
        onComplete: resolve
      });

      elements.forEach((selector, index) => {
        const element = document.querySelector(selector);
        if (element) {
          tl.to(element, {
            opacity: 0,
            y: -30,
            scale: 0.98,
            duration: ANIMATION_CONFIG.durations.fast,
            ease: ANIMATION_CONFIG.eases.smooth
          }, index * 0.05);
        }
      });
    });
  }

  // Handle page exit event
  private handlePageExit() {
    if (this.isTransitioning) return;
    this.pageExit();
  }

  // Loading animation
  loadingAnimation(): gsap.core.Timeline {
    const loader = document.createElement('div');
    loader.className = 'page-loader';
    loader.innerHTML = `
      <div class="loader-content">
        <div class="loader-logo">SME Analytica</div>
        <div class="loader-progress">
          <div class="loader-bar"></div>
        </div>
      </div>
    `;
    loader.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: #171f31;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10000;
    `;

    document.body.appendChild(loader);

    const tl = gsap.timeline({
      onComplete: () => {
        document.body.removeChild(loader);
      }
    });

    tl.fromTo('.loader-logo', 
      { opacity: 0, y: 20 },
      { opacity: 1, y: 0, duration: 0.6, ease: ANIMATION_CONFIG.eases.smooth }
    )
    .fromTo('.loader-bar',
      { scaleX: 0 },
      { scaleX: 1, duration: 1.5, ease: ANIMATION_CONFIG.eases.smooth }
    )
    .to('.loader-content', {
      opacity: 0,
      scale: 0.9,
      duration: 0.4,
      ease: ANIMATION_CONFIG.eases.smooth,
      delay: 0.3
    })
    .to(loader, {
      opacity: 0,
      duration: 0.3
    });

    return tl;
  }

  // Check if transitioning
  get isActive(): boolean {
    return this.isTransitioning;
  }
}

// Export singleton instance
export const pageTransitions = PageTransitions.getInstance();