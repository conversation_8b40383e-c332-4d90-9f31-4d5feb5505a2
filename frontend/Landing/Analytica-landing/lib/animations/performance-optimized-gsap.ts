/**
 * Performance-Optimized GSAP Animations
 * Designed for 90+ Lighthouse Performance Score
 */

import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

// Performance configuration
export const initPerformanceOptimizedGSAP = () => {
  // Enable hardware acceleration
  gsap.set("body", { 
    force3D: true,
    // Optimize rendering
    willChange: "transform",
    // Enable hardware acceleration for better performance
    perspective: 1000
  });

  // Configure ScrollTrigger for optimal performance
  ScrollTrigger.config({
    // Reduce refresh events for better performance
    autoRefreshEvents: "visibilitychange,DOMContentLoaded,load",
    // Limit refresh rate
    syncInterval: 150,
    // Use passive event listeners
    ignoreMobileResize: true,
    // Batch scroll updates
    anticipatePin: 1
  });

  // Set global defaults for better performance
  gsap.defaults({
    duration: 0.8,
    ease: "power2.out",
    // Force hardware acceleration
    force3D: true,
    // Use will-change for better performance
    willChange: "transform, opacity"
  });

  // Performance-optimized ticker
  gsap.ticker.fps(60);
  gsap.ticker.lagSmoothing(500, 33);
};

// Optimized animation presets
export const performanceAnimations = {
  // Fast entrance animations (< 0.5s)
  fastFadeIn: {
    duration: 0.3,
    opacity: 1,
    y: 0,
    ease: "power2.out",
    force3D: true
  },

  fastSlideUp: {
    duration: 0.4,
    y: 0,
    opacity: 1,
    ease: "power2.out",
    force3D: true
  },

  fastScale: {
    duration: 0.3,
    scale: 1,
    opacity: 1,
    ease: "back.out(1.2)",
    force3D: true
  },

  // Smooth scroll-triggered animations
  smoothReveal: {
    duration: 0.8,
    y: 0,
    opacity: 1,
    ease: "power2.out",
    force3D: true,
    stagger: 0.1
  },

  smoothSlide: {
    duration: 1,
    x: 0,
    opacity: 1,
    ease: "power3.out",
    force3D: true
  },

  // Micro-interactions (very fast)
  microHover: {
    duration: 0.2,
    scale: 1.05,
    ease: "power2.out",
    force3D: true
  },

  microClick: {
    duration: 0.1,
    scale: 0.95,
    ease: "power2.out",
    force3D: true
  }
};

// Performance-optimized scroll animations
export class PerformanceScrollAnimations {
  private static instance: PerformanceScrollAnimations;
  private animations: gsap.core.Timeline[] = [];
  private isInitialized = false;

  static getInstance(): PerformanceScrollAnimations {
    if (!PerformanceScrollAnimations.instance) {
      PerformanceScrollAnimations.instance = new PerformanceScrollAnimations();
    }
    return PerformanceScrollAnimations.instance;
  }

  init() {
    if (this.isInitialized) return;
    
    initPerformanceOptimizedGSAP();
    this.isInitialized = true;
  }

  // Batch scroll animations for better performance
  createBatchScrollAnimation(elements: string | NodeList | Element[], options: {
    trigger?: string;
    start?: string;
    end?: string;
    animation?: gsap.TweenVars;
    stagger?: number;
  }) {
    const timeline = gsap.timeline({
      scrollTrigger: {
        trigger: options.trigger || elements,
        start: options.start || "top 80%",
        end: options.end || "bottom 20%",
        toggleActions: "play none none reverse",
        once: true, // Only play once for better performance
        ...options
      }
    });

    // Set initial state with hardware acceleration
    gsap.set(elements, {
      y: 50,
      opacity: 0,
      force3D: true,
      willChange: "transform, opacity"
    });

    // Animate with stagger for smooth effect
    timeline.to(elements, {
      ...performanceAnimations.smoothReveal,
      stagger: options.stagger || 0.1,
      onComplete: () => {
        // Clean up will-change for performance
        gsap.set(elements, { willChange: "auto" });
      }
    });

    this.animations.push(timeline);
    return timeline;
  }

  // Optimized parallax effect
  createParallaxEffect(element: string | Element, speed: number = 0.5) {
    const parallaxTimeline = gsap.timeline({
      scrollTrigger: {
        trigger: element,
        start: "top bottom",
        end: "bottom top",
        scrub: 1,
        invalidateOnRefresh: true
      }
    });

    parallaxTimeline.fromTo(element, {
      y: -50 * speed,
      force3D: true
    }, {
      y: 50 * speed,
      ease: "none",
      force3D: true
    });

    this.animations.push(parallaxTimeline);
    return parallaxTimeline;
  }

  // Optimized floating animation
  createFloatingAnimation(element: string | Element, options: {
    y?: number;
    duration?: number;
    delay?: number;
  } = {}) {
    const floatAnimation = gsap.to(element, {
      y: options.y || -10,
      duration: options.duration || 2,
      ease: "sine.inOut",
      repeat: -1,
      yoyo: true,
      delay: options.delay || 0,
      force3D: true
    });

    this.animations.push(floatAnimation);
    return floatAnimation;
  }

  // Optimized magnetic effect
  createMagneticEffect(element: Element, strength: number = 0.3) {
    let xTo = gsap.quickTo(element, "x", { duration: 0.6, ease: "power3.out" });
    let yTo = gsap.quickTo(element, "y", { duration: 0.6, ease: "power3.out" });

    const handleMouseMove = (e: MouseEvent) => {
      const rect = element.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      
      const deltaX = (e.clientX - centerX) * strength;
      const deltaY = (e.clientY - centerY) * strength;

      xTo(deltaX);
      yTo(deltaY);
    };

    const handleMouseLeave = () => {
      xTo(0);
      yTo(0);
    };

    element.addEventListener('mousemove', handleMouseMove, { passive: true });
    element.addEventListener('mouseleave', handleMouseLeave, { passive: true });

    return {
      destroy: () => {
        element.removeEventListener('mousemove', handleMouseMove);
        element.removeEventListener('mouseleave', handleMouseLeave);
      }
    };
  }

  // Performance-optimized text animation
  createTextAnimation(element: string | Element, options: {
    text: string;
    duration?: number;
    ease?: string;
  }) {
    return gsap.to(element, {
      text: options.text,
      duration: options.duration || 1,
      ease: options.ease || "none",
      snap: { text: 1 }, // Snap to whole characters for cleaner animation
      force3D: true
    });
  }

  // Cleanup method for better memory management
  destroy() {
    this.animations.forEach(animation => {
      if (animation.kill) animation.kill();
    });
    this.animations = [];
    ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    this.isInitialized = false;
  }
}

// Utility function for lazy loading animations
export const createLazyScrollTrigger = (element: string | Element, callback: () => void) => {
  let hasTriggered = false;
  
  ScrollTrigger.create({
    trigger: element,
    start: "top 90%",
    once: true,
    onEnter: () => {
      if (!hasTriggered) {
        hasTriggered = true;
        callback();
      }
    }
  });
};

// Optimized hover effects for buttons and cards
export const createOptimizedHoverEffects = () => {
  // Use event delegation for better performance
  document.addEventListener('mouseover', (e) => {
    const target = e.target as Element;
    if (target.matches('.hover-scale, [data-hover="scale"]')) {
      gsap.to(target, performanceAnimations.microHover);
    }
  }, { passive: true });

  document.addEventListener('mouseout', (e) => {
    const target = e.target as Element;
    if (target.matches('.hover-scale, [data-hover="scale"]')) {
      gsap.to(target, {
        duration: 0.2,
        scale: 1,
        ease: "power2.out",
        force3D: true
      });
    }
  }, { passive: true });

  document.addEventListener('mousedown', (e) => {
    const target = e.target as Element;
    if (target.matches('.click-scale, [data-click="scale"]')) {
      gsap.to(target, performanceAnimations.microClick);
    }
  }, { passive: true });

  document.addEventListener('mouseup', (e) => {
    const target = e.target as Element;
    if (target.matches('.click-scale, [data-click="scale"]')) {
      gsap.to(target, {
        duration: 0.2,
        scale: 1,
        ease: "back.out(1.2)",
        force3D: true
      });
    }
  }, { passive: true });
};

// Performance monitoring
export const performanceMonitor = {
  startTime: 0,
  endTime: 0,
  
  start() {
    this.startTime = performance.now();
  },
  
  end(label: string = 'Animation') {
    this.endTime = performance.now();
    const duration = this.endTime - this.startTime;
    
    if (duration > 16.67) { // More than one frame at 60fps
      console.warn(`${label} took ${duration.toFixed(2)}ms (> 16.67ms)`);
    }
    
    return duration;
  }
};

export default PerformanceScrollAnimations;