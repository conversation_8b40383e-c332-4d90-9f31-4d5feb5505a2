/**
 * Micro-interactions
 * Subtle animations that enhance user experience
 */

import { gsap, ANIMATION_CONFIG } from './gsap-config';

export class MicroInteractions {
  private static instance: MicroInteractions;
  private activeInteractions: Map<string, gsap.core.Timeline> = new Map();

  static getInstance(): MicroInteractions {
    if (!MicroInteractions.instance) {
      MicroInteractions.instance = new MicroInteractions();
    }
    return MicroInteractions.instance;
  }

  // Button hover animations
  buttonHover(selector: string, type: 'scale' | 'lift' | 'glow' | 'magnetic' = 'scale') {
    const buttons = gsap.utils.toArray(selector) as HTMLElement[];
    
    buttons.forEach((button, index) => {
      const key = `button-${index}`;
      
      switch (type) {
        case 'scale':
          this.scaleHover(button, key);
          break;
        case 'lift':
          this.liftHover(button, key);
          break;
        case 'glow':
          this.glowHover(button, key);
          break;
        case 'magnetic':
          this.magneticHover(button, key);
          break;
      }
    });
  }

  private scaleHover(element: HTMLElement, key: string) {
    const handleMouseEnter = () => {
      const tl = gsap.timeline();
      tl.to(element, {
        scale: 1.05,
        duration: ANIMATION_CONFIG.durations.fast,
        ease: ANIMATION_CONFIG.eases.bounce
      });
      this.activeInteractions.set(key, tl);
    };

    const handleMouseLeave = () => {
      gsap.to(element, {
        scale: 1,
        duration: ANIMATION_CONFIG.durations.fast,
        ease: ANIMATION_CONFIG.eases.bounce
      });
      this.activeInteractions.delete(key);
    };

    element.addEventListener('mouseenter', handleMouseEnter);
    element.addEventListener('mouseleave', handleMouseLeave);
  }

  private liftHover(element: HTMLElement, key: string) {
    const handleMouseEnter = () => {
      const tl = gsap.timeline();
      tl.to(element, {
        y: -5,
        boxShadow: '0 10px 25px rgba(17, 165, 232, 0.3)',
        duration: ANIMATION_CONFIG.durations.fast,
        ease: ANIMATION_CONFIG.eases.smooth
      });
      this.activeInteractions.set(key, tl);
    };

    const handleMouseLeave = () => {
      gsap.to(element, {
        y: 0,
        boxShadow: '0 0 0 rgba(17, 165, 232, 0)',
        duration: ANIMATION_CONFIG.durations.fast,
        ease: ANIMATION_CONFIG.eases.smooth
      });
      this.activeInteractions.delete(key);
    };

    element.addEventListener('mouseenter', handleMouseEnter);
    element.addEventListener('mouseleave', handleMouseLeave);
  }

  private glowHover(element: HTMLElement, key: string) {
    const handleMouseEnter = () => {
      const tl = gsap.timeline();
      tl.to(element, {
        boxShadow: '0 0 20px rgba(17, 165, 232, 0.6), 0 0 40px rgba(17, 165, 232, 0.4)',
        duration: ANIMATION_CONFIG.durations.fast,
        ease: ANIMATION_CONFIG.eases.smooth
      });
      this.activeInteractions.set(key, tl);
    };

    const handleMouseLeave = () => {
      gsap.to(element, {
        boxShadow: '0 0 0 rgba(17, 165, 232, 0)',
        duration: ANIMATION_CONFIG.durations.fast,
        ease: ANIMATION_CONFIG.eases.smooth
      });
      this.activeInteractions.delete(key);
    };

    element.addEventListener('mouseenter', handleMouseEnter);
    element.addEventListener('mouseleave', handleMouseLeave);
  }

  private magneticHover(element: HTMLElement, key: string) {
    const handleMouseMove = (e: MouseEvent) => {
      const rect = element.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      
      const deltaX = (e.clientX - centerX) * 0.3;
      const deltaY = (e.clientY - centerY) * 0.3;
      
      gsap.to(element, {
        x: deltaX,
        y: deltaY,
        duration: 0.3,
        ease: ANIMATION_CONFIG.eases.smooth
      });
    };

    const handleMouseLeave = () => {
      gsap.to(element, {
        x: 0,
        y: 0,
        duration: 0.5,
        ease: ANIMATION_CONFIG.eases.elastic
      });
    };

    element.addEventListener('mousemove', handleMouseMove);
    element.addEventListener('mouseleave', handleMouseLeave);
  }

  // Card hover animations
  cardHover(selector: string, type: 'lift' | 'tilt' | 'scale' = 'lift') {
    const cards = gsap.utils.toArray(selector) as HTMLElement[];
    
    cards.forEach((card, index) => {
      const key = `card-${index}`;
      
      switch (type) {
        case 'lift':
          this.cardLift(card, key);
          break;
        case 'tilt':
          this.cardTilt(card, key);
          break;
        case 'scale':
          this.cardScale(card, key);
          break;
      }
    });
  }

  private cardLift(element: HTMLElement, key: string) {
    const handleMouseEnter = () => {
      const tl = gsap.timeline();
      tl.to(element, {
        y: -10,
        scale: 1.02,
        boxShadow: '0 20px 40px rgba(23, 31, 49, 0.15)',
        duration: ANIMATION_CONFIG.durations.normal,
        ease: ANIMATION_CONFIG.eases.smooth
      });
      this.activeInteractions.set(key, tl);
    };

    const handleMouseLeave = () => {
      gsap.to(element, {
        y: 0,
        scale: 1,
        boxShadow: '0 5px 15px rgba(23, 31, 49, 0.1)',
        duration: ANIMATION_CONFIG.durations.normal,
        ease: ANIMATION_CONFIG.eases.smooth
      });
      this.activeInteractions.delete(key);
    };

    element.addEventListener('mouseenter', handleMouseEnter);
    element.addEventListener('mouseleave', handleMouseLeave);
  }

  private cardTilt(element: HTMLElement, key: string) {
    const handleMouseMove = (e: MouseEvent) => {
      const rect = element.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      
      const rotateX = (e.clientY - centerY) / rect.height * -10;
      const rotateY = (e.clientX - centerX) / rect.width * 10;
      
      gsap.to(element, {
        rotateX,
        rotateY,
        transformPerspective: 1000,
        duration: 0.3,
        ease: ANIMATION_CONFIG.eases.smooth
      });
    };

    const handleMouseLeave = () => {
      gsap.to(element, {
        rotateX: 0,
        rotateY: 0,
        duration: 0.5,
        ease: ANIMATION_CONFIG.eases.smooth
      });
    };

    element.addEventListener('mousemove', handleMouseMove);
    element.addEventListener('mouseleave', handleMouseLeave);
  }

  private cardScale(element: HTMLElement, key: string) {
    const handleMouseEnter = () => {
      const tl = gsap.timeline();
      tl.to(element, {
        scale: 1.05,
        duration: ANIMATION_CONFIG.durations.fast,
        ease: ANIMATION_CONFIG.eases.bounce
      });
      this.activeInteractions.set(key, tl);
    };

    const handleMouseLeave = () => {
      gsap.to(element, {
        scale: 1,
        duration: ANIMATION_CONFIG.durations.fast,
        ease: ANIMATION_CONFIG.eases.bounce
      });
      this.activeInteractions.delete(key);
    };

    element.addEventListener('mouseenter', handleMouseEnter);
    element.addEventListener('mouseleave', handleMouseLeave);
  }

  // Form input animations
  inputFocus(selector: string) {
    const inputs = gsap.utils.toArray(selector) as HTMLInputElement[];
    
    inputs.forEach((input, index) => {
      const handleFocus = () => {
        gsap.to(input, {
          scale: 1.02,
          boxShadow: '0 0 0 2px rgba(17, 165, 232, 0.2)',
          duration: ANIMATION_CONFIG.durations.fast,
          ease: ANIMATION_CONFIG.eases.smooth
        });
      };

      const handleBlur = () => {
        gsap.to(input, {
          scale: 1,
          boxShadow: '0 0 0 0px rgba(17, 165, 232, 0)',
          duration: ANIMATION_CONFIG.durations.fast,
          ease: ANIMATION_CONFIG.eases.smooth
        });
      };

      input.addEventListener('focus', handleFocus);
      input.addEventListener('blur', handleBlur);
    });
  }

  // Loading states
  loadingSpinner(element: HTMLElement | string) {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    return gsap.to(target, {
      rotation: 360,
      duration: 1,
      repeat: -1,
      ease: "none"
    });
  }

  loadingDots(selector: string) {
    const dots = gsap.utils.toArray(selector) as HTMLElement[];
    
    return gsap.to(dots, {
      y: -10,
      duration: 0.5,
      repeat: -1,
      yoyo: true,
      stagger: 0.1,
      ease: ANIMATION_CONFIG.eases.smooth
    });
  }

  // Success/Error state animations
  successPulse(element: HTMLElement | string) {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    const tl = gsap.timeline();
    tl.to(target, {
      scale: 1.1,
      duration: 0.2,
      ease: ANIMATION_CONFIG.eases.bounce
    })
    .to(target, {
      scale: 1,
      duration: 0.3,
      ease: ANIMATION_CONFIG.eases.bounce
    });

    return tl;
  }

  errorShake(element: HTMLElement | string) {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    return gsap.to(target, {
      x: [-10, 10, -5, 5, 0],
      duration: 0.5,
      ease: ANIMATION_CONFIG.eases.sharp
    });
  }

  // Notification animations
  slideInNotification(element: HTMLElement | string, direction: 'top' | 'right' | 'bottom' | 'left' = 'right') {
    const target = typeof element === 'string' ? document.querySelector(element) : element;
    if (!target) return;

    const positions = {
      top: { y: -100 },
      right: { x: 100 },
      bottom: { y: 100 },
      left: { x: -100 }
    };

    const tl = gsap.timeline();
    tl.fromTo(target,
      { opacity: 0, ...positions[direction] },
      {
        opacity: 1,
        x: 0,
        y: 0,
        duration: ANIMATION_CONFIG.durations.normal,
        ease: ANIMATION_CONFIG.eases.bounce
      }
    );

    return tl;
  }

  // Cleanup method
  killInteraction(key: string) {
    const interaction = this.activeInteractions.get(key);
    if (interaction) {
      interaction.kill();
      this.activeInteractions.delete(key);
    }
  }

  killAllInteractions() {
    this.activeInteractions.forEach(interaction => interaction.kill());
    this.activeInteractions.clear();
  }
}

// Export singleton instance
export const microInteractions = MicroInteractions.getInstance();