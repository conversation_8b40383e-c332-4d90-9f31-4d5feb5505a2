/**
 * SME Analytica Brand Colors - The Intuitive Navigator Theme
 * Aligned with the sophisticated intelligence delivered with effortless clarity
 */

export const brandColors = {
  // Core Brand Colors
  primary: {
    // Primary Blue #5f7790 (HSL: 215, 35%, 31%) - Buttons, Primary Elements
    DEFAULT: '#5f7790',
    hue: 215,
    saturation: 35,
    lightness: 31,
    rgb: { r: 95, g: 119, b: 144 },
    hsl: 'hsl(215, 35%, 31%)',
    // Variations
    light: '#7a8fa8',
    lighter: '#95a7c0',
    dark: '#4a6078',
    darker: '#354860'
  },
  
  accent: {
    // Accent Blue #11a5e8 (HSL: 199, 100%, 49.8%) - Links, Highlights, Accents
    DEFAULT: '#11a5e8',
    hue: 199,
    saturation: 100,
    lightness: 49.8,
    rgb: { r: 17, g: 165, b: 232 },
    hsl: 'hsl(199, 100%, 49.8%)',
    // Variations
    light: '#3db5ed',
    lighter: '#69c5f2',
    dark: '#0e8bc4',
    darker: '#0b71a0'
  },
  
  background: {
    // Dark Background #171f31 (HSL: 215, 35%, 15%) - Main Background
    DEFAULT: '#171f31',
    hue: 215,
    saturation: 35,
    lightness: 15,
    rgb: { r: 23, g: 31, b: 49 },
    hsl: 'hsl(215, 35%, 15%)',
    // Variations
    light: '#202940',
    lighter: '#293350',
    dark: '#0e1428',
    darker: '#050a1f'
  },
  
  text: {
    // Light Text #d5dce2 (HSL: 210, 20%, 85%) - Primary Text
    DEFAULT: '#d5dce2',
    hue: 210,
    saturation: 20,
    lightness: 85,
    rgb: { r: 213, g: 220, b: 226 },
    hsl: 'hsl(210, 20%, 85%)',
    // Variations
    light: '#e8ecf0',
    lighter: '#f2f4f6',
    dark: '#c2ccd4',
    darker: '#afbcc6',
    muted: '#a4acb6' // For secondary text
  },
  
  // Visualization Colors (Aligned with Brand Hues)
  visualization: {
    // Core AI Intelligence (Primary Blue based)
    coreAI: {
      base: 'hsl(215, 100%, 95%)',
      mid: 'hsl(215, 80%, 85%)',
      deep: 'hsl(215, 60%, 70%)',
      deepest: 'hsl(215, 35%, 50%)'
    },
    
    // Data Streams (Accent Blue based)
    dataStreams: {
      base: 'hsl(199, 80%, 75%)',
      mid: 'hsl(199, 70%, 65%)',
      deep: 'hsl(199, 60%, 55%)',
      deepest: 'hsl(199, 50%, 45%)'
    },
    
    // Insights (Golden-blue for valuable insights)
    insights: {
      golden: 'hsl(40, 100%, 90%)',
      goldenMid: 'hsl(40, 90%, 80%)',
      accentBlue: 'hsl(199, 90%, 80%)',
      accentBlueMid: 'hsl(199, 80%, 70%)'
    },
    
    // Growth Paths (Deeper Accent Blue for expansion)
    growthPaths: {
      base: 'hsl(199, 80%, 65%)',
      mid: 'hsl(199, 70%, 55%)',
      deep: 'hsl(199, 60%, 45%)',
      deepest: 'hsl(199, 50%, 35%)'
    }
  },
  
  // UI State Colors
  states: {
    success: '#22c55e',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#11a5e8' // Using accent blue
  },
  
  // Utility Colors
  white: '#ffffff',
  black: '#000000',
  transparent: 'transparent'
} as const;

// CSS Custom Properties Generator
export const generateCSSVariables = () => {
  return {
    // Core Brand Colors
    '--color-primary': brandColors.primary.DEFAULT,
    '--color-primary-light': brandColors.primary.light,
    '--color-primary-dark': brandColors.primary.dark,
    '--color-accent': brandColors.accent.DEFAULT,
    '--color-accent-light': brandColors.accent.light,
    '--color-accent-dark': brandColors.accent.dark,
    '--color-background': brandColors.background.DEFAULT,
    '--color-background-light': brandColors.background.light,
    '--color-text': brandColors.text.DEFAULT,
    '--color-text-muted': brandColors.text.muted,
    
    // Visualization Colors
    '--color-core-ai': brandColors.visualization.coreAI.base,
    '--color-data-streams': brandColors.visualization.dataStreams.base,
    '--color-insights': brandColors.visualization.insights.golden,
    '--color-growth-paths': brandColors.visualization.growthPaths.base,
    
    // State Colors
    '--color-success': brandColors.states.success,
    '--color-warning': brandColors.states.warning,
    '--color-error': brandColors.states.error,
    '--color-info': brandColors.states.info
  };
};

// Tailwind CSS Color Extensions
export const tailwindColorExtensions = {
  'brand-primary': {
    DEFAULT: brandColors.primary.DEFAULT,
    light: brandColors.primary.light,
    lighter: brandColors.primary.lighter,
    dark: brandColors.primary.dark,
    darker: brandColors.primary.darker
  },
  'brand-accent': {
    DEFAULT: brandColors.accent.DEFAULT,
    light: brandColors.accent.light,
    lighter: brandColors.accent.lighter,
    dark: brandColors.accent.dark,
    darker: brandColors.accent.darker
  },
  'brand-background': {
    DEFAULT: brandColors.background.DEFAULT,
    light: brandColors.background.light,
    lighter: brandColors.background.lighter,
    dark: brandColors.background.dark,
    darker: brandColors.background.darker
  },
  'brand-text': {
    DEFAULT: brandColors.text.DEFAULT,
    light: brandColors.text.light,
    lighter: brandColors.text.lighter,
    dark: brandColors.text.dark,
    darker: brandColors.text.darker,
    muted: brandColors.text.muted
  }
};

export default brandColors;
