/**
 * Smooth Scroll Implementation
 * Using Lenis for butter-smooth scrolling experience
 */

import Lenis from '@studio-freight/lenis';
import { gsap } from 'gsap';

let lenis: Lenis | null = null;

export const initSmoothScroll = () => {
  if (typeof window === 'undefined' || lenis) return;

  // Initialize Lenis
  lenis = new Lenis({
    duration: 1.2,
    easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
    direction: 'vertical',
    gestureDirection: 'vertical',
    smooth: true,
    mouseMultiplier: 1,
    smoothTouch: false,
    touchMultiplier: 2,
    infinite: false,
  });

  // Integrate with GSAP ScrollTrigger
  lenis.on('scroll', (e) => {
    if (window.ScrollTrigger) {
      window.ScrollTrigger.update();
    }
  });

  // GSAP RAF integration
  gsap.ticker.add((time) => {
    lenis?.raf(time * 1000);
  });

  gsap.ticker.lagSmoothing(0);

  return lenis;
};

export const destroySmoothScroll = () => {
  if (lenis) {
    lenis.destroy();
    lenis = null;
  }
};

export const scrollTo = (target: string | number, options?: { offset?: number; duration?: number }) => {
  if (!lenis) return;

  lenis.scrollTo(target, {
    offset: options?.offset || 0,
    duration: options?.duration || 1.2,
    easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t))
  });
};

export const getLenis = () => lenis;