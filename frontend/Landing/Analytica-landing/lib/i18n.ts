/**
 * Bilingual ES/EN Language System for SME Analytica
 * Supporting Spanish (primary market) and English (universal)
 */

export const languages = {
  es: 'Español',
  en: 'English'
} as const;

export type Language = keyof typeof languages;

// Content translations aligned with "The Intuitive Navigator" concept
export const content = {
  hero: {
    headline: {
      es: "Navega el Crecimiento de tu Empresa con Insights de IA sin Esfuerzo",
      en: "Navigate Your Business Growth with Effortless AI Insights"
    },
    subheadline: {
      es: "SME Analytica transforma tus datos complejos en recomendaciones claras y accionables. Sin complicaciones técnicas, solo crecimiento inteligente.",
      en: "SME Analytica transforms your complex data into clear, actionable recommendations. No tech-headaches, just intelligent growth."
    },
    cta: {
      primary: {
        es: "Comenzar - Descubre tu Ruta de Crecimiento",
        en: "Get Started - Discover Your Growth Path"
      },
      secondary: {
        es: "Ver Características",
        en: "View Features"
      }
    },
    tagline: {
      es: "El Navegador Intuitivo para PYMES",
      en: "The Intuitive Navigator for SMEs"
    },
    description: {
      es: "AI-Powered Business Intelligence",
      en: "AI-Powered Business Intelligence"
    }
  },
  navigation: {
    howItWorks: {
      es: "Cómo Funciona",
      en: "How It Works"
    },
    solutions: {
      es: "Soluciones", 
      en: "Solutions"
    },
    features: {
      es: "Características",
      en: "Features"
    },
    useCases: {
      es: "Casos de Uso",
      en: "Use Cases"
    },
    about: {
      es: "Acerca de",
      en: "About Us"
    },
    blog: {
      es: "Blog",
      en: "Blog"
    },
    pricing: {
      es: "Precios",
      en: "Pricing"
    },
    contact: {
      es: "Contacto",
      en: "Contact"
    }
  },
  howItWorks: {
    title: {
      es: "El Viaje de 3 Pasos hacia el Crecimiento",
      en: "The 3-Step Journey to Growth"
    },
    steps: {
      connect: {
        title: {
          es: "Conectar",
          en: "Connect"
        },
        description: {
          es: "Conecta de forma segura tus fuentes de datos empresariales",
          en: "Securely link your business data sources"
        }
      },
      analyze: {
        title: {
          es: "Analizar", 
          en: "Analyze"
        },
        description: {
          es: "Nuestra IA sintetiza inteligentemente la información",
          en: "Our AI intelligently synthesizes information"
        }
      },
      grow: {
        title: {
          es: "Crecer",
          en: "Grow"
        },
        description: {
          es: "Recibe recomendaciones claras y accionables para el crecimiento",
          en: "Receive clear, actionable recommendations for growth"
        }
      }
    }
  },
  stats: {
    roi: {
      value: "300%",
      label: {
        es: "ROI Promedio",
        en: "Average ROI"
      }
    },
    setup: {
      value: "48hrs",
      label: {
        es: "Tiempo de Configuración",
        en: "Setup Time"
      }
    },
    companies: {
      value: "1000+",
      label: {
        es: "PYMES Atendidas",
        en: "SMEs Served"
      }
    },
    industries: {
      value: "15",
      label: {
        es: "Industrias",
        en: "Industries"
      }
    }
  },
  leftText: {
    es: "En los datos<br />descubrimos<br />patrones ocultos",
    en: "In data<br />we discover<br />hidden patterns"
  },
  rightText: {
    es: "A través de IA<br/>desbloqueamos<br/>potencial empresarial",
    en: "Through AI<br/>we unlock<br/>business potential"
  },
  bottomText: {
    es: "Potenciado por SME Analytica AI",
    en: "Powered by SME Analytica AI"
  },
  benefits: {
    title: {
      es: "Beneficios Clave para tu Negocio",
      en: "Key Benefits for Your Business"
    },
    subtitle: {
      es: "Descubre cómo SME Analytica simplifica la complejidad y potencia el crecimiento",
      en: "Discover how SME Analytica simplifies complexity and powers growth"
    },
    items: {
      effortless: {
        title: {
          es: "Navegación Sin Esfuerzo",
          en: "Effortless Navigation"
        },
        description: {
          es: "Interfaz intuitiva que convierte datos complejos en insights claros",
          en: "Intuitive interface that turns complex data into clear insights"
        }
      },
      intelligent: {
        title: {
          es: "Análisis Inteligente",
          en: "Intelligent Analysis"
        },
        description: {
          es: "IA que sintetiza información y revela patrones ocultos automáticamente",
          en: "AI that synthesizes information and reveals hidden patterns automatically"
        }
      },
      realtime: {
        title: {
          es: "Insights en Tiempo Real",
          en: "Real-time Insights"
        },
        description: {
          es: "Decisiones basadas en datos actualizados al instante",
          en: "Decisions based on instantly updated data"
        }
      },
      growth: {
        title: {
          es: "Crecimiento Acelerado",
          en: "Accelerated Growth"
        },
        description: {
          es: "Recomendaciones accionables que impulsan resultados medibles",
          en: "Actionable recommendations that drive measurable results"
        }
      },
      security: {
        title: {
          es: "Seguridad Garantizada",
          en: "Guaranteed Security"
        },
        description: {
          es: "Protección de datos empresarial con estándares bancarios",
          en: "Enterprise data protection with banking-grade standards"
        }
      },
      support: {
        title: {
          es: "Soporte Especializado",
          en: "Expert Support"
        },
        description: {
          es: "Acompañamiento personalizado en tu viaje de transformación digital",
          en: "Personalized guidance on your digital transformation journey"
        }
      }
    }
  }
} as const;

// Language detection and management
export class LanguageManager {
  private static currentLanguage: Language = 'es'; // Default to Spanish

  static getCurrentLanguage(): Language {
    return this.currentLanguage;
  }

  static setLanguage(lang: Language): void {
    this.currentLanguage = lang;
    if (typeof window !== 'undefined') {
      localStorage.setItem('sme-language', lang);
      document.documentElement.lang = lang;
    }
  }

  static detectLanguage(): Language {
    if (typeof window === 'undefined') return 'es';
    
    // Check localStorage first
    const stored = localStorage.getItem('sme-language') as Language;
    if (stored && Object.keys(languages).includes(stored)) {
      return stored;
    }

    // Auto-detect from browser
    const browserLang = navigator.language.split('-')[0] as Language;
    return Object.keys(languages).includes(browserLang) ? browserLang : 'es';
  }

  static initializeLanguage(): Language {
    const detected = this.detectLanguage();
    this.setLanguage(detected);
    return detected;
  }

  static getContent<T extends keyof typeof content>(
    key: T,
    lang?: Language
  ): typeof content[T] {
    const currentLang = lang || this.getCurrentLanguage();
    return content[key];
  }

  static getText<T extends keyof typeof content>(
    path: string,
    lang?: Language
  ): string {
    const currentLang = lang || this.getCurrentLanguage();
    const keys = path.split('.');
    let obj: any = content;
    
    for (const key of keys) {
      obj = obj[key];
      if (!obj) return path; // Fallback to path if not found
    }
    
    return obj[currentLang] || obj['es'] || path;
  }
}

export default LanguageManager;