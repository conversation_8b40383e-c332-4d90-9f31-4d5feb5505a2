'use client';

import { motion } from 'framer-motion';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import { Spotlight } from '../../components/ui/spotlight-new';
import { GradientButton } from '../../components/ui/gradient-button';

export default function PrivacyPolicy() {
  return (
    <>
      <Navbar />
      <div className="min-h-screen bg-[#171f31] pt-32 relative overflow-hidden">
        <div className="absolute inset-0 z-0">
          <Spotlight
            gradientFirst="radial-gradient(68.54% 68.72% at 55.02% 31.46%, hsla(200, 100%, 85%, .04) 0, hsla(200, 100%, 55%, .01) 50%, hsla(200, 100%, 45%, 0) 80%)"
            gradientSecond="radial-gradient(50% 50% at 50% 50%, hsla(200, 100%, 85%, .03) 0, hsla(200, 100%, 55%, .01) 80%, transparent 100%)"
            gradientThird="radial-gradient(50% 50% at 50% 50%, hsla(200, 100%, 85%, .02) 0, hsla(200, 100%, 45%, .01) 80%, transparent 100%)"
          />
        </div>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="max-w-4xl mx-auto"
          >
            <h1 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-[#11a5e8] to-[#5f7790] mb-8">
              Privacy Policy
            </h1>

            <div className="space-y-8">
              <section className="bg-[#171f31]/50 p-6 rounded-lg border border-[#5f7790]/20">
                <p className="text-[#d5dce2]">
                  SME Analytica is committed to protecting your privacy and handling your data in compliance with the EU General Data Protection Regulation (GDPR). This Privacy Policy outlines what data we collect, how we use it, and your rights.
                </p>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-[#11a5e8] mb-4">
                  Data Collection
                </h2>
                <p className="text-[#d5dce2] mb-4">
                  We collect only essential data needed to provide our analytics services:
                </p>
                <ul className="list-disc pl-6 text-[#d5dce2] space-y-2">
                  <li>Name, email, and company information</li>
                  <li>Website usage via cookies and analytics</li>
                  <li>Optional business data you choose to connect</li>
                </ul>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-[#11a5e8] mb-4">
                  Data Processing
                </h2>
                <p className="text-[#d5dce2] mb-4">
                  Your data is processed in accordance with GDPR Article 6(1):
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-[#171f31]/50 p-4 rounded-lg border border-[#5f7790]/20">
                    <h3 className="text-[#11a5e8] font-medium mb-2">Consent</h3>
                    <p className="text-[#d5dce2]/80 text-sm">With your explicit permission</p>
                  </div>
                  <div className="bg-[#171f31]/50 p-4 rounded-lg border border-[#5f7790]/20">
                    <h3 className="text-[#11a5e8] font-medium mb-2">Contract</h3>
                    <p className="text-[#d5dce2]/80 text-sm">For fulfilling our service agreement</p>
                  </div>
                  <div className="bg-[#171f31]/50 p-4 rounded-lg border border-[#5f7790]/20">
                    <h3 className="text-[#11a5e8] font-medium mb-2">Legitimate Interest</h3>
                    <p className="text-[#d5dce2]/80 text-sm">For business purposes with safeguards</p>
                  </div>
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-[#11a5e8] mb-4">
                  Your Rights
                </h2>
                <p className="text-[#d5dce2] mb-4">
                  Under GDPR, you have the right to:
                </p>
                <ul className="list-disc pl-6 text-[#d5dce2] space-y-2">
                  <li>Access your data</li>
                  <li>Correct inaccurate data</li>
                  <li>Request data deletion</li>
                  <li>Object to data processing</li>
                  <li>Data portability</li>
                </ul>
                <div className="mt-4">
                  <a href="/gdpr" className="text-[#11a5e8] hover:text-[#11a5e8]/80 transition-colors flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 9l3 3m0 0l-3 3m3-3H8m13 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Learn more about your GDPR rights
                  </a>
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-[#11a5e8] mb-4">
                  Data Security
                </h2>
                <p className="text-[#d5dce2] mb-4">
                  We implement strong security measures to protect your data:
                </p>
                <div className="bg-[#171f31]/50 p-4 rounded-lg border border-[#5f7790]/20">
                  <ul className="list-disc pl-6 text-[#d5dce2] space-y-2">
                    <li>Encryption of data in transit and at rest</li>
                    <li>Secure servers and access controls</li>
                    <li>Regular audits and vulnerability scans</li>
                    <li>Staff training on data protection</li>
                    <li>Incident response procedures</li>
                  </ul>
                </div>
              </section>

              <div className="my-8 h-[1px] w-full bg-gradient-to-r from-transparent via-[#5f7790]/30 to-transparent" />

              <section className="bg-[#171f31]/50 p-6 rounded-lg border border-[#5f7790]/20">
                <h2 className="text-2xl font-semibold text-[#11a5e8] mb-4">
                  Contact Us
                </h2>
                <p className="text-[#d5dce2]">
                  For questions about this policy or your data rights, contact our Data Protection Officer at{' '}
                  <a
                    href="mailto:<EMAIL>"
                    className="text-[#11a5e8] hover:text-[#11a5e8]/80 transition-colors"
                  >
                    <EMAIL>
                  </a>
                </p>
                <div className="mt-6 flex flex-wrap gap-4">
                  <GradientButton href="/terms" size="sm">
                    Terms of Service
                  </GradientButton>
                  <GradientButton href="/gdpr" size="sm">
                    GDPR Compliance
                  </GradientButton>
                </div>
              </section>
            </div>
          </motion.div>
        </div>
      </div>
      <Footer />
    </>
  );
}