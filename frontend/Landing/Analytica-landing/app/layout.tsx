import type { Metada<PERSON>, Viewport } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import ParallaxProviderLayout from './providers/ParallaxProviderLayout';
import { OrganizationStructuredData, SoftwareStructuredData } from '../components/structured-data';
import { AIAgentAnalytics } from '../components/ai-agent-analytics';
import { LanguageProvider } from '../lib/LanguageProvider';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  metadataBase: new URL('https://smeanalytica.dev'),
  title: {
    default: 'SME Analytica: Insights con IA para Hacer Crecer tu Pequeña Empresa de Forma Inteligente',
    template: '%s | SME Analytica'
  },
  description: 'Automatiza el análisis de datos, predice tendencias y toma decisiones con confianza usando análisis IA diseñados para PYMES. Sin complejidad, solo resultados. 300% ROI promedio en 6 meses.',
  keywords: [
    'análisis IA',
    'plataforma inteligencia empresarial',
    'análisis PYME',
    'crecimiento pequeñas empresas',
    'análisis medianas empresas',
    'análisis tiempo real',
    'insights predictivos',
    'optimización empresarial',
    'análisis datos clientes',
    'pronóstico mercado',
    'estrategias precios',
    'análisis competitivo',
    'optimización rentabilidad',
    'toma decisiones basada datos',
    'software análisis avanzado',
    'insights empresariales automatizados',
    'pronóstico ventas',
    'herramientas inteligencia empresarial',
    'análisis impulsado IA',
    'estrategias crecimiento PYME'
  ],
  authors: [{ name: 'Equipo SME Analytica', url: 'https://smeanalytica.dev/about' }],
  creator: 'SME Analytica',
  publisher: 'SME Analytica',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'es_ES',
    url: 'https://smeanalytica.dev',
    siteName: 'SME Analytica',
    title: 'SME Analytica: Insights con IA para Hacer Crecer tu Pequeña Empresa de Forma Inteligente',
    description: 'Automatiza el análisis de datos, predice tendencias y toma decisiones con confianza usando análisis IA diseñados para PYMES. 300% ROI promedio en 6 meses.',
    images: [
      {
        url: 'https://smeanalytica.dev/images/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'SME Analytica – Análisis IA Avanzado e Insights',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'SME Analytica – Plataforma de Análisis e Inteligencia Empresarial Impulsada por IA',
    description: 'Desbloquea análisis potentes impulsados por IA para impulsar el rendimiento de tu PYME, optimizar precios y anticipar tendencias del mercado de manera efectiva.',
    creator: '@smeanalytica',
    site: 'https://x.com/smeanalytica?s=21',
    images: ['https://x.com/smeanalytica/photo'],
  },
  verification: {
    google: 'your-google-verification-code',
  },
  category: 'Tecnología, Inteligencia Empresarial, Análisis',
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: 'cover',
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#f9fafb' },
    { media: '(prefers-color-scheme: dark)', color: '#111827' }
  ]
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="es" suppressHydrationWarning className="h-full">
      <head>
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        <link rel="canonical" href="https://smeanalytica.dev" />
        <meta name="application-name" content="SME Analytica" />
        <meta name="apple-mobile-web-app-title" content="SME Analytica" />
        <link rel="icon" href="/logo.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/images/logo.png" />
        <link rel="manifest" href="/manifest.json" />
      </head>
      <body className={`${inter.className} antialiased min-h-full bg-[#171f31] pt-24`}>
        <OrganizationStructuredData />
        <SoftwareStructuredData />
        <AIAgentAnalytics page="home" category="landing" />
        <LanguageProvider>
          <ParallaxProviderLayout>
            {children}
          </ParallaxProviderLayout>
        </LanguageProvider>
      </body>
    </html>
  );
}