'use client';

import { motion } from 'framer-motion';
import { FiTarget, FiEye, FiHeart, FiUsers, FiTrendingUp, FiAward } from 'react-icons/fi';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';

const teamMembers = [
  {
    name: "<PERSON>",
    role: "CEO & Co-Founder",
    bio: "Passionate software engineer and ML/AI enthusiast with a knack for building scalable and efficient solutions. I'm a quick learner and always looking for new challenges.",
    image: "/images/team/sarah.jpg"
  },
  {
    name: "<PERSON><PERSON>",
    role: "Co-Founder & Marketing Lead",
    bio: "CEO of House of Young, <PERSON><PERSON> is a serial entrepreneur and investor. He is a passionate about building businesses that solve real world problems and make a difference.",
    image: "/images/team/marcus.jpg"
  },
  {
    name: "<PERSON>",
    role: "Chief Consultant",
    bio: "<PERSON> is a seasoned business consultant with a passion for helping businesses grow and scale. He is a quick learner and always looking for new challenges.",
    image: "/images/team/emily.jpg"
  },
  {
    name: "<PERSON>",
    role: "Head of Operations",
    bio: "<PERSON> is a seasoned operations manager with a passion for helping businesses grow and scale. She is a quick learner and always looking for new challenges.",
    image: "/images/team/david.jpg"
  }
];

const milestones = [
  {
    year: "Late 2024",
    title: "Company Founded",
    description: "Started with a mission to make AI analytics accessible to every small business"
  },
  {
    year: "First quarter 2025",
    title: "First 10 Customers",
    description: "Reached our first milestone with SMEs across 5 different industries"
  },
  {
    year: "Second quarter 2025",
    title: "Series A Funding",
    description: "Raised $5M to accelerate product development and expand our team"
  },
  {
    year: "Third quarter 2025",
    title: "100+ SMEs Served",
    description: "Now helping over 100 small businesses across 5 industries grow with AI"
  }
];

const values = [
  {
    icon: FiHeart,
    title: "SME-First Approach",
    description: "Every feature we build is designed specifically for the unique needs and constraints of small and medium enterprises."
  },
  {
    icon: FiUsers,
    title: "Accessibility",
    description: "We believe powerful analytics shouldn't require a PhD in data science. Our platform is built for business owners, not data scientists."
  },
  {
    icon: FiTrendingUp,
    title: "Real Impact",
    description: "We measure our success by the growth and profitability improvements our customers achieve, not just our own metrics."
  },
  {
    icon: FiAward,
    title: "Transparency",
    description: "No hidden fees, no complex contracts, no confusing jargon. We believe in honest, straightforward business relationships."
  }
];

const stats = [
  { number: "10+", label: "SMEs Served" },
  { number: "15", label: "Industries" },
  { number: "300%", label: "Average ROI" },
  { number: "48hrs", label: "Setup Time" }
];

export default function AboutClient() {
  return (
    <div className="min-h-screen bg-[#171f31]">
      <Navbar />
      
      {/* Hero Section */}
      <section className="pt-32 pb-20 px-6">
        <div className="container mx-auto max-w-6xl">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h1 className="text-5xl md:text-6xl font-bold text-[#d5dce2] mb-6">
              Empowering SMEs with{' '}
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#11a5e8] to-[#5f7790]">
                AI Analytics
              </span>
            </h1>
            <p className="text-xl text-[#d5dce2]/80 max-w-3xl mx-auto">
              We're on a mission to democratize powerful AI analytics, making enterprise-level 
              insights accessible and affordable for every small and medium business.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Mission, Vision, Values */}
      <section className="pb-20 px-6">
        <div className="container mx-auto max-w-7xl">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-20">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="bg-[#1e2a3e] rounded-2xl p-8 border border-[#2a3441]"
            >
              <div className="p-3 bg-[#11a5e8]/10 rounded-xl w-fit mb-6">
                <FiTarget className="w-8 h-8 text-[#11a5e8]" />
              </div>
              <h3 className="text-2xl font-bold text-[#d5dce2] mb-4">Our Mission</h3>
              <p className="text-[#d5dce2]/80">
                To level the playing field by giving small and medium enterprises access to the same 
                powerful AI analytics tools that large corporations use to drive growth and profitability.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-[#1e2a3e] rounded-2xl p-8 border border-[#2a3441]"
            >
              <div className="p-3 bg-[#11a5e8]/10 rounded-xl w-fit mb-6">
                <FiEye className="w-8 h-8 text-[#11a5e8]" />
              </div>
              <h3 className="text-2xl font-bold text-[#d5dce2] mb-4">Our Vision</h3>
              <p className="text-[#d5dce2]/80">
                A world where every small business owner has the insights they need to make confident, 
                data-driven decisions that accelerate growth and ensure long-term success.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="bg-[#1e2a3e] rounded-2xl p-8 border border-[#2a3441]"
            >
              <div className="p-3 bg-[#11a5e8]/10 rounded-xl w-fit mb-6">
                <FiHeart className="w-8 h-8 text-[#11a5e8]" />
              </div>
              <h3 className="text-2xl font-bold text-[#d5dce2] mb-4">Our Values</h3>
              <p className="text-[#d5dce2]/80">
                We believe in transparency, accessibility, and real impact. Every decision we make 
                is guided by what's best for small business owners and their unique challenges.
              </p>
            </motion.div>
          </div>

          {/* Core Values */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="flex items-start gap-4"
              >
                <div className="p-3 bg-[#11a5e8]/10 rounded-xl">
                  <value.icon className="w-6 h-6 text-[#11a5e8]" />
                </div>
                <div>
                  <h4 className="text-xl font-semibold text-[#d5dce2] mb-2">{value.title}</h4>
                  <p className="text-[#d5dce2]/70">{value.description}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Our Story */}
      <section className="py-20 px-6 bg-[#1e2a3e]/50">
        <div className="container mx-auto max-w-6xl">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-[#d5dce2] mb-6">Our Story</h2>
            <p className="text-xl text-[#d5dce2]/80 max-w-3xl mx-auto">
              SME Analytica was born from a simple observation: small businesses generate tons of data 
              but lack the tools to turn it into actionable insights.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
            >
              <p className="text-[#d5dce2]/80 mb-6">
                After years of working with Fortune 500 companies and seeing the incredible impact of 
                data-driven decision making, our founders realized that small businesses were being 
                left behind in the analytics revolution.
              </p>
              <p className="text-[#d5dce2]/80 mb-6">
                Traditional business intelligence tools were either too expensive, too complex, or 
                simply not designed for the unique needs of SMEs. We set out to change that.
              </p>
              <p className="text-[#d5dce2]/80">
                Today, we're proud to serve over 1000 small and medium enterprises across 15 industries, 
                helping them achieve an average ROI of 300% through smarter, data-driven decisions.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="space-y-6"
            >
              {milestones.map((milestone, index) => (
                <div key={index} className="flex items-start gap-4">
                  <div className="w-16 h-16 bg-[#11a5e8]/10 rounded-xl flex items-center justify-center flex-shrink-0">
                    <span className="text-[#11a5e8] font-bold">{milestone.year}</span>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-[#d5dce2] mb-1">{milestone.title}</h4>
                    <p className="text-[#d5dce2]/70">{milestone.description}</p>
                  </div>
                </div>
              ))}
            </motion.div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20 px-6">
        <div className="container mx-auto max-w-7xl">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-[#d5dce2] mb-6">Meet Our Team</h2>
            <p className="text-xl text-[#d5dce2]/80">
              Passionate experts dedicated to empowering small business success
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {teamMembers.map((member, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-[#1e2a3e] rounded-2xl p-6 border border-[#2a3441] text-center"
              >
                <div className="w-24 h-24 bg-[#11a5e8]/10 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-2xl font-bold text-[#11a5e8]">
                    {member.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
                <h4 className="text-xl font-semibold text-[#d5dce2] mb-1">{member.name}</h4>
                <p className="text-[#11a5e8] mb-3">{member.role}</p>
                <p className="text-[#d5dce2]/70 text-sm">{member.bio}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 px-6 bg-[#1e2a3e]/50">
        <div className="container mx-auto max-w-6xl">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-bold text-[#d5dce2] mb-6">Our Impact</h2>
            <p className="text-xl text-[#d5dce2]/80">
              Numbers that reflect our commitment to SME success
            </p>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.2 }}
              >
                <div className="text-4xl md:text-5xl font-bold text-[#11a5e8] mb-2">{stat.number}</div>
                <div className="text-[#d5dce2]/80">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-6">
        <div className="container mx-auto max-w-4xl text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="bg-gradient-to-r from-[#11a5e8]/10 to-[#5f7790]/10 rounded-2xl p-12 border border-[#2a3441]"
          >
            <h2 className="text-4xl font-bold text-[#d5dce2] mb-6">
              Ready to Join Our Mission?
            </h2>
            <p className="text-xl text-[#d5dce2]/80 mb-8">
              Whether you're a small business looking to grow or a talented individual wanting to make an impact, 
              we'd love to hear from you.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.a
                href="https://calendly.com/smeanalytica/30min"
                target="_blank"
                rel="noopener noreferrer"
                className="px-8 py-4 bg-[#11a5e8] text-white rounded-full text-lg font-medium hover:bg-[#0e8bc7] transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Schedule a Demo
              </motion.a>
              <motion.a
                href="/contact"
                className="px-8 py-4 bg-transparent text-[#d5dce2] rounded-full text-lg font-medium border-2 border-[#5f7790] hover:bg-[#5f7790] transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Get in Touch
              </motion.a>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
} 