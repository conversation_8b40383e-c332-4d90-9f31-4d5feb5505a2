'use client';

import { motion } from 'framer-motion';
import { 
  FiTrendingUp, 
  FiDollarSign, 
  FiTarget, 
  FiUsers, 
  FiPackage, 
  FiBarChart,
  FiPieChart,
  FiActivity
} from 'react-icons/fi';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';

const features = [
  {
    icon: FiDollarSign,
    title: "Análisis Predictivo de Flujo de Caja",
    description: "Nunca más te quedes sin efectivo. Nuestra IA analiza tus datos históricos, patrones estacionales y tendencias del mercado para predecir tu flujo de caja hasta 12 meses adelante.",
    benefits: [
      "Predice déficits de efectivo antes de que ocurran",
      "Optimiza el tiempo de pagos y cobranzas",
      "Planifica inversiones y gastos con confianza",
      "Reduce el estrés financiero e incertidumbre"
    ],
    keywords: "predicción flujo de caja pequeñas empresas, pronóstico financiero IA"
  },
  {
    icon: FiTrendingUp,
    title: "Pronóstico de Ventas y Análisis de Tendencias",
    description: "Toma decisiones de ventas basadas en datos con pronósticos impulsados por IA que identifican tendencias, patrones estacionales y oportunidades de crecimiento.",
    benefits: [
      "Predicciones precisas de ventas para mejor planificación",
      "Identifica tus productos/servicios de mejor rendimiento",
      "Detecta tendencias emergentes antes que la competencia",
      "Optimiza niveles de inventario y personal"
    ],
    keywords: "pronóstico ventas IA pequeñas empresas, análisis tendencias ventas"
  },
  {
    icon: FiTarget,
    title: "Optimización de ROI de Marketing",
    description: "Deja de desperdiciar dinero en marketing inefectivo. Nuestra IA rastrea cada euro gastado y te muestra exactamente qué campañas generan resultados reales.",
    benefits: [
      "Rastrea ROI en todos los canales de marketing",
      "Identifica tus fuentes más rentables de adquisición de clientes",
      "Optimiza el gasto publicitario para máximo retorno",
      "Elimina gastos de marketing desperdiciados"
    ],
    keywords: "optimización ROI marketing PYME, análisis marketing IA"
  },
  {
    icon: FiUsers,
    title: "Segmentación de Clientes y Predicción de Abandono",
    description: "Entiende a tus clientes como nunca antes. La IA segmenta tu base de clientes y predice quién es probable que se vaya, para que puedas actuar proactivamente.",
    benefits: [
      "Identifica tus segmentos de clientes más valiosos",
      "Predice y previene el abandono de clientes",
      "Personaliza marketing para mejor engagement",
      "Aumenta el valor de vida del cliente"
    ],
    keywords: "predicción abandono clientes pequeñas empresas, segmentación clientes IA"
  },
  {
    icon: FiPackage,
    title: "Insights de Gestión de Inventario",
    description: "Perfecto para PYMES de retail y e-commerce. La IA optimiza tus niveles de inventario, predice demanda y previene situaciones de desabastecimiento o sobrestock.",
    benefits: [
      "Optimiza niveles de inventario automáticamente",
      "Predice demanda para cada producto",
      "Reduce costos de almacenamiento y desperdicio",
      "Nunca pierdas ventas por desabastecimiento"
    ],
    keywords: "gestión inventario IA retail, optimización stock pequeñas empresas"
  },
  {
    icon: FiBarChart,
    title: "Análisis Competitivo e Inteligencia de Mercado",
    description: "Mantente adelante de la competencia con análisis de mercado impulsado por IA que rastrea precios de competidores, tendencias y oportunidades.",
    benefits: [
      "Monitorea precios y estrategias de competidores",
      "Identifica brechas y oportunidades del mercado",
      "Compara tu rendimiento",
      "Toma decisiones estratégicas con confianza"
    ],
    keywords: "análisis competitivo pequeñas empresas, inteligencia mercado IA"
  },
  {
    icon: FiPieChart,
    title: "Análisis de Rentabilidad por Producto/Servicio",
    description: "Descubre qué productos o servicios realmente te generan dinero. Nuestra IA desglosa la rentabilidad a niveles granulares.",
    benefits: [
      "Identifica tus ofertas más rentables",
      "Elimina productos/servicios que generan pérdidas",
      "Optimiza estrategias de precios",
      "Enfoca recursos en oportunidades de alto margen"
    ],
    keywords: "análisis rentabilidad producto, insights rentabilidad empresarial IA"
  },
  {
    icon: FiActivity,
    title: "Paneles de Rendimiento en Tiempo Real",
    description: "Monitorea la salud de tu negocio 24/7 con paneles personalizables que muestran las métricas que más importan a tu PYME.",
    benefits: [
      "Visibilidad en tiempo real de métricas clave",
      "Paneles personalizables para tu industria",
      "Acceso móvil para monitoreo en movimiento",
      "Alertas automatizadas para cambios importantes"
    ],
    keywords: "panel empresarial tiempo real PYME, dashboard análisis IA"
  }
];

export default function FeaturesClient() {
  return (
    <div className="min-h-screen bg-[#171f31]">
      <Navbar />
      
      {/* Hero Section */}
      <section className="pt-32 pb-20 px-6">
        <div className="container mx-auto max-w-6xl">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h1 className="text-5xl md:text-6xl font-bold text-[#d5dce2] mb-6">
              Características Impulsadas por IA para el{' '}
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#11a5e8] to-[#5f7790]">
                Éxito de Pequeñas Empresas
              </span>
            </h1>
            <p className="text-xl text-[#d5dce2]/80 max-w-3xl mx-auto">
              Descubre cómo nuestra plataforma de análisis impulsada por IA transforma datos complejos en insights accionables 
              que impulsan el crecimiento, optimizan operaciones y aumentan la rentabilidad para PYMES.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="pb-20 px-6">
        <div className="container mx-auto max-w-7xl">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-[#1e2a3e] rounded-2xl p-8 border border-[#2a3441] hover:border-[#5f7790] transition-all duration-300"
              >
                <div className="flex items-start gap-4 mb-6">
                  <div className="p-3 bg-[#11a5e8]/10 rounded-xl">
                    <feature.icon className="w-8 h-8 text-[#11a5e8]" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-2xl font-bold text-[#d5dce2] mb-3">
                      {feature.title}
                    </h3>
                    <p className="text-[#d5dce2]/70 mb-6">
                      {feature.description}
                    </p>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <h4 className="text-lg font-semibold text-[#11a5e8] mb-3">Beneficios Clave:</h4>
                  {feature.benefits.map((benefit, benefitIndex) => (
                    <div key={benefitIndex} className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-[#11a5e8] rounded-full mt-2 flex-shrink-0" />
                      <span className="text-[#d5dce2]/80">{benefit}</span>
                    </div>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-6">
        <div className="container mx-auto max-w-4xl text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="bg-gradient-to-r from-[#11a5e8]/10 to-[#5f7790]/10 rounded-2xl p-12 border border-[#2a3441]"
          >
            <h2 className="text-4xl font-bold text-[#d5dce2] mb-6">
              ¿Listo para Transformar tu Negocio?
            </h2>
            <p className="text-xl text-[#d5dce2]/80 mb-8">
              Ve cómo estas potentes características de IA pueden impulsar el crecimiento y la rentabilidad de tu PYME.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.a
                href="https://calendly.com/smeanalytica/30min"
                target="_blank"
                rel="noopener noreferrer"
                className="px-8 py-4 bg-[#11a5e8] text-white rounded-full text-lg font-medium hover:bg-[#0e8bc7] transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Agendar Demo Gratuito
              </motion.a>
              <motion.a
                href="/pricing"
                className="px-8 py-4 bg-transparent text-[#d5dce2] rounded-full text-lg font-medium border-2 border-[#5f7790] hover:bg-[#5f7790] transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Ver Precios
              </motion.a>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
} 