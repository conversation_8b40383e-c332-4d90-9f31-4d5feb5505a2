import type { Metadata } from 'next';
import FeaturesClient from './FeaturesClient';

export const metadata: Metadata = {
  title: 'Características de Análisis IA para PYMES | Insights Predictivos por SME Analytica',
  description: 'Explora las características impulsadas por IA de SME Analytica: flujo de caja predictivo, pronóstico de ventas, ROI de marketing, segmentación de clientes, e insights de gestión de inventario adaptados para pequeñas empresas.',
  keywords: [
    'predicción flujo de caja pequeñas empresas',
    'análisis ventas IA para retail',
    'optimización ROI marketing',
    'segmentación clientes PYME',
    'insights gestión inventario',
    'análisis predictivo pequeñas empresas',
    'características inteligencia empresarial IA'
  ],
  openGraph: {
    title: 'Características de Análisis IA para PYMES | SME Analytica',
    description: 'Descubre características potentes de IA que transforman datos en insights accionables para pequeñas y medianas empresas.',
    url: 'https://smeanalytica.dev/features',
  },
};

export default function FeaturesPage() {
  return <FeaturesClient />;
} 