/**
 * SME Analytica Internal Admin Portal
 * For company-wide restaurant intelligence and platform management
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Search, 
  Building2, 
  Brain, 
  Activity, 
  Target, 
  BarChart3,
  Users,
  Shield,
  Database,
  TrendingUp,
  MapPin,
  Phone,
  Mail,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { getAllRestaurants, getPlatformStats, Restaurant } from '@/lib/supabase';
import PlatformAIInsights from '@/components/admin/PlatformAIInsights';
import SystemHealthMonitor from '@/components/admin/SystemHealthMonitor';
import AITrainingDashboard from '@/components/admin/AITrainingDashboard';
import RevenueOptimizationEngine from '@/components/admin/RevenueOptimizationEngine';

export default function SMEAnalyticaAdminPortal() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRestaurant, setSelectedRestaurant] = useState<Restaurant | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loginCode, setLoginCode] = useState('');
  const [restaurants, setRestaurants] = useState<Restaurant[]>([]);
  const [platformStats, setPlatformStats] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load real data from database
  useEffect(() => {
    if (isAuthenticated) {
      loadRestaurantData();
    }
  }, [isAuthenticated]);

  const loadRestaurantData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [restaurantData, statsData] = await Promise.all([
        getAllRestaurants(),
        getPlatformStats()
      ]);
      
      setRestaurants(restaurantData);
      setPlatformStats(statsData);
    } catch (err) {
      console.error('Error loading data:', err);
      setError('Failed to load restaurant data. Please check your connection.');
    } finally {
      setLoading(false);
    }
  };

  const filteredRestaurants = restaurants.filter(restaurant =>
    restaurant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (restaurant.location && restaurant.location.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleLogin = () => {
    // Simple demo authentication - in production use proper auth
    if (loginCode === 'sme2025') {
      setIsAuthenticated(true);
    } else {
      alert('Invalid access code');
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center gap-2">
              <Shield className="h-6 w-6 text-blue-600" />
              SME Analytica Admin Portal
            </CardTitle>
            <CardDescription>
              Internal platform intelligence & restaurant management
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Access Code</label>
              <Input
                type="password"
                placeholder="Enter admin access code"
                value={loginCode}
                onChange={(e) => setLoginCode(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleLogin()}
              />
            </div>
            <Button onClick={handleLogin} className="w-full">
              Access Admin Portal
            </Button>
            <p className="text-xs text-gray-500 text-center">
              Demo code: sme2025
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-3">
              <Building2 className="h-8 w-8 text-blue-600" />
              <div>
                <h1 className="text-xl font-bold text-gray-900">SME Analytica</h1>
                <p className="text-xs text-gray-500">Internal Admin Portal</p>
              </div>
            </div>
            <div className="flex items-center gap-4">
              {platformStats && (
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <span>{platformStats.totalRestaurants} Restaurants</span>
                  <span>•</span>
                  <span>{platformStats.totalOrders} Orders</span>
                  <span>•</span>
                  <Badge variant="outline" className="text-green-600 border-green-600">
                    {platformStats.platformUptime}% Uptime
                  </Badge>
                </div>
              )}
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => {
                  setIsAuthenticated(false);
                  setRestaurants([]);
                  setPlatformStats(null);
                }}
              >
                Sign Out
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs defaultValue="revenue" className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="revenue">Revenue Engine</TabsTrigger>
            <TabsTrigger value="restaurants">Restaurant Search</TabsTrigger>
            <TabsTrigger value="intelligence">AI Intelligence</TabsTrigger>
            <TabsTrigger value="system-health">System Health</TabsTrigger>
            <TabsTrigger value="training">AI Training</TabsTrigger>
          </TabsList>

          {/* Revenue Optimization Engine Tab */}
          <TabsContent value="revenue" className="space-y-6">
            <RevenueOptimizationEngine />
          </TabsContent>

          {/* Restaurant Search Tab */}
          <TabsContent value="restaurants" className="space-y-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search restaurants by name or location..."
                  className="pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  disabled={loading}
                />
              </div>
              <Button 
                variant="outline"
                onClick={loadRestaurantData}
                disabled={loading}
              >
                {loading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Loading...
                  </>
                ) : (
                  <>
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Refresh Data
                  </>
                )}
              </Button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Restaurant List */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">
                  Restaurants ({loading ? '...' : filteredRestaurants.length})
                </h3>
                
                {error && (
                  <Card className="border-red-200 bg-red-50">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-2 text-red-700">
                        <AlertCircle className="h-4 w-4" />
                        <span className="text-sm">{error}</span>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {loading && (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
                    <span className="ml-2 text-gray-600">Loading restaurants...</span>
                  </div>
                )}

                {!loading && !error && filteredRestaurants.length === 0 && (
                  <Card>
                    <CardContent className="text-center py-8">
                      <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500">
                        {searchTerm ? 'No restaurants found matching your search.' : 'No restaurants found in the database.'}
                      </p>
                    </CardContent>
                  </Card>
                )}

                {!loading && filteredRestaurants.map((restaurant) => (
                  <Card 
                    key={restaurant.id}
                    className={`cursor-pointer transition-all hover:shadow-md ${
                      selectedRestaurant?.id === restaurant.id ? 'ring-2 ring-blue-500' : ''
                    }`}
                    onClick={() => setSelectedRestaurant(restaurant)}
                  >
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-semibold">{restaurant.name}</h4>
                        <Badge 
                          variant={restaurant.status === 'active' ? 'default' : 
                                 restaurant.status === 'trial' ? 'secondary' : 'outline'}
                        >
                          {restaurant.status}
                        </Badge>
                      </div>
                      <div className="space-y-1 text-sm text-gray-600">
                        <p className="flex items-center gap-1">
                          <MapPin className="h-3 w-3" />
                          {restaurant.location}
                        </p>
                        <p>Orders: {restaurant.totalOrders.toLocaleString()}</p>
                        <p>Revenue: ${restaurant.revenue.toLocaleString()}</p>
                        <p>Rating: {restaurant.customerSatisfaction}/5.0</p>
                        <p className="text-xs">Last active: {restaurant.lastActive}</p>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* Restaurant Details */}
              <div>
                {selectedRestaurant ? (
                  <Card>
                    <CardHeader>
                      <CardTitle>{selectedRestaurant.name}</CardTitle>
                      <CardDescription>Detailed restaurant intelligence</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="text-center p-3 bg-blue-50 rounded-lg">
                          <p className="text-2xl font-bold text-blue-600">{selectedRestaurant.totalOrders}</p>
                          <p className="text-xs text-gray-600">Total Orders</p>
                        </div>
                        <div className="text-center p-3 bg-green-50 rounded-lg">
                          <p className="text-2xl font-bold text-green-600">${selectedRestaurant.revenue.toLocaleString()}</p>
                          <p className="text-xs text-gray-600">Revenue</p>
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <h5 className="font-medium">Quick Actions</h5>
                        <div className="grid grid-cols-1 gap-2">
                          <Button variant="outline" size="sm">
                            <Brain className="h-4 w-4 mr-2" />
                            Generate AI Insights
                          </Button>
                          <Button variant="outline" size="sm">
                            <Target className="h-4 w-4 mr-2" />
                            Menu Optimization
                          </Button>
                          <Button variant="outline" size="sm">
                            <Activity className="h-4 w-4 mr-2" />
                            Performance Analysis
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ) : (
                  <Card>
                    <CardContent className="text-center py-12">
                      <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500">Select a restaurant to view details</p>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          </TabsContent>

          {/* AI Intelligence Tab */}
          <TabsContent value="intelligence" className="space-y-6">
            <PlatformAIInsights />
          </TabsContent>

          {/* System Health Tab */}
          <TabsContent value="system-health" className="space-y-6">
            <SystemHealthMonitor />
          </TabsContent>

          {/* AI Training Tab */}
          <TabsContent value="training" className="space-y-6">
            <AITrainingDashboard />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}