'use client';

import React from 'react';
import { motion } from 'framer-motion';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import { Spotlight } from '../../components/ui/spotlight-new';
import { GradientButton } from '../../components/ui/gradient-button';

export default function TermsOfService() {
  return (
    <>
      <Navbar />
      <div className="min-h-screen bg-[#171f31] pt-32 relative overflow-hidden">
        <div className="absolute inset-0 z-0">
          <Spotlight
            gradientFirst="radial-gradient(68.54% 68.72% at 55.02% 31.46%, hsla(200, 100%, 85%, .04) 0, hsla(200, 100%, 55%, .01) 50%, hsla(200, 100%, 45%, 0) 80%)"
            gradientSecond="radial-gradient(50% 50% at 50% 50%, hsla(200, 100%, 85%, .03) 0, hsla(200, 100%, 55%, .01) 80%, transparent 100%)"
            gradientThird="radial-gradient(50% 50% at 50% 50%, hsla(200, 100%, 85%, .02) 0, hsla(200, 100%, 45%, .01) 80%, transparent 100%)"
          />
        </div>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="max-w-4xl mx-auto"
          >
            <h1 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-[#11a5e8] to-[#5f7790] mb-8">
              Terms of Service
            </h1>

            <div className="space-y-8">
              <section className="bg-[#171f31]/50 p-6 rounded-lg border border-[#5f7790]/20">
                <p className="text-[#d5dce2]">
                  By accessing SME Analytica, you agree to these Terms of Service. These terms apply to all visitors, users, and others who access or use the platform. Please read them carefully before using our services.
                </p>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-[#11a5e8] mb-4">
                  Service Description
                </h2>
                <p className="text-[#d5dce2] mb-4">
                  SME Analytica provides AI-powered business intelligence and analytics services designed to help small and medium enterprises grow. Our services include:
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-[#171f31]/50 p-4 rounded-lg border border-[#5f7790]/20">
                    <div className="flex items-center mb-2">
                      <div className="w-8 h-8 rounded-full bg-[#11a5e8]/10 flex items-center justify-center mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-[#11a5e8]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                      </div>
                      <h3 className="text-[#d5dce2] font-medium">Business Performance Analytics</h3>
                    </div>
                    <p className="text-[#d5dce2]/70 text-sm pl-10">Comprehensive analysis of your business metrics and KPIs</p>
                  </div>
                  <div className="bg-[#171f31]/50 p-4 rounded-lg border border-[#5f7790]/20">
                    <div className="flex items-center mb-2">
                      <div className="w-8 h-8 rounded-full bg-[#11a5e8]/10 flex items-center justify-center mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-[#11a5e8]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                        </svg>
                      </div>
                      <h3 className="text-[#d5dce2] font-medium">Market Trend Analysis</h3>
                    </div>
                    <p className="text-[#d5dce2]/70 text-sm pl-10">Insights into market trends and competitive landscape</p>
                  </div>
                  <div className="bg-[#171f31]/50 p-4 rounded-lg border border-[#5f7790]/20">
                    <div className="flex items-center mb-2">
                      <div className="w-8 h-8 rounded-full bg-[#11a5e8]/10 flex items-center justify-center mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-[#11a5e8]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                      </div>
                      <h3 className="text-[#d5dce2] font-medium">Growth Opportunity Identification</h3>
                    </div>
                    <p className="text-[#d5dce2]/70 text-sm pl-10">AI-powered detection of growth opportunities</p>
                  </div>
                  <div className="bg-[#171f31]/50 p-4 rounded-lg border border-[#5f7790]/20">
                    <div className="flex items-center mb-2">
                      <div className="w-8 h-8 rounded-full bg-[#11a5e8]/10 flex items-center justify-center mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-[#11a5e8]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </div>
                      <h3 className="text-[#d5dce2] font-medium">Automated Reporting</h3>
                    </div>
                    <p className="text-[#d5dce2]/70 text-sm pl-10">Customized reports and actionable insights</p>
                  </div>
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-[#11a5e8] mb-4">
                  User Obligations
                </h2>
                <p className="text-[#d5dce2] mb-4">
                  As a user of our service, you agree to:
                </p>
                <ul className="list-disc pl-6 text-[#d5dce2] space-y-2">
                  <li>Provide accurate and complete information</li>
                  <li>Maintain the security of your account</li>
                  <li>Use the service in compliance with applicable laws</li>
                  <li>Not misuse or attempt to exploit the service</li>
                </ul>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-[#11a5e8] mb-4">
                  Intellectual Property
                </h2>
                <div className="bg-[#171f31]/50 p-4 rounded-lg border border-[#5f7790]/20">
                  <p className="text-[#d5dce2]">
                    All content, features, and functionality of SME Analytica, including but not limited to text, graphics, logos,
                    and software, are the exclusive property of SME Analytica and are protected by international copyright,
                    trademark, and other intellectual property laws.
                  </p>
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-[#11a5e8] mb-4">
                  Limitation of Liability
                </h2>
                <p className="text-[#d5dce2]">
                  Our platform and services are provided "as is" and "as available" without any warranties, express or implied. SME Analytica is not liable for any indirect, incidental, special, consequential, or punitive damages, or any loss of profits or revenues, whether incurred directly or indirectly.
                </p>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-[#11a5e8] mb-4">
                  Changes to Terms
                </h2>
                <p className="text-[#d5dce2]">
                  We reserve the right to modify these terms at any time. We will notify users of any material changes.
                  Your continued use of the service after such modifications constitutes acceptance of the updated terms.
                </p>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-[#11a5e8] mb-4">
                  Governing Law
                </h2>
                <div className="bg-[#171f31]/50 p-4 rounded-lg border border-[#5f7790]/20">
                  <p className="text-[#d5dce2]">
                    These terms are governed by and construed in accordance with the laws of Spain and the European Union (EU). Any disputes shall be resolved in the courts of competent jurisdiction in Valencia, Spain.
                  </p>
                </div>
              </section>

              <section className="bg-[#171f31]/50 p-6 rounded-lg border border-[#5f7790]/20">
                <h2 className="text-2xl font-semibold text-[#11a5e8] mb-4">
                  Contact
                </h2>
                <p className="text-[#d5dce2]">
                  For any questions regarding these terms, please contact us at{' '}
                  <a
                    href="mailto:<EMAIL>"
                    className="text-[#11a5e8] hover:text-[#11a5e8]/80 transition-colors"
                  >
                    <EMAIL>
                  </a>
                </p>

                <div className="mt-6 flex flex-wrap gap-4">
                  <GradientButton href="/privacy-policy" size="sm">
                    Privacy Policy
                  </GradientButton>
                  <GradientButton href="/gdpr" size="sm">
                    GDPR Compliance
                  </GradientButton>
                </div>
              </section>

              <div className="my-8 h-[1px] w-full bg-gradient-to-r from-transparent via-[#5f7790]/30 to-transparent" />
            </div>
          </motion.div>
        </div>
      </div>
      <Footer />
    </>
  );
}