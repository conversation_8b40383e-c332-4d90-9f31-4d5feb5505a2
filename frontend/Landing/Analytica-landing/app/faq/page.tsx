'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiChevronDown, FiChevronUp, FiHelpCircle, FiSearch } from 'react-icons/fi';
import { FAQStructuredData } from '../../components/structured-data';

interface FAQItem {
  id: string;
  category: string;
  question: string;
  answer: string;
  keywords: string[];
}

const faqData: FAQItem[] = [
  {
    id: 'what-is-sme-analytica',
    category: 'General',
    question: 'What is SME Analytica and how does it help small businesses?',
    answer: 'SME Analytica is an AI-powered business intelligence platform specifically designed for small and medium enterprises. It helps businesses automate data analysis, predict market trends, optimize pricing strategies, and make data-driven decisions. Our platform includes mobile analytics, restaurant management systems, and voice AI capabilities.',
    keywords: ['sme analytica', 'small business', 'ai platform', 'business intelligence']
  },
  {
    id: 'ai-analysis-types',
    category: 'AI Features',
    question: 'What types of AI analysis does SME Analytica provide?',
    answer: 'SME Analytica offers comprehensive AI analysis including: Dynamic Pricing Optimization, Market Trend Analysis, Customer Sentiment Analysis, Growth Forecasting, Competitor Analysis, and Business Performance Insights. Our AI engine uses advanced models including Claude Opus 4, GPT-4.1, and Gemini 2.0 Flash for accurate predictions.',
    keywords: ['ai analysis', 'pricing optimization', 'market analysis', 'sentiment analysis', 'forecasting']
  },
  {
    id: 'restaurant-management',
    category: 'Restaurant Features',
    question: 'How does the restaurant management system work?',
    answer: 'Our Menu Flow Dynamo system provides restaurants with AI-powered menu optimization, dynamic pricing based on demand patterns, customer sentiment analysis from reviews, and real-time analytics. It includes QR code menus, order management, and chef recommendation systems powered by AI insights.',
    keywords: ['restaurant management', 'menu optimization', 'qr code', 'dynamic pricing', 'chef recommendations']
  },
  {
    id: 'subscription-plans',
    category: 'Pricing',
    question: 'What subscription plans are available?',
    answer: 'SME Analytica offers three subscription tiers: Basic ($29/month) with 12 analyses, Premium ($79/month) with 50 analyses and advanced features, and Enterprise ($199/month) with unlimited analyses and custom AI models. All plans include a 30-day free trial.',
    keywords: ['subscription', 'pricing', 'plans', 'basic', 'premium', 'enterprise', 'free trial']
  },
  {
    id: 'business-types',
    category: 'General',
    question: 'Is SME Analytica suitable for different types of businesses?',
    answer: 'Yes, SME Analytica serves multiple business types including restaurants, retail stores, e-commerce businesses, service providers, and general SMEs. Our platform adapts AI analysis to specific industry needs and provides customized insights for each business type.',
    keywords: ['business types', 'restaurants', 'retail', 'ecommerce', 'service providers', 'industries']
  },
  {
    id: 'ai-accuracy',
    category: 'AI Features',
    question: 'How accurate are the AI predictions and recommendations?',
    answer: 'Our AI models achieve 85-95% accuracy in predictions depending on the analysis type. We use ensemble methods combining multiple AI models, real-time data sources, and Harvard Business School frameworks to ensure reliable insights. All recommendations include confidence scores and supporting data.',
    keywords: ['ai accuracy', 'predictions', 'confidence scores', 'reliability', 'harvard business school']
  },
  {
    id: 'getting-started',
    category: 'Getting Started',
    question: 'How do I get started with SME Analytica?',
    answer: 'Getting started is simple: 1) Sign up for a free 30-day trial, 2) Choose your business type and upload basic business information, 3) Connect your data sources (optional), 4) Run your first AI analysis, 5) Review insights and implement recommendations. Our onboarding process takes less than 10 minutes.',
    keywords: ['getting started', 'onboarding', 'free trial', 'setup', 'first analysis']
  },
  {
    id: 'data-security',
    category: 'Security',
    question: 'How secure is my business data?',
    answer: 'SME Analytica uses enterprise-grade security including end-to-end encryption, SOC 2 compliance, GDPR compliance, and secure cloud infrastructure. Your data is never shared with third parties and you maintain full ownership. We use Supabase for secure database management and implement strict access controls.',
    keywords: ['data security', 'encryption', 'gdpr', 'soc 2', 'privacy', 'supabase']
  },
  {
    id: 'mobile-app',
    category: 'Mobile',
    question: 'Is there a mobile app available?',
    answer: 'Yes, SME Analytica offers native mobile apps for iOS and Android. The mobile app provides full access to analytics, real-time notifications, dashboard viewing, and the ability to run quick analyses on the go. The app syncs seamlessly with the web platform.',
    keywords: ['mobile app', 'ios', 'android', 'notifications', 'dashboard', 'sync']
  },
  {
    id: 'api-integration',
    category: 'Technical',
    question: 'Can I integrate SME Analytica with my existing systems?',
    answer: 'Yes, SME Analytica provides comprehensive REST APIs, webhooks, and integrations with popular business tools. We support integrations with POS systems, accounting software, CRM platforms, and e-commerce platforms. Our API documentation is available at docs.smeanalytica.dev.',
    keywords: ['api integration', 'rest api', 'webhooks', 'pos systems', 'accounting', 'crm', 'ecommerce']
  }
];

const categories = ['All', 'General', 'AI Features', 'Restaurant Features', 'Pricing', 'Getting Started', 'Security', 'Mobile', 'Technical'];

export default function FAQPage() {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchQuery, setSearchQuery] = useState('');
  const [openItems, setOpenItems] = useState<string[]>([]);

  const filteredFAQs = faqData.filter(faq => {
    const matchesCategory = selectedCategory === 'All' || faq.category === selectedCategory;
    const matchesSearch = searchQuery === '' || 
      faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.keywords.some(keyword => keyword.toLowerCase().includes(searchQuery.toLowerCase()));
    
    return matchesCategory && matchesSearch;
  });

  const toggleItem = (id: string) => {
    setOpenItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  return (
    <div className="min-h-screen bg-[#171f31] text-white">
      <FAQStructuredData />
      
      {/* Hero Section */}
      <section className="relative py-20 px-6">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="flex items-center justify-center gap-3 mb-6"
          >
            <FiHelpCircle className="w-12 h-12 text-[#11a5e8]" />
            <h1 className="text-4xl md:text-5xl font-bold text-[#d5dce2]">
              Frequently Asked Questions
            </h1>
          </motion.div>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-xl text-[#d5dce2]/80 mb-8"
          >
            Find answers to common questions about SME Analytica's AI-powered business intelligence platform
          </motion.p>

          {/* Search Bar */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="relative max-w-md mx-auto mb-8"
          >
            <FiSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-[#d5dce2]/60 w-5 h-5" />
            <input
              type="text"
              placeholder="Search FAQs..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-12 pr-4 py-3 bg-[#1e2a3f] border border-[#5f7790]/20 rounded-lg text-[#d5dce2] placeholder-[#d5dce2]/60 focus:outline-none focus:border-[#11a5e8] transition-colors"
            />
          </motion.div>
        </div>
      </section>

      {/* Category Filter */}
      <section className="px-6 pb-8">
        <div className="max-w-4xl mx-auto">
          <div className="flex flex-wrap gap-3 justify-center">
            {categories.map((category, index) => (
              <motion.button
                key={category}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 * index }}
                onClick={() => setSelectedCategory(category)}
                className={`px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                  selectedCategory === category
                    ? 'bg-[#11a5e8] text-white'
                    : 'bg-[#1e2a3f] text-[#d5dce2]/80 hover:bg-[#11a5e8]/20 hover:text-[#11a5e8]'
                }`}
              >
                {category}
              </motion.button>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Items */}
      <section className="px-6 pb-16">
        <div className="max-w-4xl mx-auto">
          <div className="space-y-4">
            {filteredFAQs.map((faq, index) => (
              <motion.div
                key={faq.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 * index }}
                className="bg-[#1e2a3f] rounded-xl border border-[#5f7790]/20 overflow-hidden"
              >
                <button
                  onClick={() => toggleItem(faq.id)}
                  className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-[#5f7790]/10 transition-colors"
                >
                  <div className="flex-1">
                    <span className="inline-block px-2 py-1 bg-[#11a5e8]/20 text-[#11a5e8] text-xs font-medium rounded mb-2">
                      {faq.category}
                    </span>
                    <h3 className="text-lg font-semibold text-[#d5dce2]">
                      {faq.question}
                    </h3>
                  </div>
                  {openItems.includes(faq.id) ? (
                    <FiChevronUp className="w-5 h-5 text-[#11a5e8] flex-shrink-0 ml-4" />
                  ) : (
                    <FiChevronDown className="w-5 h-5 text-[#d5dce2]/60 flex-shrink-0 ml-4" />
                  )}
                </button>
                
                <AnimatePresence>
                  {openItems.includes(faq.id) && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="overflow-hidden"
                    >
                      <div className="px-6 pb-4">
                        <p className="text-[#d5dce2]/80 leading-relaxed">
                          {faq.answer}
                        </p>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            ))}
          </div>

          {filteredFAQs.length === 0 && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-12"
            >
              <FiHelpCircle className="w-16 h-16 text-[#d5dce2]/40 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-[#d5dce2] mb-2">No FAQs found</h3>
              <p className="text-[#d5dce2]/60">
                Try adjusting your search or category filter
              </p>
            </motion.div>
          )}
        </div>
      </section>

      {/* Contact Section */}
      <section className="px-6 pb-16">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="bg-gradient-to-r from-[#11a5e8]/10 to-[#5f7790]/10 rounded-xl p-8 text-center border border-[#11a5e8]/20"
          >
            <h2 className="text-2xl font-bold text-[#d5dce2] mb-4">
              Still have questions?
            </h2>
            <p className="text-[#d5dce2]/80 mb-6">
              Can't find the answer you're looking for? Our support team is here to help.
            </p>
            <a
              href="/contact"
              className="inline-flex items-center gap-2 bg-[#11a5e8] text-white px-6 py-3 rounded-lg font-medium hover:bg-[#11a5e8]/90 transition-colors"
            >
              Contact Support
            </a>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
