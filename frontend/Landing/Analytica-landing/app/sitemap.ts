import { MetadataRoute } from 'next';

type ChangeFrequency = 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://smeanalytica.dev';

  // Main pages with their specific configurations
  const mainPages: Array<{
    route: string;
    changeFrequency: ChangeFrequency;
    priority: number;
  }> = [
    { route: '', changeFrequency: 'weekly', priority: 1.0 },
    { route: '/features', changeFrequency: 'monthly', priority: 0.8 },
    { route: '/use-cases', changeFrequency: 'monthly', priority: 0.8 },
    { route: '/pricing', changeFrequency: 'monthly', priority: 0.8 },
    { route: '/about', changeFrequency: 'monthly', priority: 0.8 },
    { route: '/contact', changeFrequency: 'monthly', priority: 0.8 },
    { route: '/blog', changeFrequency: 'weekly', priority: 0.9 },
    { route: '/privacy-policy', changeFrequency: 'yearly', priority: 0.5 },
    { route: '/terms', changeFrequency: 'yearly', priority: 0.5 },
    { route: '/gdpr', changeFrequency: 'yearly', priority: 0.5 },
  ];

  // Blog posts
  const blogPosts = [
    '/blog/cash-flow-prediction-strategies-small-business',
    '/blog/retail-customer-behavior-analytics-increase-sales',
    '/blog/ai-inventory-management-small-business-comparison',
    '/blog/restaurant-food-waste-reduction-analytics',
    '/blog/ecommerce-analytics-small-business-guide-2024',
    '/blog/customer-lifetime-value-calculation-improvement-sme',
  ];

  // Generate main sitemap
  const mainSitemap: MetadataRoute.Sitemap = mainPages.map(({ route, changeFrequency, priority }) => ({
    url: `${baseUrl}${route}`,
    lastModified: new Date(),
    changeFrequency,
    priority,
  }));

  // Generate blog sitemap
  const blogSitemap: MetadataRoute.Sitemap = blogPosts.map((post) => ({
    url: `${baseUrl}${post}`,
    lastModified: new Date(),
    changeFrequency: 'monthly' as const,
    priority: 0.7,
  }));

  return [...mainSitemap, ...blogSitemap];
}