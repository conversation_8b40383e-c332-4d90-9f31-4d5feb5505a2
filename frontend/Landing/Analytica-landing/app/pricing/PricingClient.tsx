'use client';

import { motion } from 'framer-motion';
import { FiCheck, FiX, FiStar, FiHelpCircle } from 'react-icons/fi';
import { useState } from 'react';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';

const plans = [
  {
    name: "Startup",
    price: 45,
    description: "Perfecto para nuevas PYMES que comienzan con análisis de datos",
    features: [
      "Predicción básica de flujo de caja",
      "Análisis de tendencias de ventas",
      "Hasta 3 fuentes de datos",
      "Informes mensuales",
      "Soporte por email",
      "Acceso a aplicación móvil"
    ],
    notIncluded: [
      "Pronósticos avanzados con IA",
      "Integraciones personalizadas",
      "Soporte prioritario",
      "Informes de marca blanca"
    ],
    popular: false,
    cta: "Comenzar Prueba Gratuita"
  },
  {
    name: "Crecimiento",
    price: 137,
    description: "Ideal para PYMES en crecimiento listas para escalar con análisis avanzados",
    features: [
      "Análisis predictivo avanzado",
      "Predicción de abandono de clientes",
      "Optimización de ROI de marketing",
      "Hasta 10 fuentes de datos",
      "Informes automatizados semanales",
      "Soporte prioritario por email y chat",
      "Paneles personalizados",
      "Optimización de inventario",
      "Análisis competitivo"
    ],
    notIncluded: [
      "Informes de marca blanca",
      "Gestor de cuenta dedicado"
    ],
    popular: true,
    cta: "Comenzar Prueba Gratuita"
  },
  {
    name: "Empresa",
    price: 367,
    description: "Para PYMES establecidas que necesitan soluciones de análisis integrales",
    features: [
      "Todas las características de Crecimiento",
      "Entrenamiento personalizado de modelos IA",
      "Fuentes de datos ilimitadas",
      "Informes diarios en tiempo real",
      "Gestor de cuenta dedicado",
      "Soporte telefónico",
      "Informes de marca blanca",
      "Acceso a API",
      "Integraciones personalizadas",
      "Características avanzadas de seguridad",
      "Herramientas de colaboración en equipo"
    ],
    notIncluded: [],
    popular: false,
    cta: "Contactar Ventas"
  }
];

const faqs = [
  {
    question: "¿Hay una prueba gratuita?",
    answer: "¡Sí! Ofrecemos una prueba gratuita de 14 días para todos los planes. No se requiere tarjeta de crédito para comenzar."
  },
  {
    question: "¿Puedo cambiar de plan en cualquier momento?",
    answer: "Absolutamente. Puedes actualizar o reducir tu plan en cualquier momento. Los cambios toman efecto inmediatamente."
  },
  {
    question: "¿Qué fuentes de datos soportan?",
    answer: "Nos integramos con más de 100 herramientas empresariales populares incluyendo QuickBooks, Shopify, Stripe, Google Analytics, Facebook Ads, y muchas más."
  },
  {
    question: "¿Qué tan rápido puedo ver resultados?",
    answer: "La mayoría de clientes ven insights accionables dentro de 48 horas de conectar sus fuentes de datos. El entrenamiento completo del modelo IA típicamente toma 1-2 semanas."
  },
  {
    question: "¿Están seguros mis datos?",
    answer: "Sí. Usamos encriptación de nivel empresarial, cumplimiento SOC 2, y nunca compartimos tus datos con terceros."
  },
  {
    question: "¿Ofrecen soluciones personalizadas?",
    answer: "¡Sí! Nuestro plan Empresa incluye entrenamiento personalizado de modelos IA e integraciones adaptadas a las necesidades específicas de tu negocio."
  }
];

export default function PricingClient() {
  const [isAnnual, setIsAnnual] = useState(false);
  const [openFaq, setOpenFaq] = useState<number | null>(null);

  return (
    <div className="min-h-screen bg-[#171f31]">
      <Navbar />
      
      {/* Hero Section */}
      <section className="pt-32 pb-20 px-6">
        <div className="container mx-auto max-w-6xl">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h1 className="text-5xl md:text-6xl font-bold text-[#d5dce2] mb-6">
              Precios Simples y{' '}
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#11a5e8] to-[#5f7790]">
                Transparentes
              </span>
            </h1>
            <p className="text-xl text-[#d5dce2]/80 max-w-3xl mx-auto mb-8">
              Elige el plan perfecto para tu PYME. Todos los planes incluyen nuestras características 
              principales de análisis IA sin tarifas ocultas o contratos a largo plazo.
            </p>
            
            {/* Annual/Monthly Toggle */}
            <div className="flex items-center justify-center gap-4 mb-12">
              <span className={`text-lg ${!isAnnual ? 'text-[#11a5e8]' : 'text-[#d5dce2]/60'}`}>
                Mensual
              </span>
              <button
                onClick={() => setIsAnnual(!isAnnual)}
                className="relative w-16 h-8 bg-[#2a3441] rounded-full p-1 transition-all duration-300"
              >
                <div
                  className={`w-6 h-6 bg-[#11a5e8] rounded-full transition-all duration-300 ${
                    isAnnual ? 'translate-x-8' : 'translate-x-0'
                  }`}
                />
              </button>
              <span className={`text-lg ${isAnnual ? 'text-[#11a5e8]' : 'text-[#d5dce2]/60'}`}>
                Anual
              </span>
              {isAnnual && (
                <span className="bg-green-500/20 text-green-400 px-3 py-1 rounded-full text-sm font-medium">
                  Ahorra 20%
                </span>
              )}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="pb-20 px-6">
        <div className="container mx-auto max-w-7xl">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {plans.map((plan, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className={`relative bg-[#1e2a3e] rounded-2xl p-8 border ${
                  plan.popular 
                    ? 'border-[#11a5e8] ring-2 ring-[#11a5e8]/20' 
                    : 'border-[#2a3441]'
                } hover:border-[#5f7790] transition-all duration-300`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <div className="bg-[#11a5e8] text-white px-4 py-2 rounded-full text-sm font-medium flex items-center gap-2">
                      <FiStar className="w-4 h-4" />
                      Más Popular
                    </div>
                  </div>
                )}
                
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-[#d5dce2] mb-2">{plan.name}</h3>
                  <p className="text-[#d5dce2]/70 mb-6">{plan.description}</p>
                  <div className="mb-6">
                    <span className="text-5xl font-bold text-[#11a5e8]">
                      €{isAnnual ? Math.round(plan.price * 0.8) : plan.price}
                    </span>
                    <span className="text-[#d5dce2]/60 text-lg">/mes</span>
                    {isAnnual && (
                      <div className="text-sm text-[#d5dce2]/60 mt-1">
                        Facturado anualmente (€{Math.round(plan.price * 0.8 * 12)}/año)
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-4 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-start gap-3">
                      <FiCheck className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                      <span className="text-[#d5dce2]/90">{feature}</span>
                    </div>
                  ))}
                  {plan.notIncluded.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-start gap-3 opacity-50">
                      <FiX className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                      <span className="text-[#d5dce2]/60">{feature}</span>
                    </div>
                  ))}
                </div>

                <button className={`w-full py-4 rounded-xl font-medium transition-all duration-300 ${
                  plan.popular
                    ? 'bg-[#11a5e8] text-white hover:bg-[#11a5e8]/90 hover:scale-105'
                    : 'bg-transparent border-2 border-[#5f7790] text-[#d5dce2] hover:border-[#11a5e8] hover:text-[#11a5e8]'
                }`}>
                  {plan.cta}
                </button>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Comparison */}
      <section className="py-20 px-6 bg-[#1e2a3e]/30">
        <div className="container mx-auto max-w-6xl">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-[#d5dce2] mb-4">
              Comparación Detallada de Características
            </h2>
            <p className="text-xl text-[#d5dce2]/80">
              Ve exactamente qué incluye cada plan para tomar la mejor decisión para tu negocio
            </p>
          </motion.div>

          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b border-[#2a3441]">
                  <th className="text-left py-4 px-6 text-[#d5dce2] font-semibold">Características</th>
                  <th className="text-center py-4 px-6 text-[#d5dce2] font-semibold">Startup</th>
                  <th className="text-center py-4 px-6 text-[#d5dce2] font-semibold">Crecimiento</th>
                  <th className="text-center py-4 px-6 text-[#d5dce2] font-semibold">Empresa</th>
                </tr>
              </thead>
              <tbody>
                {[
                  { feature: "Fuentes de datos", startup: "3", growth: "10", enterprise: "Ilimitadas" },
                  { feature: "Predicción de flujo de caja", startup: "✓", growth: "✓", enterprise: "✓" },
                  { feature: "Análisis de tendencias", startup: "✓", growth: "✓", enterprise: "✓" },
                  { feature: "Informes automatizados", startup: "Mensual", growth: "Semanal", enterprise: "Diario" },
                  { feature: "Soporte", startup: "Email", growth: "Email + Chat", enterprise: "Teléfono + Dedicado" },
                  { feature: "Paneles personalizados", startup: "✗", growth: "✓", enterprise: "✓" },
                  { feature: "Análisis predictivo avanzado", startup: "✗", growth: "✓", enterprise: "✓" },
                  { feature: "Modelos IA personalizados", startup: "✗", growth: "✗", enterprise: "✓" },
                  { feature: "Acceso a API", startup: "✗", growth: "✗", enterprise: "✓" },
                  { feature: "Informes de marca blanca", startup: "✗", growth: "✗", enterprise: "✓" }
                ].map((row, index) => (
                  <tr key={index} className="border-b border-[#2a3441]/50">
                    <td className="py-4 px-6 text-[#d5dce2]">{row.feature}</td>
                    <td className="py-4 px-6 text-center text-[#d5dce2]/80">{row.startup}</td>
                    <td className="py-4 px-6 text-center text-[#d5dce2]/80">{row.growth}</td>
                    <td className="py-4 px-6 text-center text-[#d5dce2]/80">{row.enterprise}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 px-6">
        <div className="container mx-auto max-w-4xl">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-[#d5dce2] mb-4">
              Preguntas Frecuentes
            </h2>
            <p className="text-xl text-[#d5dce2]/80">
              ¿Tienes preguntas? Tenemos respuestas.
            </p>
          </motion.div>

          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-[#1e2a3e] rounded-xl border border-[#2a3441] overflow-hidden"
              >
                <button
                  onClick={() => setOpenFaq(openFaq === index ? null : index)}
                  className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-[#2a3441]/50 transition-colors"
                >
                  <span className="text-[#d5dce2] font-medium">{faq.question}</span>
                  <FiHelpCircle className={`w-5 h-5 text-[#11a5e8] transition-transform ${
                    openFaq === index ? 'rotate-180' : ''
                  }`} />
                </button>
                {openFaq === index && (
                  <div className="px-6 pb-4">
                    <p className="text-[#d5dce2]/80">{faq.answer}</p>
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-6 bg-gradient-to-r from-[#11a5e8]/10 to-[#5f7790]/10">
        <div className="container mx-auto max-w-4xl text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl font-bold text-[#d5dce2] mb-4">
              ¿Listo para Transformar tu PYME?
            </h2>
            <p className="text-xl text-[#d5dce2]/80 mb-8">
              Únete a más de 1,000 PYMES que ya están usando SME Analytica para tomar mejores decisiones empresariales.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="px-8 py-4 bg-[#11a5e8] text-white rounded-xl font-medium hover:bg-[#11a5e8]/90 hover:scale-105 transition-all duration-300">
                Comenzar Prueba Gratuita
              </button>
              <button className="px-8 py-4 bg-transparent border-2 border-[#5f7790] text-[#d5dce2] rounded-xl font-medium hover:border-[#11a5e8] hover:text-[#11a5e8] transition-all duration-300">
                Agendar Demo
              </button>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
} 