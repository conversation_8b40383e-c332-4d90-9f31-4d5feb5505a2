'use client';

import { motion } from 'framer-motion';
import { FiMail, FiPhone, FiMapPin, FiClock, FiMessageCircle, FiHelpCircle } from 'react-icons/fi';
import { useState } from 'react';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import { Input } from '../../components/ui/input';
import { Textarea } from '../../components/ui/textarea';

const contactMethods = [
  {
    icon: FiMail,
    title: "Email Us",
    description: "Get in touch via email for general inquiries",
    contact: "<EMAIL>",
    action: "mailto:<EMAIL>"
  },
  {
    icon: FiPhone,
    title: "Call Us",
    description: "Speak directly with our team",
    contact: "+34 605 599 964",
    action: "tel:+34605599964"
  },
  {
    icon: FiMessageCircle,
    title: "Live Chat",
    description: "Chat with our support team in real-time",
    contact: "Available 9 AM - 6 PM EST",
    action: "#"
  },
  {
    icon: FiMapPin,
    title: "Visit Us",
    description: "Our headquarters in Valencia, Spain",
    contact: "Valencia, Spain",
    action: "https://maps.google.com"
  }
];

const faqs = [
  {
    question: "How quickly can I get started?",
    answer: "Most customers are up and running within 48 hours. Our onboarding team will guide you through the entire setup process."
  },
  {
    question: "Do you offer custom integrations?",
    answer: "Yes! Our Enterprise plan includes custom integrations. We can connect to virtually any business system or database."
  },
  {
    question: "What kind of support do you provide?",
    answer: "We offer email, chat, and phone support depending on your plan. Enterprise customers get a dedicated account manager."
  },
  {
    question: "Can I see a demo before purchasing?",
    answer: "Absolutely! We offer personalized demos where we can show you exactly how SME Analytica would work for your specific business."
  }
];

export default function ContactClient() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Reset form
    setFormData({ name: '', email: '', company: '', message: '' });
    setIsSubmitting(false);
    
    // Show success message (you can implement this with a toast or modal)
    alert('Thank you for your message! We\'ll get back to you within 24 hours.');
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <div className="min-h-screen bg-[#171f31]">
      <Navbar />
      
      {/* Hero Section */}
      <section className="pt-32 pb-20 px-6">
        <div className="container mx-auto max-w-6xl">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h1 className="text-5xl md:text-6xl font-bold text-[#d5dce2] mb-6">
              Get in{' '}
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#11a5e8] to-[#5f7790]">
                Touch
              </span>
            </h1>
            <p className="text-xl text-[#d5dce2]/80 max-w-3xl mx-auto">
              Ready to transform your business with AI analytics? We're here to help you get started 
              and answer any questions you might have.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Contact Methods */}
      <section className="pb-20 px-6">
        <div className="container mx-auto max-w-7xl">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
            {contactMethods.map((method, index) => (
              <motion.a
                key={index}
                href={method.action}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-[#1e2a3e] rounded-2xl p-6 border border-[#2a3441] hover:border-[#5f7790] transition-all duration-300 text-center group"
              >
                <div className="p-3 bg-[#11a5e8]/10 rounded-xl w-fit mx-auto mb-4 group-hover:bg-[#11a5e8]/20 transition-all duration-300">
                  <method.icon className="w-8 h-8 text-[#11a5e8]" />
                </div>
                <h3 className="text-xl font-semibold text-[#d5dce2] mb-2">{method.title}</h3>
                <p className="text-[#d5dce2]/70 mb-3">{method.description}</p>
                <p className="text-[#11a5e8] font-medium">{method.contact}</p>
              </motion.a>
            ))}
          </div>

          {/* Contact Form and Info */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              className="bg-[#1e2a3e] rounded-2xl p-8 border border-[#2a3441]"
            >
              <h2 className="text-3xl font-bold text-[#d5dce2] mb-6">Send us a Message</h2>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-[#d5dce2] mb-2">
                      Full Name *
                    </label>
                    <Input
                      id="name"
                      name="name"
                      type="text"
                      required
                      value={formData.name}
                      onChange={handleInputChange}
                      className="bg-[#2a3441] border-[#3a4451] text-[#d5dce2] focus:border-[#11a5e8]"
                      placeholder="Your full name"
                    />
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-[#d5dce2] mb-2">
                      Email Address *
                    </label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      required
                      value={formData.email}
                      onChange={handleInputChange}
                      className="bg-[#2a3441] border-[#3a4451] text-[#d5dce2] focus:border-[#11a5e8]"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>
                
                <div>
                  <label htmlFor="company" className="block text-sm font-medium text-[#d5dce2] mb-2">
                    Company Name
                  </label>
                  <Input
                    id="company"
                    name="company"
                    type="text"
                    value={formData.company}
                    onChange={handleInputChange}
                    className="bg-[#2a3441] border-[#3a4451] text-[#d5dce2] focus:border-[#11a5e8]"
                    placeholder="Your company name"
                  />
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-[#d5dce2] mb-2">
                    Message *
                  </label>
                  <Textarea
                    id="message"
                    name="message"
                    required
                    rows={5}
                    value={formData.message}
                    onChange={handleInputChange}
                    className="bg-[#2a3441] border-[#3a4451] text-[#d5dce2] focus:border-[#11a5e8] resize-none"
                    placeholder="Tell us about your business and how we can help..."
                  />
                </div>

                <motion.button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full py-4 bg-[#11a5e8] text-white rounded-full text-lg font-medium hover:bg-[#0e8bc7] transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                  whileHover={{ scale: isSubmitting ? 1 : 1.02 }}
                  whileTap={{ scale: isSubmitting ? 1 : 0.98 }}
                >
                  {isSubmitting ? 'Sending...' : 'Send Message'}
                </motion.button>
              </form>
            </motion.div>

            {/* Office Info & Hours */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="space-y-8"
            >
              {/* Office Hours */}
              <div className="bg-[#1e2a3e] rounded-2xl p-8 border border-[#2a3441]">
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-3 bg-[#11a5e8]/10 rounded-xl">
                    <FiClock className="w-6 h-6 text-[#11a5e8]" />
                  </div>
                  <h3 className="text-2xl font-bold text-[#d5dce2]">Office Hours</h3>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-[#d5dce2]/80">Monday - Friday</span>
                    <span className="text-[#d5dce2]">9:00 AM - 6:00 PM EST</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-[#d5dce2]/80">Saturday</span>
                    <span className="text-[#d5dce2]">10:00 AM - 2:00 PM EST</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-[#d5dce2]/80">Sunday</span>
                    <span className="text-[#d5dce2]">Closed</span>
                  </div>
                </div>
              </div>

              {/* Response Time */}
              <div className="bg-[#1e2a3e] rounded-2xl p-8 border border-[#2a3441]">
                <h3 className="text-xl font-semibold text-[#d5dce2] mb-4">Response Times</h3>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-[#d5dce2]/80">Email: Within 24 hours</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-[#d5dce2]/80">Phone: Immediate during business hours</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-[#d5dce2]/80">Live Chat: Real-time support</span>
                  </div>
                </div>
              </div>

              {/* Emergency Support */}
              <div className="bg-gradient-to-r from-[#11a5e8]/10 to-[#5f7790]/10 rounded-2xl p-8 border border-[#2a3441]">
                <h3 className="text-xl font-semibold text-[#d5dce2] mb-4">Need Immediate Help?</h3>
                <p className="text-[#d5dce2]/80 mb-4">
                  For urgent technical issues or enterprise support, our priority support team is available 24/7.
                </p>
                <motion.a
                  href="https://calendly.com/smeanalytica/30min"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-2 px-6 py-3 bg-[#11a5e8] text-white rounded-full font-medium hover:bg-[#0e8bc7] transition-all duration-300"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Schedule Emergency Call
                </motion.a>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 px-6 bg-[#1e2a3e]/50">
        <div className="container mx-auto max-w-4xl">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-bold text-[#d5dce2] mb-6">Quick Answers</h2>
            <p className="text-xl text-[#d5dce2]/80">
              Common questions we get from potential customers
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-[#1e2a3e] rounded-xl p-6 border border-[#2a3441]"
              >
                <div className="flex items-start gap-3 mb-3">
                  <FiHelpCircle className="w-6 h-6 text-[#11a5e8] mt-1 flex-shrink-0" />
                  <h4 className="text-lg font-semibold text-[#d5dce2]">{faq.question}</h4>
                </div>
                <p className="text-[#d5dce2]/80 ml-9">{faq.answer}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-6">
        <div className="container mx-auto max-w-4xl text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="bg-gradient-to-r from-[#11a5e8]/10 to-[#5f7790]/10 rounded-2xl p-12 border border-[#2a3441]"
          >
            <h2 className="text-4xl font-bold text-[#d5dce2] mb-6">
              Ready to Get Started?
            </h2>
            <p className="text-xl text-[#d5dce2]/80 mb-8">
              Don't wait to transform your business. Schedule a personalized demo and see 
              how SME Analytica can drive your growth.
            </p>
            <motion.a
              href="https://calendly.com/smeanalytica/30min"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2 px-8 py-4 bg-[#11a5e8] text-white rounded-full text-lg font-medium hover:bg-[#0e8bc7] transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Schedule Your Demo
            </motion.a>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
} 