import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';
import { Label } from '../../components/ui/label';
import { Input } from '../../components/ui/input';
import { Textarea } from '../../components/ui/textarea';
import { BottomGradient } from '../../components/ui/bottom-gradient';
import { cn } from '../../src/lib/utils';

// Helper component for form fields
const LabelInputContainer = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <div className={cn("flex w-full flex-col space-y-2", className)}>
      {children}
    </div>
  );
};

interface InterestFormProps {
  isOpen: boolean;
  onClose: () => void;
  formType?: 'interest' | 'early-access' | 'contact';
  title?: string;
}

export default function InterestForm({ isOpen, onClose, formType = 'interest', title }: InterestFormProps) {
  const [formData, setFormData] = useState({
    fullName: '',
    businessName: '',
    location: '',
    email: '',
    message: '',
    interest: '',
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  const getFormTitle = () => {
    if (title) return title;
    switch (formType) {
      case 'early-access':
        return 'Request Early Access';
      case 'contact':
        return 'Contact Us';
      default:
        return 'Tell Us About Your Business';
    }
  };

  const getFormDescription = () => {
    switch (formType) {
      case 'early-access':
        return 'Be among the first to experience AI-powered business intelligence with Analytica.';
      case 'contact':
        return 'Have questions? We\'d love to hear from you. Send us a message and we\'ll respond as soon as possible.';
      default:
        return 'We\'d love to learn more about your business and how we can help you grow.';
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
    setError('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/interest', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Origin': 'https://smeanalytica.dev'
        },
        body: JSON.stringify({
          full_name: formData.fullName,
          business_name: formData.businessName,
          location: formData.location,
          email: formData.email,
          message: formData.message,
          interest: formData.interest,
          form_type: formType,
        }),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.detail || 'Failed to submit form');
      }

      // Successfully submitted the form
      await response.json();

      setSuccess(true);
      setTimeout(() => {
        onClose();
        setSuccess(false);
        setFormData({
          fullName: '',
          businessName: '',
          location: '',
          email: '',
          message: '',
          interest: '',
        });
      }, 3000);

    } catch (err: any) {
      console.error('Form submission error:', err);
      setError(err.message || 'An error occurred while submitting the form');
    } finally {
      setLoading(false);
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-[#171f31]/80 backdrop-blur-md z-50 flex items-center justify-center p-4"
          onClick={(e) => e.target === e.currentTarget && onClose()}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="shadow-input bg-[#171f31] rounded-2xl shadow-2xl w-full max-w-md p-8 relative border border-[#5f7790]/20"
          >
            <button
              onClick={onClose}
              className="absolute top-4 right-4 text-[#d5dce2]/70 hover:text-[#11a5e8] transition-colors w-8 h-8 flex items-center justify-center rounded-full bg-[#5f7790]/10"
            >
              ✕
            </button>

            {!success ? (
              <div>
                <h2 className="text-2xl font-bold text-[#d5dce2] mb-2">
                  {getFormTitle()}
                </h2>
                <p className="text-[#d5dce2]/70 mb-6 text-sm">
                  {getFormDescription()}
                </p>

                <div className="my-4 h-[1px] w-full bg-gradient-to-r from-transparent via-[#5f7790]/30 to-transparent" />

                <form onSubmit={handleSubmit} className="space-y-4 my-6">
                  <LabelInputContainer>
                    <Label htmlFor="fullName">Full Name</Label>
                    <Input
                      id="fullName"
                      name="fullName"
                      required
                      value={formData.fullName}
                      onChange={handleChange}
                      placeholder="Enter your full name"
                    />
                  </LabelInputContainer>

                  {formType !== 'contact' && (
                    <>
                      <LabelInputContainer>
                        <Label htmlFor="businessName">Business Name</Label>
                        <Input
                          id="businessName"
                          name="businessName"
                          required
                          value={formData.businessName}
                          onChange={handleChange}
                          placeholder="Enter your business name"
                        />
                      </LabelInputContainer>

                      <LabelInputContainer>
                        <Label htmlFor="location">Location</Label>
                        <Input
                          id="location"
                          name="location"
                          required
                          value={formData.location}
                          onChange={handleChange}
                          placeholder="City, State"
                        />
                      </LabelInputContainer>
                    </>
                  )}

                  <LabelInputContainer>
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      required
                      value={formData.email}
                      onChange={handleChange}
                      placeholder="<EMAIL>"
                    />
                  </LabelInputContainer>

                  {formType === 'contact' ? (
                    <LabelInputContainer>
                      <Label htmlFor="message">Message</Label>
                      <Textarea
                        id="message"
                        name="message"
                        required
                        value={formData.message}
                        onChange={handleChange}
                        placeholder="How can we help you?"
                        rows={4}
                      />
                    </LabelInputContainer>
                  ) : (
                    <LabelInputContainer>
                      <Label htmlFor="interest">What interests you most about Analytica?</Label>
                      <Textarea
                        id="interest"
                        name="interest"
                        required
                        value={formData.interest}
                        onChange={handleChange}
                        placeholder="Tell us what you hope to achieve..."
                        rows={3}
                      />
                    </LabelInputContainer>
                  )}

                  {error && (
                    <p className="text-sm text-red-400 mt-2">
                      {error}
                    </p>
                  )}

                  <button
                    type="submit"
                    disabled={loading}
                    className="group/btn relative block h-10 w-full rounded-md bg-gradient-to-br from-[#5f7790] to-[#11a5e8] font-medium text-[#d5dce2] shadow-[0px_1px_0px_0px_#ffffff40_inset,0px_-1px_0px_0px_#ffffff40_inset] mt-6 hover:shadow-[0_0_10px_rgba(17,165,232,0.5)] transition-all duration-300 disabled:opacity-50"
                  >
                    {loading ? 'Submitting...' : 'Submit'}
                    <BottomGradient />
                  </button>
                </form>
              </div>
            ) : (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="text-center py-8"
              >
                <div className="w-16 h-16 bg-[#11a5e8]/10 rounded-full flex items-center justify-center mx-auto mb-6 border border-[#11a5e8]/30">
                  <svg className="w-8 h-8 text-[#11a5e8]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-[#d5dce2] mb-3">
                  {formType === 'contact' ? 'Message Sent!' : 'Thank You for Your Interest!'}
                </h3>
                <p className="text-[#d5dce2]/70">
                  {formType === 'contact'
                    ? 'We\'ll get back to you as soon as possible.'
                    : 'We\'ll be in touch with you soon to discuss how Analytica can help your business grow.'}
                </p>
              </motion.div>
            )}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}