'use client';

import { Navbar as NavbarComponent } from '../../components/ui/resizable-navbar';
import { useLanguage } from '../../lib/LanguageProvider';

export default function Navbar() {
  const { language, setLanguage } = useLanguage();
  
  return (
    <NavbarComponent>
      <div className="flex items-center justify-between">
        <span className="text-[#11a5e8] font-bold text-xl">SME Analytica</span>
        <button
          onClick={() => setLanguage(language === 'es' ? 'en' : 'es')}
          className="px-3 py-1 bg-[#5f7790]/20 border border-[#5f7790]/40 rounded-lg text-[#d5dce2] text-xs font-medium hover:bg-[#5f7790]/30 transition-all duration-200"
        >
          {language.toUpperCase()}
        </button>
      </div>
    </NavbarComponent>
  );
}
