'use client';

import { motion } from 'framer-motion';
import { Box, ChartBar, Lock, Smartphone, Sparkles, Zap } from 'lucide-react';
import { useLanguage } from '../../lib/LanguageProvider';

export default function FeaturesBento() {
  const { language, content } = useLanguage();

  const benefits = [
    {
      icon: <Sparkles className="h-8 w-8" />,
      key: 'effortless',
      visualColor: "hsl(199, 80%, 65%)", // Data Streams
      delay: 0
    },
    {
      icon: <ChartBar className="h-8 w-8" />,
      key: 'intelligent',
      visualColor: "hsl(215, 40%, 50%)", // Core AI
      delay: 0.1
    },
    {
      icon: <Zap className="h-8 w-8" />,
      key: 'realtime',
      visualColor: "hsl(199, 100%, 75%)", // Insights
      delay: 0.2
    },
    {
      icon: <Box className="h-8 w-8" />,
      key: 'growth',
      visualColor: "hsl(199, 80%, 50%)", // Growth Paths
      delay: 0.3
    },
    {
      icon: <Lock className="h-8 w-8" />,
      key: 'security',
      visualColor: "hsl(215, 35%, 50%)", // Primary Blue
      delay: 0.4
    },
    {
      icon: <Smartphone className="h-8 w-8" />,
      key: 'support',
      visualColor: "hsl(199, 100%, 60%)", // Accent Blue
      delay: 0.5
    }
  ];

  return (
    <section id="benefits" className="relative py-20 bg-[#171f31] overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute w-72 h-72 bg-[#11a5e8]/10 rounded-full blur-3xl top-20 left-1/4 animate-pulse" />
        <div className="absolute w-96 h-96 bg-[#5f7790]/10 rounded-full blur-3xl bottom-20 right-1/4 animate-pulse" />
        {/* Subtle Grid */}
        <div 
          className="absolute inset-0 opacity-[0.02]"
          style={{
            backgroundImage: `
              linear-gradient(rgba(213, 220, 226, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(213, 220, 226, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '60px 60px',
          }}
        />
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-[#d5dce2] mb-6 tracking-tight">
            {content.benefits.title[language]}
          </h2>
          <p className="text-lg md:text-xl text-[#d5dce2]/80 max-w-3xl mx-auto leading-relaxed">
            {content.benefits.subtitle[language]}
          </p>
        </motion.div>

        {/* Benefits Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {benefits.map((benefit, index) => (
            <motion.div
              key={benefit.key}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: benefit.delay }}
              className="relative group"
            >
              <div className="relative bg-[#171f31] border border-[#5f7790]/20 rounded-2xl p-8 h-full group-hover:border-[#11a5e8]/40 transition-all duration-300">
                {/* Glowing Background */}
                <div 
                  className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-5 transition-opacity duration-300 blur-xl"
                  style={{ background: benefit.visualColor }}
                />
                
                {/* Icon */}
                <div className="relative mb-6">
                  <div 
                    className="w-16 h-16 rounded-full flex items-center justify-center relative overflow-hidden"
                    style={{ background: `${benefit.visualColor}15` }}
                  >
                    <div 
                      className="text-current relative z-10"
                      style={{ color: benefit.visualColor }}
                    >
                      {benefit.icon}
                    </div>
                    {/* Animated glow */}
                    <motion.div
                      className="absolute inset-0 rounded-full"
                      style={{ background: benefit.visualColor }}
                      animate={{ 
                        opacity: [0.1, 0.3, 0.1],
                        scale: [1, 1.1, 1]
                      }}
                      transition={{ 
                        repeat: Infinity, 
                        duration: 4,
                        delay: index * 0.5
                      }}
                    />
                  </div>
                </div>

                {/* Content */}
                <h3 className="text-xl font-semibold text-[#d5dce2] mb-4">
                  {content.benefits.items[benefit.key as keyof typeof content.benefits.items].title[language]}
                </h3>
                <p className="text-[#d5dce2]/70 leading-relaxed">
                  {content.benefits.items[benefit.key as keyof typeof content.benefits.items].description[language]}
                </p>

                {/* Subtle hover effect */}
                <div 
                  className="absolute bottom-0 left-0 right-0 h-1 rounded-b-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  style={{ background: `linear-gradient(90deg, ${benefit.visualColor}60, ${benefit.visualColor}20)` }}
                />
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}