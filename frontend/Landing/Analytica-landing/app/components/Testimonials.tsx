'use client';

import { motion } from 'framer-motion';
import TestimonialCard from '../../components/ui/testimonial-card';
import { useLanguage } from '../../lib/LanguageProvider';

export default function Testimonials() {
  return (
    <section id="testimonials" className="py-32 bg-[#171f31] overflow-hidden relative">
      <div className="container mx-auto px-6 lg:px-8 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="text-center mb-24"
        >
          <h2 className="text-5xl font-extrabold text-[#d5dce2] mb-6 tracking-tight">
            See Analytica in Action
          </h2>
          <p className="text-xl text-[#d5dce2]/80 max-w-2xl mx-auto leading-relaxed">
            We&apos;re actively testing Analytica with local businesses around us. Hear what our clients have to say about the power and insights delivered by our AI-driven analytics platform.
          </p>
        </motion.div>

        {/* Testimonial Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.2 }}
          className="mb-24 grid md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {[
            {
              name: "Maria García",
              role: "CEO, TechNova Solutions",
              content: "SME Analytica transformed our decision-making process. We've seen a 40% increase in operational efficiency.",
            },
            {
              name: "David Chen", 
              role: "Owner, Urban Eats",
              content: "The predictive analytics helped us optimize our inventory. Food waste decreased by 30% in just 3 months.",
            },
            {
              name: "Sarah Johnson",
              role: "Director, Coastal Retreats",
              content: "Real-time insights into customer behavior changed how we approach marketing. ROI improved by 150%.",
            }
          ].map((testimonial, index) => (
            <TestimonialCard
              key={index}
              name={testimonial.name}
              role={testimonial.role}
              content={testimonial.content}
            />
          ))}
        </motion.div>

        {/* Preview Video */}
        <div className="relative max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
            className="relative h-[75vh] bg-[#171f31] backdrop-blur-sm rounded-2xl overflow-hidden shadow-2xl border border-[#5f7790]/30"
          >
            {/* Background glow effects */}
            <div className="absolute inset-0 pointer-events-none">
              <div className="absolute top-0 left-1/4 w-1/2 h-1/2 bg-[#11a5e8]/10 rounded-full blur-3xl" />
              <div className="absolute bottom-0 right-1/4 w-1/2 h-1/2 bg-[#5f7790]/10 rounded-full blur-3xl" />
            </div>

            {/* Video */}
            <div className="relative h-full group">
              <video
                autoPlay
                loop
                muted
                playsInline
                className="w-full h-full object-contain bg-transparent transition-transform duration-700 group-hover:scale-[1.02]"
              >
                <source src="/videos/initial.mp4" type="video/mp4" />
                Your browser does not support video playback.
              </video>

              {/* Overlay gradient */}
              <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-[#171f31]/40" />

              {/* Info overlay */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="absolute bottom-0 left-0 right-0 p-6 text-[#d5dce2] bg-gradient-to-t from-[#171f31]/80 to-transparent"
              >
                <p className="text-sm font-medium">Watch Demo</p>
                <div className="mt-1 flex items-center gap-2 text-[#d5dce2]/60">
                  <span className="inline-block w-2 h-2 rounded-full bg-[#11a5e8] animate-pulse" />
                  <span className="text-xs">Live Preview</span>
                </div>
              </motion.div>
            </div>

            {/* Corner decorations */}
            <div className="absolute top-3 left-3 w-3 h-3 border-l-2 border-t-2 border-[#5f7790]/50" />
            <div className="absolute top-3 right-3 w-3 h-3 border-r-2 border-t-2 border-[#5f7790]/50" />
            <div className="absolute bottom-3 left-3 w-3 h-3 border-l-2 border-b-2 border-[#5f7790]/50" />
            <div className="absolute bottom-3 right-3 w-3 h-3 border-r-2 border-b-2 border-[#5f7790]/50" />
          </motion.div>
        </div>

        {/* Bottom Info Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.4 }}
          className="text-center mt-24 max-w-4xl mx-auto"
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 items-start">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.5 }}
              className="p-6 rounded-2xl bg-[#171f31] backdrop-blur-sm border border-[#5f7790]/30"
            >
              <div className="w-12 h-12 bg-[#11a5e8]/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-[#11a5e8]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-[#d5dce2] mb-2">Real-time Insights</h3>
              <p className="text-[#d5dce2]/80">
                Get instant analytics and actionable insights powered by advanced AI algorithms.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.6 }}
              className="p-6 rounded-2xl bg-[#171f31] backdrop-blur-sm border border-[#5f7790]/30"
            >
              <div className="w-12 h-12 bg-[#5f7790]/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-[#5f7790]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-[#d5dce2] mb-2">Enterprise Security</h3>
              <p className="text-[#d5dce2]/80">
                Your data is protected with bank-grade encryption and security measures.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.7 }}
              className="p-6 rounded-2xl bg-[#171f31] backdrop-blur-sm border border-[#5f7790]/30"
            >
              <div className="w-12 h-12 bg-[#11a5e8]/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-[#11a5e8]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-[#d5dce2] mb-2">24/7 Support</h3>
              <p className="text-[#d5dce2]/80">
                Our dedicated team is always available to help you succeed.
              </p>
            </motion.div>
          </div>

          <motion.p
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.8 }}
            className="mt-12 text-[#d5dce2]/80 text-lg max-w-2xl mx-auto"
          >
            Join hundreds of businesses already using Analytica to transform their data into strategic decisions. Our platform adapts to your needs, whether you're a small business or a large enterprise.
          </motion.p>
        </motion.div>
      </div>
    </section>
  );
}
