'use client';

import { motion, useScroll, useSpring, useTransform } from 'framer-motion';
import { useRef } from 'react';
import { useLanguage } from '../../lib/LanguageProvider';
import { WobbleCard } from '../../components/ui/wobble-card';
import { FiCpu, FiTrendingUp, FiDatabase } from 'react-icons/fi';

const timeline = [
  {
    date: 'Q4 2024',
    title: 'Initial Build',
    description: 'Set up core backend infrastructure, designed robust and scalable APIs, established initial database structures, and performed foundational tests to ensure system reliability.'
  },
  {
    date: 'Q1 2025',
    title: 'AI Model Integration',
    description: 'Integrated and trained advanced AI models, established machine learning pipelines, implemented predictive analytics, and optimized algorithms for enhanced accuracy and efficiency.'
  },
  {
    date: 'Q2 2025',
    title: 'Real-time Features',
    description: 'Developed real-time data streaming and analysis features, enabled dynamic pricing mechanisms, and introduced comprehensive market and sentiment analysis tools.'
  },
  {
    date: 'April 2025',
    title: 'Analytica Launch 🚀',
    description: 'Official public release, first batch of customers onboarded, extensive marketing and user feedback sessions initiated to refine platform capabilities and user experience continuously.'
  }
];

export default function InsightsSection() {
  const { language } = useLanguage();
  const ref = useRef(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ['start end', 'end start'],
  });
  const springProgress = useSpring(scrollYProgress, { stiffness: 120, damping: 20 });

  return (
    <section id="insights" className="py-16 sm:py-24 md:py-32 relative bg-[#171f31] overflow-hidden">
      
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="text-center mb-16 sm:mb-20 md:mb-24"
        >
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-extrabold text-[#d5dce2] mb-4 sm:mb-6 tracking-tight">
            {language === 'es' ? 'El Motor de Analytica' : 'The Analytica Engine'}
          </h2>
          <p className="text-base sm:text-lg md:text-xl text-[#d5dce2]/80 max-w-2xl mx-auto px-4">
            {language === 'es' 
              ? 'Detrás de escena de nuestra poderosa plataforma impulsada por IA'
              : 'Behind the scenes of our powerful AI-driven platform'
            }
          </p>
        </motion.div>

        {/* Features Grid */}
        <div className="mb-20 sm:mb-32 md:mb-40 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
            <WobbleCard containerClassName="col-span-1 lg:col-span-2 h-full bg-[#171f31] min-h-[300px] lg:min-h-[400px]">
              <div className="p-8 lg:p-16">
                <FiCpu className="w-12 h-12 text-[#11a5e8] mb-4" />
                <h3 className="text-2xl lg:text-3xl font-bold text-[#d5dce2] mb-4">
                  {language === 'es' ? 'IA Avanzada de Análisis' : 'Advanced Analytics AI'}
                </h3>
                <p className="text-[#d5dce2]/80 text-lg max-w-xl">
                  {language === 'es' 
                    ? 'Procesa millones de puntos de datos en tiempo real para entregar insights accionables que impulsan el crecimiento de tu negocio.'
                    : 'Process millions of data points in real-time to deliver actionable insights that drive your business growth.'
                  }
                </p>
              </div>
            </WobbleCard>
            
            <WobbleCard containerClassName="col-span-1 min-h-[300px]">
              <div className="p-8">
                <FiTrendingUp className="w-10 h-10 text-[#22c55e] mb-4" />
                <h3 className="text-xl font-bold text-[#d5dce2] mb-3">
                  {language === 'es' ? 'Predicciones Precisas' : 'Accurate Predictions'}
                </h3>
                <p className="text-[#d5dce2]/70">
                  {language === 'es' 
                    ? 'Modelos predictivos con 94% de precisión para pronósticos de ventas y tendencias de mercado.'
                    : '94% accurate predictive models for sales forecasting and market trends.'
                  }
                </p>
              </div>
            </WobbleCard>
            
            <WobbleCard containerClassName="col-span-1 min-h-[300px]">
              <div className="p-8">
                <FiDatabase className="w-10 h-10 text-[#8b5cf6] mb-4" />
                <h3 className="text-xl font-bold text-[#d5dce2] mb-3">
                  {language === 'es' ? 'Integración Total' : 'Complete Integration'}
                </h3>
                <p className="text-[#d5dce2]/70">
                  {language === 'es' 
                    ? 'Conecta con todas tus fuentes de datos existentes sin disrupciones en tu flujo de trabajo.'
                    : 'Connect with all your existing data sources without disrupting your workflow.'
                  }
                </p>
              </div>
            </WobbleCard>
          </div>
        </div>

        {/* Timeline */}
        <div ref={ref} className="relative max-w-4xl mx-auto px-4">
          {/* Connecting line */}
          <motion.div
            className="absolute top-0 left-4 sm:left-1/2 w-0.5 sm:w-1 bg-gradient-to-b from-[#11a5e8] via-[#5f7790] to-[#d5dce2] rounded-full origin-top shadow-[0_0_12px_rgba(17,165,232,0.4)]"
            style={{
              height: '100%',
              scaleY: springProgress
            }}
          />
          {/* Glowing dot */}
          <motion.div
            className="absolute w-3 h-3 sm:w-4 sm:h-4 left-[14px] sm:left-[calc(50%-8px)] bg-[#171f31] rounded-full shadow-lg"
            style={{
              top: '0%',
              y: useTransform(springProgress, [0, 1], ['0%', '100%']),
            }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-[#11a5e8] to-[#5f7790] rounded-full animate-pulse" />
            <div className="absolute inset-[2px] bg-[#171f31] rounded-full" />
          </motion.div>

          <div className="space-y-16 sm:space-y-24">
            {timeline.map((item, index) => (
              <motion.div
                key={item.date}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.2 }}
                className={`relative flex flex-col ${index % 2 === 0 ? 'sm:items-end' : 'sm:items-start'} ml-8 sm:ml-0 ${
                  index % 2 === 0 ? 'sm:justify-end' : 'sm:justify-start'
                }`}
              >
                <div className={`w-full sm:w-[calc(50%-2rem)] ${index % 2 === 0 ? 'sm:pr-12' : 'sm:pl-12'}`}>
                  <div className="bg-[#171f31] border border-[#5f7790]/30 p-4 sm:p-6 rounded-xl sm:rounded-2xl shadow-xl backdrop-blur-md">
                    <span className="text-sm sm:text-base text-[#11a5e8] font-semibold">
                      {item.date}
                    </span>
                    <h4 className="text-base sm:text-lg font-semibold text-[#d5dce2] mt-2">
                      {item.title}
                    </h4>
                    <p className="text-sm sm:text-base text-[#d5dce2]/80 mt-2">
                      {item.description}
                    </p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
