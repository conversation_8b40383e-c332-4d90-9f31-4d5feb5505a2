'use client';

// Using custom X (Twitter) icon to avoid deprecation warnings
import { motion } from 'framer-motion';

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-[#171f31] text-[#d5dce2] py-12 sm:py-16 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        {/* Horizontal line animation */}
        <motion.div
          className="absolute top-0 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-[#11a5e8]/40 to-transparent"
          animate={{
            x: ['-100%', '100%']
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "linear"
          }}
        />

        {/* Glowing orbs */}
        <motion.div
          className="absolute top-[20%] left-[10%] w-32 h-32 rounded-full bg-[#11a5e8]/5 blur-3xl"
          animate={{
            opacity: [0.3, 0.5, 0.3],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        <motion.div
          className="absolute bottom-[30%] right-[15%] w-40 h-40 rounded-full bg-[#5f7790]/5 blur-3xl"
          animate={{
            opacity: [0.2, 0.4, 0.2],
            scale: [1.2, 1, 1.2],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          className="flex flex-col sm:flex-row justify-between items-center gap-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="w-full sm:w-auto flex justify-center sm:justify-start">
            <motion.img
              src="/images/sme-logo.png"
              alt="SME Analytica"
              className="h-10 sm:h-12"
              whileHover={{ scale: 1.05 }}
            />
          </div>

          <div className="flex flex-col sm:flex-row items-center gap-6">
            <motion.a
              href="https://x.com/smeanalytica?s=21"
              target="_blank"
              rel="noopener noreferrer"
              className="text-[#d5dce2] hover:text-[#11a5e8] transition-colors flex items-center gap-2 text-sm sm:text-base"
              whileHover={{ scale: 1.05, color: "#11a5e8" }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-[#5f7790]/20 border border-[#5f7790]/30">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M4 4l11.733 16h4.267l-11.733 -16z"></path>
                  <path d="M4 20l6.768 -6.768m2.46 -2.46l6.772 -6.772"></path>
                </svg>
              </div>
              <span>@smeanalytica</span>
            </motion.a>

            <motion.a
              href="mailto:<EMAIL>"
              className="text-[#d5dce2] hover:text-[#11a5e8] transition-colors flex items-center gap-2 text-sm sm:text-base"
              whileHover={{ scale: 1.05, color: "#11a5e8" }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-[#5f7790]/20 border border-[#5f7790]/30">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <rect width="20" height="16" x="2" y="4" rx="2" />
                  <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
                </svg>
              </div>
              <span>Contact Us</span>
            </motion.a>
          </div>
        </motion.div>

        <motion.div
          className="border-t border-[#5f7790]/20 mt-8 pt-8 flex flex-col sm:flex-row justify-between items-center gap-6"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <p className="text-[#d5dce2]/70 text-sm text-center sm:text-left">
            &copy; {currentYear} SME Analytica. All rights reserved.
          </p>

          <div className="flex flex-col sm:flex-row items-center gap-6 sm:gap-8">
            <motion.a
              href="/privacy-policy"
              className="text-[#d5dce2]/70 hover:text-[#11a5e8] text-sm transition-colors"
              whileHover={{ y: -2, color: "#11a5e8" }}
            >
              Privacy Policy
            </motion.a>
            <motion.a
              href="/terms"
              className="text-[#d5dce2]/70 hover:text-[#11a5e8] text-sm transition-colors"
              whileHover={{ y: -2, color: "#11a5e8" }}
            >
              Terms of Service
            </motion.a>
            <motion.a
              href="/gdpr"
              className="text-[#d5dce2]/70 hover:text-[#11a5e8] text-sm transition-colors"
              whileHover={{ y: -2, color: "#11a5e8" }}
            >
              GDPR Compliance
            </motion.a>
          </div>
        </motion.div>
      </div>
    </footer>
  );
}
