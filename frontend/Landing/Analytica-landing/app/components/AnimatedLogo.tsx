'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';

// Define types for component props
interface AnimatedLogoProps {
  showText?: boolean;
  className?: string;
  imageClassName?: string;
  textClassName?: string;
  size?: 'sm' | 'md' | 'lg';
}

// Define animation variants
const logoVariants = {
  initial: {
    opacity: 0,
    scale: 0.8
  },
  animate: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.3,
      type: "spring",
      stiffness: 260,
      damping: 20
    }
  },
  hover: {
    scale: 1.05,
    transition: {
      duration: 0.2,
      type: "spring",
      stiffness: 400,
      damping: 10
    }
  }
};

const glowVariants = {
  animate: {
    scale: [1, 1.2, 1],
    opacity: [0.3, 0.5, 0.3],
    transition: {
      duration: 3,
      repeat: Infinity,
      ease: "easeInOut"
    }
  }
};

const textVariants = {
  initial: {
    opacity: 0,
    x: -10
  },
  animate: {
    opacity: 1,
    x: 0,
    transition: {
      delay: 0.2,
      duration: 0.3
    }
  }
};

export default function AnimatedLogo({
  showText = true,
  className = "",
  imageClassName = "",
  textClassName = "",
  size = 'md'
}: AnimatedLogoProps) {
  // Determine logo size based on prop
  const logoSizes = {
    sm: { width: 120, height_px: 24 },
    md: { width: 160, height_px: 32 },
    lg: { width: 200, height_px: 40 }
  };

  const { width, height_px } = logoSizes[size];

  return (
    <Link href="/" className={`inline-flex items-center gap-2 group ${className}`}>
      <motion.div
        variants={logoVariants}
        initial="initial"
        animate="animate"
        whileHover="hover"
        className="relative"
      >
        {/* Glow effect */}
        <motion.div
          className="absolute inset-0 bg-[#11a5e8]/20 rounded-full blur-xl"
          variants={glowVariants}
          animate="animate"
        />

        {/* Logo image */}
        <Image
          src="/images/sme-logo.png"
          alt="SME Analytica"
          width={width}
          height={height_px}
          className={`w-auto relative z-10 ${imageClassName}`}
          style={{ height: `${height_px}px` }}
          priority
        />
      </motion.div>

      {/* Logo text */}
      {showText && (
        <motion.span
          variants={textVariants}
          initial="initial"
          animate="animate"
          className={`font-bold tracking-tight text-[#d5dce2] ${textClassName}`}
        >
          Analytica
        </motion.span>
      )}
    </Link>
  );
}