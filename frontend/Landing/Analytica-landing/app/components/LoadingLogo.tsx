'use client';

import { motion, useAnimation, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import logo from '/public/images/sme-logo.png';
import { useEffect, useState } from 'react';
import { FiCpu } from 'react-icons/fi';

// Types
interface ParticleProps {
  index: number;
}

interface RingProps {
  delay: number;
  scale: number;
}

interface DataStreamProps {
  side: 'left' | 'right';
}

// Animation variants
const particleVariants = {
  animate: (index: number) => ({
    scale: [0, 1.5, 0],
    opacity: [0, 0.8, 0],
    x: index % 2 === 0 ? [0, 30, 0] : [0, -30, 0],
    y: index % 2 === 0 ? [0, 15, 0] : [0, -15, 0],
    transition: {
      duration: 3,
      repeat: Infinity,
      delay: index * 0.3,
      ease: "easeInOut",
    }
  })
};

const ringVariants = {
  animate: {
    scale: [1, 1.5, 2],
    opacity: [0.6, 0.3, 0],
    rotate: [0, 180],
    transition: {
      duration: 3,
      repeat: Infinity,
      ease: "easeInOut",
      times: [0, 0.5, 1],
    }
  }
};

const streamVariants = {
  left: {
    opacity: [0, 1, 0],
    x: [30, -30],
    transition: {
      duration: 2,
      repeat: Infinity,
      ease: "linear",
    }
  },
  right: {
    opacity: [0, 1, 0],
    x: [-30, 30],
    transition: {
      duration: 2,
      repeat: Infinity,
      ease: "linear",
    }
  }
};

const pulseVariants = {
  animate: {
    scale: [1, 1.4, 1],
    opacity: [0.3, 0.1, 0.3],
    transition: {
      duration: 2,
      repeat: Infinity,
      ease: "easeInOut",
    }
  }
};

const logoVariants = {
  animate: {
    x: [0, -1, 2, -1, 1, 0],
    opacity: [1, 0.9, 1],
    transition: {
      duration: 2,
      repeat: Infinity,
      ease: "easeInOut",
    }
  }
};

const glowVariants = {
  animate: {
    opacity: [0.5, 0.8, 0.5],
    scale: [1, 1.03, 1],
    transition: {
      duration: 2,
      repeat: Infinity,
      ease: "easeInOut",
    }
  }
};

const scanlineVariants = {
  animate: {
    y: ["100%", "-100%"],
    transition: {
      duration: 2.5,
      repeat: Infinity,
      ease: "easeInOut",
    }
  }
};

// Component for glowing particles
const GlowingParticle = ({ index }: ParticleProps) => (
  <motion.div
    className="absolute w-3 h-3 bg-gradient-to-r from-[#11a5e8] to-[#5f7790] rounded-full"
    style={{
      filter: 'blur(3px)',
      boxShadow: '0 0 15px rgba(17, 165, 232, 0.6)',
    }}
    initial={{ scale: 0, opacity: 0 }}
    variants={particleVariants}
    animate="animate"
    custom={index}
  />
);

// Component for animated rings
const Ring = ({ delay, scale }: RingProps) => (
  <motion.div
    className="absolute rounded-full"
    style={{
      width: '8rem',
      height: '8rem',
      border: '3px dashed rgba(17, 165, 232, 0.5)',
      filter: 'drop-shadow(0 0 12px rgba(17, 165, 232, 0.4))',
    }}
    variants={ringVariants}
    animate="animate"
    transition={{
      ...ringVariants.animate.transition,
      delay
    }}
  />
);

// Component for data streams
const DataStream = ({ side }: DataStreamProps) => {
  const streamLength = 4;
  const items = Array.from({ length: streamLength });

  return (
    <div className={`absolute top-1/2 ${side === 'left' ? '-left-12' : '-right-12'} transform -translate-y-1/2`}>
      {items.map((_, i) => (
        <motion.div
          key={i}
          className="h-2 w-8 bg-gradient-to-r from-[#11a5e8] to-[#5f7790] rounded-full mb-3"
          style={{
            filter: 'blur(1px)',
            boxShadow: '0 0 8px rgba(17, 165, 232, 0.4)',
          }}
          initial={{ opacity: 0, x: side === 'left' ? -30 : 30 }}
          variants={streamVariants}
          animate={side}
          transition={{
            ...streamVariants[side].transition,
            delay: i * 0.4
          }}
        />
      ))}
    </div>
  );
};

// Component for processing text animation
const ProcessingText = () => {
  const [currentPhrase, setCurrentPhrase] = useState(0);
  const phrases = [
    "🧠 Training AI Models...",
    "📊 Processing Market Data...",
    "💡 Generating Insights...",
    "🎯 Optimizing Strategy...",
    "📈 Analyzing Trends...",
    "🔄 Syncing Data...",
    "⚡ Powering Up...",
    "🌟 Almost Ready...",
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentPhrase(prev => (prev + 1) % phrases.length);
    }, 3000);

    return () => {
      clearInterval(interval);
    };
  }, [phrases.length]);

  return (
    <div className="relative h-20 flex items-center justify-center">
      <div className="relative px-8 py-4">
        <AnimatePresence mode="wait">
          <motion.p
            key={currentPhrase}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{
              duration: 0.5,
              ease: "easeInOut"
            }}
            className="text-center text-xl md:text-2xl text-[#d5dce2] font-mono tracking-wider whitespace-nowrap"
          >
            {phrases[currentPhrase]}
          </motion.p>
        </AnimatePresence>
      </div>
    </div>
  );
};

// Component for pulse effect
const PulseEffect = () => (
  <motion.div
    className="absolute w-full h-full rounded-full bg-[#11a5e8]/30"
    variants={pulseVariants}
    animate="animate"
  />
);

export default function LoadingLogo() {
  const keywords = [
    "INTELLIGENCE",
    "TRENDS",
    "INSIGHTS",
    "PRECISION",
    "PREDICTIVE",
    "REAL-TIME",
    "STRATEGY",
    "DYNAMICS"
  ];

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="relative w-full h-screen flex flex-col items-center justify-center bg-[#171f31] overflow-hidden"
    >
      {/* Background gradient */}
      <motion.div
        className="absolute w-[1200px] h-[1200px] bg-gradient-to-r from-[#171f31]/80 to-[#5f7790]/10 rounded-full blur-3xl"
        animate={{
          scale: [1, 1.1, 1],
          rotate: [0, 360],
        }}
        transition={{
          duration: 20,
          repeat: Infinity,
          ease: "linear",
        }}
      />

      {/* Animated floating keywords */}
      {keywords.map((word, index) => (
        <motion.div
          key={index}
          className="absolute text-[#d5dce2]/60 text-lg md:text-xl font-bold uppercase tracking-widest select-none pointer-events-none"
          style={{
            top: `${Math.random() * 80 + 10}%`,
            left: `${Math.random() * 80 + 10}%`,
          }}
          animate={{
            opacity: [0.4, 0.7, 0.4],
            scale: [0.95, 1.05, 0.95],
            y: [0, -10, 0],
          }}
          transition={{
            delay: index * 0.8,
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        >
          {word}
        </motion.div>
      ))}

      {/* Icon container with effects */}
      <div className="relative w-48 h-48 flex items-center justify-center mb-12">
        {/* Glowing particles - reduced number */}
        {Array.from({ length: 6 }).map((_, i) => (
          <GlowingParticle key={i} index={i} />
        ))}

        {/* Animated rings */}
        <Ring delay={0.5} scale={2} />
        <Ring delay={0.25} scale={1.5} />

        {/* Data streams */}
        <DataStream side="left" />
        <DataStream side="right" />

        {/* CPU Icon container */}
        <motion.div
          className="relative z-10 w-24 h-24 rounded-full bg-gradient-to-br from-[#5f7790] to-[#11a5e8] flex items-center justify-center shadow-lg"
          animate={{
            rotate: 360,
            scale: [0.95, 1.05, 0.95],
          }}
          transition={{
            rotate: { duration: 10, repeat: Infinity, ease: "linear" },
            scale: { duration: 3, repeat: Infinity, ease: "easeInOut" },
          }}
        >
          <PulseEffect />
          <FiCpu className="w-12 h-12 text-[#d5dce2]" />
        </motion.div>
      </div>

      {/* Logo */}
      <motion.div
        className="relative z-10"
        variants={logoVariants}
        animate="animate"
      >
        <Image
          src={logo}
          alt="SME Analytica Logo"
          width={300}
          height={112}
          priority
          className="object-contain"
        />

        {/* Logo glow effect */}
        <motion.div
          className="absolute inset-0 rounded-xl bg-gradient-to-r from-[#5f7790]/20 via-[#11a5e8]/20 to-[#5f7790]/20 blur-xl"
          variants={glowVariants}
          animate="animate"
        />

        {/* Scan-line shimmer */}
        <motion.div
          className="absolute inset-0 z-20 bg-gradient-to-t from-transparent via-[#d5dce2]/10 to-transparent pointer-events-none"
          variants={scanlineVariants}
          animate="animate"
          style={{
            backgroundSize: "100% 4px",
            mixBlendMode: "overlay",
          }}
        />
      </motion.div>

      {/* Processing text */}
      <div className="relative z-10 w-auto min-w-[300px] max-w-[90%] mt-8">
        <ProcessingText />
      </div>
    </motion.div>
  );
}