'use client';

import { motion } from 'framer-motion';
import CountUp from 'react-countup';
import { Spotlight } from '../../components/ui/spotlight-new';

export default function CTASection() {

  return (
    <>
      <section id="cta" className="relative overflow-hidden bg-[#171f31]">
        <Spotlight />

        <div className="container mx-auto px-6 lg:px-8 relative z-10 py-20">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            className="text-center max-w-4xl mx-auto relative"
          >
            {/* Trust indicators */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.3 }}
              className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-10"
            >
              {[
                { label: 'Active Testers', value: '50+' },
                { label: 'Data Points Analyzed', value: '500K+' },
                { label: 'Platform Uptime', value: '99.9%' },
                { label: 'Security Audits', value: '5+' },
              ].map((stat) => (
                <div key={stat.label} className="text-center">
                  <div className="text-3xl font-extrabold text-[#d5dce2] mb-2">
                    <CountUp end={Number(stat.value.replace(/\D/g, ''))} duration={2} separator="," />
                    {stat.value.match(/[^\d]/g)?.join('')}
                  </div>
                  <div className="text-[#d5dce2]/80 text-sm">{stat.label}</div>
                </div>
              ))}
            </motion.div>

            {/* Security badges */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.4 }}
              className="mt-16 flex flex-wrap justify-center gap-4"
            >
              {['GDPR', 'ISO 27001', 'SOC 2'].map((badge) => (
                <div
                  key={badge}
                  className="px-4 py-2 bg-[#171f31] rounded-full text-[#d5dce2] text-sm font-medium border border-[#5f7790] hover:scale-105 hover:border-[#11a5e8] transition-all shadow-[0_0_10px_rgba(17,165,232,0.2)]"
                >
                  {badge} Compliant
                </div>
              ))}
            </motion.div>


          </motion.div>
        </div>
      </section>
    </>
  );
}
