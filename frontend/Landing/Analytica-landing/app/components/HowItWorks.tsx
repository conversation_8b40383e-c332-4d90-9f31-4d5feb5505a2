'use client';

import { motion, useScroll, useSpring, useTransform } from 'framer-motion';
import { useState, useRef } from 'react';
import InterestForm from './InterestForm';
import { useLanguage } from '../../lib/LanguageProvider';
import { FiDatabase, FiCpu, FiTrendingUp } from 'react-icons/fi';

export default function HowItWorks() {
  const { language, content } = useLanguage();
  const [isInterestFormOpen, setIsInterestFormOpen] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });

  const springProgress = useSpring(scrollYProgress, {
    stiffness: 100,
    damping: 30,
    restDelta: 0.001
  });

  const steps = [
    {
      icon: FiDatabase,
      title: content.howItWorks.steps.connect.title[language],
      description: content.howItWorks.steps.connect.description[language],
      visualColor: "hsl(199, 80%, 65%)", // Data Streams color
      delay: 0
    },
    {
      icon: FiCpu,
      title: content.howItWorks.steps.analyze.title[language],
      description: content.howItWorks.steps.analyze.description[language],
      visualColor: "hsl(215, 40%, 50%)", // Core AI color
      delay: 0.2
    },
    {
      icon: FiTrendingUp,
      title: content.howItWorks.steps.grow.title[language],
      description: content.howItWorks.steps.grow.description[language],
      visualColor: "hsl(199, 100%, 75%)", // Insights color
      delay: 0.4
    }
  ];

  return (
    <section id="how-it-works" className="relative bg-[#171f31] overflow-hidden py-20" ref={containerRef}>
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-[0.02]">
        <div 
          className="h-full w-full"
          style={{
            backgroundImage: `
              linear-gradient(rgba(213, 220, 226, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(213, 220, 226, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '40px 40px',
          }}
        />
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 pt-10 pb-20">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="text-center mb-16 relative"
        >
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-[#d5dce2] mb-6 tracking-tight">
            {content.howItWorks.title[language]}
          </h2>
          <p className="text-lg md:text-xl text-[#d5dce2]/80 max-w-2xl mx-auto leading-relaxed">
            {language === 'es' 
              ? 'Transforma datos complejos en crecimiento empresarial con nuestra metodología probada'
              : 'Transform complex data into business growth with our proven methodology'
            }
          </p>
        </motion.div>

        {/* 3-Step Process */}
        <div className="relative max-w-6xl mx-auto">
          {/* Connection Lines */}
          <div className="hidden md:block absolute top-1/2 left-0 right-0 h-0.5 bg-gradient-to-r from-[#11a5e8]/30 via-[#5f7790]/30 to-[#11a5e8]/30 transform -translate-y-1/2 z-0" />
          
          <div className="grid md:grid-cols-3 gap-8 md:gap-12 relative z-10">
            {steps.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: step.delay }}
                className="relative"
              >
                {/* Step Card */}
                <div className="relative bg-[#171f31] border border-[#5f7790]/20 rounded-2xl p-8 text-center group hover:border-[#11a5e8]/40 transition-all duration-300">
                  {/* Glowing Background */}
                  <div 
                    className="absolute inset-0 rounded-2xl opacity-5 blur-xl transition-opacity duration-300 group-hover:opacity-10"
                    style={{ background: step.visualColor }}
                  />
                  
                  {/* Step Number */}
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <div 
                      className="w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold text-white"
                      style={{ background: step.visualColor }}
                    >
                      {index + 1}
                    </div>
                  </div>

                  {/* Icon */}
                  <div className="relative mb-6">
                    <div 
                      className="w-16 h-16 mx-auto rounded-full flex items-center justify-center relative overflow-hidden"
                      style={{ background: `${step.visualColor}15` }}
                    >
                      <step.icon 
                        className="w-8 h-8 relative z-10" 
                        style={{ color: step.visualColor }}
                      />
                      {/* Animated glow */}
                      <motion.div
                        className="absolute inset-0 rounded-full"
                        style={{ background: step.visualColor }}
                        animate={{ 
                          opacity: [0.1, 0.3, 0.1],
                          scale: [1, 1.1, 1]
                        }}
                        transition={{ 
                          repeat: Infinity, 
                          duration: 3,
                          delay: index * 0.5
                        }}
                      />
                    </div>
                  </div>

                  {/* Content */}
                  <h3 className="text-xl font-semibold text-[#d5dce2] mb-4">
                    {step.title}
                  </h3>
                  <p className="text-[#d5dce2]/70 leading-relaxed">
                    {step.description}
                  </p>

                  {/* Visual Flow Indicator */}
                  {index < steps.length - 1 && (
                    <div className="hidden md:block absolute top-1/2 -right-6 transform -translate-y-1/2">
                      <motion.div
                        className="flex items-center"
                        animate={{ x: [0, 5, 0] }}
                        transition={{ repeat: Infinity, duration: 2, delay: index * 0.3 }}
                      >
                        <div className="w-3 h-3 rounded-full bg-[#11a5e8] opacity-60" />
                        <div className="w-2 h-0.5 bg-[#11a5e8] opacity-40 mx-1" />
                        <div className="w-1 h-1 rounded-full bg-[#11a5e8] opacity-20" />
                      </motion.div>
                    </div>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* CTA Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.8 }}
          className="text-center mt-16"
        >
          <motion.button
            className="relative px-12 py-5 bg-[#11a5e8] text-white rounded-full font-semibold text-lg shadow-xl overflow-hidden group"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setIsInterestFormOpen(true)}
          >
            <span className="relative z-10">
              {language === 'es' ? 'Comenzar tu Viaje de Crecimiento' : 'Start Your Growth Journey'}
            </span>
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-[#5f7790] to-[#11a5e8] opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            />
          </motion.button>
        </motion.div>
      </div>


      {/* Interest Form Modal */}
      <InterestForm isOpen={isInterestFormOpen} onClose={() => setIsInterestFormOpen(false)} />
    </section>
  );
}