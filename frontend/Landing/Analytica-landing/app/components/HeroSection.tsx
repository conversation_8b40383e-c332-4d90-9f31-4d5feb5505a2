'use client';

import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiArrowRight } from 'react-icons/fi';
import { ContainerTextFlip } from '../../components/ui/container-text-flip';
import { useLanguage } from '../../lib/LanguageProvider';

export default function HeroSection() {
  const { language, content } = useLanguage();
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX - window.innerWidth / 2) * 0.005,
        y: (e.clientY - window.innerHeight / 2) * 0.005,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <>
      <section className="relative min-h-screen overflow-hidden bg-[#171f31] flex flex-col justify-center">
        {/* Background Patterns */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute inset-0 opacity-10">
            <motion.div 
              className="absolute top-1/4 left-1/4 w-64 h-64 bg-[#5f7790] rounded-full mix-blend-multiply filter blur-3xl"
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.3, 0.6, 0.3],
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
            <motion.div 
              className="absolute top-1/3 right-1/4 w-64 h-64 bg-[#11a5e8] rounded-full mix-blend-multiply filter blur-3xl"
              animate={{
                scale: [1.2, 1, 1.2],
                opacity: [0.6, 0.3, 0.6],
              }}
              transition={{
                duration: 10,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 2,
              }}
            />
            <motion.div 
              className="absolute bottom-1/4 left-1/3 w-64 h-64 bg-[#d5dce2] rounded-full mix-blend-multiply filter blur-3xl"
              animate={{
                scale: [1, 1.3, 1],
                opacity: [0.2, 0.5, 0.2],
              }}
              transition={{
                duration: 12,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 4,
              }}
            />
          </div>
        </div>

        {/* Subtle Grid Pattern */}
        <div
          className="absolute inset-0 opacity-[0.02]"
          style={{
            backgroundImage: `
              linear-gradient(rgba(213, 220, 226, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(213, 220, 226, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px',
            transform: `translate(${mousePosition.x * 2}px, ${mousePosition.y * 2}px)`,
          }}
        />

        {/* Main Hero Content */}
        <div className="relative z-10 flex flex-col-reverse md:flex-row items-center justify-center min-h-[70vh] container mx-auto px-6 py-12 gap-12">
          {/* Left: Write-up */}
          <div className="flex-1 flex flex-col items-start justify-center text-left max-w-xl">
            <motion.h1
              className="text-4xl md:text-5xl lg:text-6xl font-bold text-[#d5dce2] leading-tight mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              {content.hero.headline[language]}
            </motion.h1>
            <motion.p
              className="text-lg md:text-xl text-[#d5dce2]/80 mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              {content.hero.subheadline[language]}
            </motion.p>
            <div className="flex flex-col sm:flex-row gap-4">
              <motion.a
                href="https://calendly.com/smeanalytica/30min"
                target="_blank"
                rel="noopener noreferrer"
                className="px-8 py-4 bg-[#11a5e8] text-white rounded-full text-lg font-medium hover:shadow-lg hover:scale-105 transition-all duration-300 flex items-center gap-2"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {content.hero.cta.primary[language]} <FiArrowRight className="inline-block" />
              </motion.a>
              <motion.a
                href="/features"
                className="px-8 py-4 bg-transparent text-[#d5dce2] rounded-full text-lg font-medium border-2 border-[#5f7790] hover:shadow-lg hover:scale-105 transition-all duration-300 flex items-center gap-2"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {content.hero.cta.secondary[language]}
              </motion.a>
            </div>
          </div>
          {/* Right: Phone Image */}
          <div className="flex-1 flex items-center justify-center w-full max-w-md">
            <motion.div
              className="relative"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
            >
              {/* Main phone image */}
              <motion.img
                src="/images/sme-ios/1.png"
                alt="Panel de Control SME Analytica"
                className="mx-auto rounded-2xl object-cover h-[500px] w-auto"
                draggable={false}
                whileHover={{
                  scale: 1.03
                }}
                transition={{ type: "spring", stiffness: 300 }}
              />
            </motion.div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="container mx-auto px-6 pb-20">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-8">
            {[
              content.stats.roi,
              content.stats.setup,
              content.stats.companies,
              content.stats.industries
            ].map((stat, index) => (
              <motion.div
                key={index}
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.2 }}
              >
                <div className="text-4xl font-bold text-[#11a5e8]">{stat.value}</div>
                <div className="text-[#d5dce2] mt-2">{stat.label[language]}</div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Scroll Indicator */}
        <motion.div
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          animate={{
            y: [0, 10, 0],
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        >
          <div className="w-6 h-10 border-2 border-[#5f7790] rounded-full flex justify-center">
            <motion.div
              className="w-1.5 h-1.5 bg-[#11a5e8] rounded-full mt-2"
              animate={{
                y: [0, 12, 0],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
          </div>
        </motion.div>
      </section>
    </>
  );
}
