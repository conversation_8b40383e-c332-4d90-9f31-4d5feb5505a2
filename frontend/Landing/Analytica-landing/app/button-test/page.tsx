'use client';

import { motion } from 'framer-motion';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import { Spotlight } from '../../components/ui/spotlight-new';
import { GradientButton } from '../../components/ui/gradient-button';

export default function ButtonTest() {
  return (
    <>
      <Navbar />
      <div className="min-h-screen bg-[#171f31] pt-32 relative overflow-hidden">
        <div className="absolute inset-0 z-0">
          <Spotlight 
            gradientFirst="radial-gradient(68.54% 68.72% at 55.02% 31.46%, hsla(200, 100%, 85%, .04) 0, hsla(200, 100%, 55%, .01) 50%, hsla(200, 100%, 45%, 0) 80%)"
            gradientSecond="radial-gradient(50% 50% at 50% 50%, hsla(200, 100%, 85%, .03) 0, hsla(200, 100%, 55%, .01) 80%, transparent 100%)"
            gradientThird="radial-gradient(50% 50% at 50% 50%, hsla(200, 100%, 85%, .02) 0, hsla(200, 100%, 45%, .01) 80%, transparent 100%)"
          />
        </div>
        
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="max-w-4xl mx-auto"
          >
            <h1 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-[#11a5e8] to-[#5f7790] mb-8">
              Gradient Button Examples
            </h1>
            
            <div className="space-y-12">
              <section className="bg-[#171f31]/50 p-6 rounded-lg border border-[#5f7790]/20">
                <h2 className="text-2xl font-semibold text-[#11a5e8] mb-6">
                  Button Sizes
                </h2>
                <div className="flex flex-wrap gap-6 items-center">
                  <GradientButton size="sm">
                    Small Button
                  </GradientButton>
                  
                  <GradientButton size="md">
                    Medium Button
                  </GradientButton>
                  
                  <GradientButton size="lg">
                    Large Button
                  </GradientButton>
                </div>
              </section>

              <section className="bg-[#171f31]/50 p-6 rounded-lg border border-[#5f7790]/20">
                <h2 className="text-2xl font-semibold text-[#11a5e8] mb-6">
                  Button Variants
                </h2>
                <div className="flex flex-wrap gap-6 items-center">
                  <GradientButton variant="primary">
                    Primary Button
                  </GradientButton>
                  
                  <GradientButton variant="secondary">
                    Secondary Button
                  </GradientButton>
                </div>
              </section>

              <section className="bg-[#171f31]/50 p-6 rounded-lg border border-[#5f7790]/20">
                <h2 className="text-2xl font-semibold text-[#11a5e8] mb-6">
                  Buttons with Icons
                </h2>
                <div className="flex flex-wrap gap-6 items-center">
                  <GradientButton 
                    icon={
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                    }
                  >
                    Get Started
                  </GradientButton>
                  
                  <GradientButton 
                    icon={
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    }
                  >
                    Contact Us
                  </GradientButton>
                </div>
              </section>

              <section className="bg-[#171f31]/50 p-6 rounded-lg border border-[#5f7790]/20">
                <h2 className="text-2xl font-semibold text-[#11a5e8] mb-6">
                  Link Buttons
                </h2>
                <div className="flex flex-wrap gap-6 items-center">
                  <GradientButton href="/privacy-policy">
                    Privacy Policy
                  </GradientButton>
                  
                  <GradientButton href="/terms">
                    Terms of Service
                  </GradientButton>
                  
                  <GradientButton href="/gdpr">
                    GDPR Compliance
                  </GradientButton>
                </div>
              </section>
            </div>
          </motion.div>
        </div>
      </div>
      <Footer />
    </>
  );
}
