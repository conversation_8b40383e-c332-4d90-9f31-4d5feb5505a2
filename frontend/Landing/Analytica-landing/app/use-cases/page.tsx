import type { Metadata } from 'next';
import UseCasesClient from './UseCasesClient';


export const metadata: Metadata = {
  title: 'AI Analytics Use Cases by Industry | SME Analytica Solutions',
  description: 'Discover how different SMEs use AI analytics: retail businesses, service companies, e-commerce stores, manufacturing, and marketing agencies. Real scenarios, real results.',
  keywords: [
    'AI for small retail businesses',
    'analytics for service-based companies',
    'e-commerce sales forecasting',
    'manufacturing analytics SME',
    'marketing agency analytics',
    'small business AI use cases',
    'industry-specific analytics solutions'
  ],
  openGraph: {
    title: 'AI Analytics Use Cases by Industry | SME Analytica',
    description: 'See how SMEs across different industries leverage AI analytics to solve real business challenges and drive growth.',
    url: 'https://smeanalytica.dev/use-cases',
  },
};

export default function UseCasesPage() {
  return <UseCasesClient />;
} 