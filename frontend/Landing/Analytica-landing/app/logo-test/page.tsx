'use client';

import { motion } from 'framer-motion';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import { Spotlight } from '../../components/ui/spotlight-new';
import AnimatedLogo from '../components/AnimatedLogo';
import { useState } from 'react';
import LoadingLogo from '../components/LoadingLogo';

export default function LogoTest() {
  const [showLoading, setShowLoading] = useState(false);
  
  return (
    <>
      {showLoading ? (
        <LoadingLogo />
      ) : (
        <>
          <Navbar />
          <div className="min-h-screen bg-[#171f31] pt-32 relative overflow-hidden">
            <div className="absolute inset-0 z-0">
              <Spotlight 
                gradientFirst="radial-gradient(68.54% 68.72% at 55.02% 31.46%, hsla(200, 100%, 85%, .04) 0, hsla(200, 100%, 55%, .01) 50%, hsla(200, 100%, 45%, 0) 80%)"
                gradientSecond="radial-gradient(50% 50% at 50% 50%, hsla(200, 100%, 85%, .03) 0, hsla(200, 100%, 55%, .01) 80%, transparent 100%)"
                gradientThird="radial-gradient(50% 50% at 50% 50%, hsla(200, 100%, 85%, .02) 0, hsla(200, 100%, 45%, .01) 80%, transparent 100%)"
              />
            </div>
            
            <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16 relative z-10">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="max-w-4xl mx-auto"
              >
                <h1 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-[#11a5e8] to-[#5f7790] mb-8">
                  Logo Components
                </h1>
                
                <div className="space-y-12">
                  <section className="bg-[#171f31]/50 p-6 rounded-lg border border-[#5f7790]/20">
                    <h2 className="text-2xl font-semibold text-[#11a5e8] mb-6">
                      AnimatedLogo Sizes
                    </h2>
                    <div className="flex flex-col gap-8">
                      <div className="flex items-center gap-4">
                        <span className="text-[#d5dce2] w-20">Small:</span>
                        <AnimatedLogo size="sm" />
                      </div>
                      
                      <div className="flex items-center gap-4">
                        <span className="text-[#d5dce2] w-20">Medium:</span>
                        <AnimatedLogo size="md" />
                      </div>
                      
                      <div className="flex items-center gap-4">
                        <span className="text-[#d5dce2] w-20">Large:</span>
                        <AnimatedLogo size="lg" />
                      </div>
                    </div>
                  </section>

                  <section className="bg-[#171f31]/50 p-6 rounded-lg border border-[#5f7790]/20">
                    <h2 className="text-2xl font-semibold text-[#11a5e8] mb-6">
                      AnimatedLogo Variants
                    </h2>
                    <div className="flex flex-col gap-8">
                      <div className="flex items-center gap-4">
                        <span className="text-[#d5dce2] w-20">With Text:</span>
                        <AnimatedLogo showText={true} />
                      </div>
                      
                      <div className="flex items-center gap-4">
                        <span className="text-[#d5dce2] w-20">Icon Only:</span>
                        <AnimatedLogo showText={false} />
                      </div>
                    </div>
                  </section>

                  <section className="bg-[#171f31]/50 p-6 rounded-lg border border-[#5f7790]/20">
                    <h2 className="text-2xl font-semibold text-[#11a5e8] mb-6">
                      Loading Logo
                    </h2>
                    <div className="flex flex-col items-center gap-6">
                      <p className="text-[#d5dce2]">
                        Click the button below to see the full-screen loading logo animation.
                      </p>
                      
                      <button
                        onClick={() => setShowLoading(true)}
                        className="px-6 py-3 bg-gradient-to-r from-[#5f7790] to-[#11a5e8] text-[#d5dce2] rounded-lg hover:opacity-90 transition-opacity"
                      >
                        Show Loading Logo
                      </button>
                    </div>
                  </section>
                </div>
              </motion.div>
            </div>
          </div>
          <Footer />
        </>
      )}
    </>
  );
}
