# SME Analytica Landing Page

The official landing page for SME Analytica, an AI-driven analytics and business intelligence platform designed specifically for small and medium enterprises.

## Features

- 🎯 Modern, responsive design with Next.js 14 and Tailwind CSS
- 🌙 Dark mode support
- 🎨 Smooth animations and transitions
- 📱 Mobile-first approach
- 🔍 SEO optimized
- 🚀 Performance optimized
- 📊 Interactive components
- 🔒 Privacy and Terms pages
- 📝 Contact form with backend integration

## Tech Stack

- [Next.js 14](https://nextjs.org/) - React framework
- [Tailwind CSS](https://tailwindcss.com/) - Styling
- [TypeScript](https://www.typescriptlang.org/) - Type safety
- [Vercel](https://vercel.com/) - Deployment platform

## Getting Started

### Prerequisites

- Node.js 18.x or later
- npm or yarn package manager

### Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/Analytica-landing.git
cd Analytica-landing
```

2. Install dependencies:
```bash
npm install
# or
yarn install
```

3. Create a `.env.local` file in the root directory and add necessary environment variables:
```env
NEXT_PUBLIC_API_URL=your_api_url
```

4. Start the development server:
```bash
npm run dev
# or
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) to view the site in your browser.

## Project Structure

```
Analytica-landing/
├── app/                    # Next.js app directory
│   ├── components/         # Reusable components
│   ├── providers/         # Context providers
│   ├── styles/           # Global styles
│   └── ...               # Page routes
├── public/                # Static assets
├── styles/                # CSS modules
└── ...                   # Config files
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript compiler check

## Deployment

The site is automatically deployed to Vercel on push to the main branch. You can also deploy manually:

```bash
npm run build
vercel --prod
```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is proprietary and confidential. All rights reserved.

## Contact

SME Analytica Team - [Follow us on X (Twitter)](https://x.com/smeanalytica?s=21)

Project Link: [https://smeanalytica.dev](https://smeanalytica.dev)
