# Changelog

All notable changes to this project will be documented in this file.

## [Unreleased] - 2025-06-16

### Fixed - SSL Certificate Issue Resolution

#### **Problem**
- **Issue**: Safari showing "This Connection Is Not Private" error when accessing `smeanalytica.dev`
- **Error Details**: Certificate name mismatch - SSL certificate was not matching the domain being accessed
- **Affected URLs**: 
  - `https://smeanalytica.dev` ❌ (SSL certificate error)
  - `https://www.smeanalytica.dev` ❌ (SSL certificate error)

#### **Root Cause Analysis**
1. **DNS Misconfiguration**: Domain `smeanalytica.dev` was pointing to incorrect server (`***********`) instead of Vercel's servers
2. **Domain Not Connected**: While the domain existed in Vercel account, it wasn't properly connected to the project
3. **Wrong SSL Certificate**: The server at `***********` had a different/invalid SSL certificate

#### **Resolution Steps**

##### **Step 1: DNS Investigation**
- Identified that `smeanalytica.dev` was resolving to `***********` (wrong server)
- Confirmed V<PERSON><PERSON> was ready to serve the site by testing with `curl -H "Host: smeanalytica.dev" https://cname.vercel-dns.com`

##### **Step 2: DNS Correction in Porkbun**
- **Before**: A record pointing `smeanalytica.dev` to `***********`
- **After**: ALIAS record pointing `smeanalytica.dev` to `cname.vercel-dns.com`
- **Note**: CNAME cannot be used for root domains, so ALIAS was the correct choice

##### **Step 3: Domain Connection in Vercel**
- Verified both domains were properly configured in Vercel project:
  - `smeanalytica.dev` ✅
  - `www.smeanalytica.dev` ✅
- Ensured domains were connected to the correct project (`sme-analytica`)

##### **Step 4: SSL Certificate Provisioning**
- Waited for DNS propagation (10-15 minutes)
- Let's Encrypt automatically provisioned new SSL certificates for both domains
- Verified certificate validity with `curl -I` commands

#### **Final Outcome** ✅

##### **Working URLs**
- ✅ `https://smeanalytica.dev` - Fully functional with valid SSL
- ✅ `https://www.smeanalytica.dev` - Fully functional with valid SSL

##### **SSL Certificate Details**
- **Issuer**: R10 (Let's Encrypt)
- **Valid Until**: Tuesday, 2 September 2025 at 02:28:50 Central European Summer Time
- **Subject**: `CN=smeanalytica.dev`
- **SAN**: `DNS:smeanalytica.dev`

##### **DNS Configuration (Final)**
- **Root Domain**: `smeanalytica.dev` → ALIAS → `cname.vercel-dns.com`
- **WWW Subdomain**: `www.smeanalytica.dev` → CNAME → `cname.vercel-dns.com`
- **API Subdomain**: `api.smeanalytica.dev` → ALIAS → `analytica-api.onrender.com`

#### **Technical Notes**
- **Platform**: Vercel (Frontend), Render (API)
- **Domain Registrar**: Porkbun
- **SSL Provider**: Let's Encrypt (via Vercel)
- **Issue Duration**: ~2 hours (including troubleshooting)
- **Resolution Time**: ~15 minutes (after correct diagnosis)

#### **Lessons Learned**
1. Always verify DNS resolution with `nslookup` or `dig` before assuming SSL issues
2. ALIAS records are required for root domains when pointing to CNAME targets
3. Vercel requires explicit domain addition to projects for SSL certificate provisioning
4. DNS propagation can take 10-60 minutes depending on TTL settings

#### **Commands Used for Verification**
```bash
# DNS verification
nslookup smeanalytica.dev
ping -c 3 smeanalytica.dev

# SSL verification  
curl -I https://smeanalytica.dev
curl -I https://www.smeanalytica.dev

# Certificate details
echo | openssl s_client -servername smeanalytica.dev -connect smeanalytica.dev:443 2>/dev/null | openssl x509 -text -noout | grep -E "(Subject:|DNS:)"
```

---

**Contact**: <EMAIL>  
**Resolved By**: AI Assistant  
**Date**: June 16, 2025 