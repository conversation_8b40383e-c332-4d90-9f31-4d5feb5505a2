'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { Language, LanguageManager, translations } from '../lib/i18n';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (section: keyof typeof translations, key: string) => string;
  isLoading: boolean;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setCurrentLanguage] = useState<Language>('en');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Initialize language detection
    const detected = LanguageManager.initializeLanguage();
    setCurrentLanguage(detected);
    setIsLoading(false);
  }, []);

  const setLanguage = (lang: Language) => {
    LanguageManager.setLanguage(lang);
    setCurrentLanguage(lang);
  };

  const t = (section: keyof typeof translations, key: string): string => {
    return LanguageManager.getText(section, key, language);
  };

  return (
    <LanguageContext.Provider value={{
      language,
      setLanguage,
      t,
      isLoading
    }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}
