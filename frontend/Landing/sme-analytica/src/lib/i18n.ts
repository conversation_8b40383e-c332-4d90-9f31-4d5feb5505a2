/**
 * Internationalization system for SME Analytica Landing Page
 * Supporting Spanish and English with automatic language detection
 */

export const languages = {
  es: 'Español',
  en: 'English'
} as const;

export type Language = keyof typeof languages;

// Content translations
export const translations = {
  // Navigation
  navbar: {
    solutions: {
      es: 'Soluciones',
      en: 'Solutions'
    },
    products: {
      es: 'Productos',
      en: 'Products'
    },
    features: {
      es: 'Características',
      en: 'Features'
    },
    about: {
      es: 'Acerca de',
      en: 'About'
    }
  },

  // Solutions dropdown
  solutions: {
    smeAnalytics: {
      es: 'SME Analytics',
      en: 'SME Analytics'
    },
    restaurantSystem: {
      es: 'Sistema de Restaurante',
      en: 'Restaurant System'
    },
    connectoVoice: {
      es: 'Connecto Voice',
      en: 'Connecto Voice'
    },
    aiInsights: {
      es: 'Insights de IA',
      en: 'AI Insights'
    }
  },

  // Features dropdown
  featuresDropdown: {
    aiRecommendations: {
      es: 'Recomendaciones de IA',
      en: 'AI Recommendations'
    },
    marketAnalysis: {
      es: 'Análisis de Mercado',
      en: 'Market Analysis'
    },
    dynamicPricing: {
      es: 'Precios Dinámicos',
      en: 'Dynamic Pricing'
    },
    sentimentAnalysis: {
      es: 'Análisis de Sentimientos',
      en: 'Sentiment Analysis'
    }
  },

  // About dropdown
  about: {
    aboutUs: {
      es: 'Acerca de Nosotros',
      en: 'About Us'
    },
    contact: {
      es: 'Contacto',
      en: 'Contact'
    },
    team: {
      es: 'Nuestro Equipo',
      en: 'Our Team'
    },
    careers: {
      es: 'Carreras',
      en: 'Careers'
    }
  },

  // Hero Section
  hero: {
    greeting: {
      es: 'Hola',
      en: 'Hello'
    },
    title: {
      es: 'Transforma Tu Negocio con IA',
      en: 'Transform Your Business with AI'
    },
    tagline: {
      es: 'Analizar • Optimizar • Escalar',
      en: 'Analyze • Optimize • Scale'
    },
    subtitle: {
      es: 'Transforma datos sin procesar en insights accionables con nuestra suite de análisis impulsada por IA. Desde operaciones de restaurantes hasta inteligencia empresarial, entregamos la claridad que necesitas para tomar decisiones confiadas.',
      en: 'Transform raw data into actionable insights with our AI-powered analytics suite. From restaurant operations to business intelligence, we deliver the clarity you need to make confident decisions.'
    },
    description: {
      es: 'Donde cada punto de datos cuenta una historia, cada insight impulsa la acción, y cada oportunidad se convierte en crecimiento.',
      en: 'Where every data point tells a story, every insight drives action, and every opportunity becomes growth.'
    },
    taglineEnd: {
      es: 'Tu camino unificado hacia la evolución empresarial inteligente.',
      en: 'Your unified path to intelligent business evolution.'
    },
    cta: {
      primary: {
        es: 'Comienza Tu Viaje con IA',
        en: 'Start Your AI Journey'
      },
      secondary: {
        es: 'Explorar Características',
        en: 'Explore Features'
      }
    }
  },

  // Products Section
  products: {
    smeAnalytics: {
      title: {
        es: 'SME Analytics',
        en: 'SME Analytics'
      },
      description: {
        es: 'Análisis empresarial con IA',
        en: 'AI business analytics'
      }
    },
    rosRestaurant: {
      title: {
        es: 'ROS Restaurant',
        en: 'ROS Restaurant'
      },
      description: {
        es: 'Operaciones de restaurante',
        en: 'Restaurant operations'
      }
    },
    connectoVoice: {
      title: {
        es: 'Connecto Voice',
        en: 'Connecto Voice'
      },
      description: {
        es: 'Sistema de voz con IA',
        en: 'AI voice system'
      }
    },
    marketIntelligence: {
      title: {
        es: 'Inteligencia de Mercado',
        en: 'Market Intelligence'
      },
      description: {
        es: 'Análisis de mercado',
        en: 'Market analysis'
      }
    }
  },

  // Features Section
  features: {
    title: {
      es: 'Características Potentes',
      en: 'Powerful Features'
    },
    subtitle: {
      es: 'Todo lo que necesitas para hacer crecer tu negocio',
      en: 'Everything you need to grow your business'
    }
  },

  // Contact Section
  contact: {
    title: {
      es: 'Contáctanos',
      en: 'Contact Us'
    },
    subtitle: {
      es: 'Listo para transformar tu negocio?',
      en: 'Ready to transform your business?'
    }
  },

  // Common
  common: {
    learnMore: {
      es: 'Saber Más',
      en: 'Learn More'
    },
    getStarted: {
      es: 'Comenzar',
      en: 'Get Started'
    },
    bookDemo: {
      es: 'Reservar Demo',
      en: 'Book Demo'
    }
  }
};

// Language detection and management
export class LanguageManager {
  private static currentLanguage: Language = 'en'; // Default to English

  static getCurrentLanguage(): Language {
    return this.currentLanguage;
  }

  static setLanguage(lang: Language): void {
    this.currentLanguage = lang;
    if (typeof window !== 'undefined') {
      localStorage.setItem('sme-language', lang);
      document.documentElement.lang = lang;
    }
  }

  static detectLanguage(): Language {
    if (typeof window === 'undefined') return 'en';
    
    // Check localStorage first
    const stored = localStorage.getItem('sme-language') as Language;
    if (stored && Object.keys(languages).includes(stored)) {
      return stored;
    }

    // Auto-detect from browser/system language
    const browserLang = navigator.language.split('-')[0] as Language;
    return Object.keys(languages).includes(browserLang) ? browserLang : 'en';
  }

  static initializeLanguage(): Language {
    const detected = this.detectLanguage();
    this.setLanguage(detected);
    return detected;
  }

  static getText<T extends keyof typeof translations>(
    section: T,
    key: string,
    lang?: Language
  ): string {
    const currentLang = lang || this.getCurrentLanguage();
    const sectionTranslations = translations[section] as any;



    if (!sectionTranslations) {
      return key; // Fallback to key if section not found
    }

    // Handle nested keys like 'cta.primary'
    const keys = key.split('.');
    let value = sectionTranslations;

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        return key; // Fallback to key if path not found
      }
    }

    if (value && typeof value === 'object' && currentLang in value) {
      return value[currentLang];
    } else if (value && typeof value === 'object' && 'en' in value) {
      return value['en'];
    }

    return key; // Final fallback
  }
}

export default LanguageManager;
