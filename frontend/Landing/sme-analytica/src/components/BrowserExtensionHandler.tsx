'use client';

import { useEffect } from 'react';

/**
 * <PERSON><PERSON>erExtensionHandler component to handle browser extension interference
 * This component detects and cleans up attributes added by browser extensions
 * that can cause hydration mismatches
 */
export default function BrowserExtensionHandler() {
  useEffect(() => {
    // Function to clean up browser extension attributes
    const cleanupExtensionAttributes = () => {
      // Common browser extension attributes that cause hydration issues
      const extensionAttributes = [
        '__processed_',
        'data-extension-',
        'data-adblock',
        'data-grammarly',
        'data-lastpass',
        'data-honey',
        'data-metamask',
        'cz-shortcut-listen',
        'data-new-gr-c-s-check-loaded',
        'data-gr-ext-installed'
      ];

      // Clean up body element
      const body = document.body;
      if (body) {
        // Remove extension attributes
        Array.from(body.attributes).forEach(attr => {
          if (extensionAttributes.some(ext => attr.name.includes(ext))) {
            body.removeAttribute(attr.name);
          }
        });

        // Remove extension classes
        const extensionClasses = [
          'extension-',
          'adblock-',
          'grammarly-',
          'lastpass-',
          'honey-',
          'metamask-'
        ];

        extensionClasses.forEach(className => {
          Array.from(body.classList).forEach(cls => {
            if (cls.includes(className)) {
              body.classList.remove(cls);
            }
          });
        });
      }

      // Clean up html element
      const html = document.documentElement;
      if (html) {
        Array.from(html.attributes).forEach(attr => {
          if (extensionAttributes.some(ext => attr.name.includes(ext))) {
            html.removeAttribute(attr.name);
          }
        });
      }
    };

    // Run cleanup immediately
    cleanupExtensionAttributes();

    // Set up a MutationObserver to watch for extension modifications
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes') {
          const target = mutation.target as Element;
          const attributeName = mutation.attributeName;
          
          if (attributeName && (
            attributeName.includes('__processed_') ||
            attributeName.includes('data-extension-') ||
            attributeName.includes('data-adblock') ||
            attributeName.includes('data-grammarly') ||
            attributeName.includes('data-lastpass') ||
            attributeName.includes('data-honey') ||
            attributeName.includes('data-metamask')
          )) {
            // Remove the problematic attribute
            target.removeAttribute(attributeName);
          }
        }
      });
    });

    // Start observing
    observer.observe(document.documentElement, {
      attributes: true,
      attributeOldValue: true,
      subtree: true
    });

    // Cleanup function
    return () => {
      observer.disconnect();
    };
  }, []);

  // This component doesn't render anything
  return null;
}
