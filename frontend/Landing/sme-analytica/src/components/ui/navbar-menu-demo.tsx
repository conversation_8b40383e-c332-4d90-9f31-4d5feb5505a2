"use client";
import React, { useState } from "react";
import { HoveredLink, Menu, MenuItem, ProductItem } from "@/components/ui/navbar-menu";
import { cn } from "@/lib/utils";

export default function NavbarDemo() {
  return (
    <div className="relative w-full flex items-center justify-center">
      <Navbar className="top-2" />
    </div>
  );
}

function Navbar({ className }: { className?: string }) {
  const [active, setActive] = useState<string | null>(null);
  return (
    <div
      className={cn("fixed top-10 inset-x-0 max-w-2xl mx-auto z-50", className)}
    >
      <Menu setActive={setActive}>
        <MenuItem setActive={setActive} active={active} item="Solutions">
          <div className="flex flex-col space-y-4 text-sm">
            <HoveredLink href="#sme-analytics">SME Analytics</HoveredLink>
            <HoveredLink href="#restaurant-system">Restaurant System</HoveredLink>
            <HoveredLink href="#connecto-voice">Connecto Voice</HoveredLink>
            <HoveredLink href="#ai-insights">AI Insights</HoveredLink>
          </div>
        </MenuItem>
        <MenuItem setActive={setActive} active={active} item="Products">
          <div className="text-sm grid grid-cols-2 gap-10 p-4">
            <ProductItem
              title="SME Analytics"
              href="#sme-analytics"
              src="/images/sme-logo.png"
              description="AI-powered business analytics for small and medium enterprises."
            />
            <ProductItem
              title="ROS Restaurant"
              href="#restaurant-system"
              src="/images/sme-logo.png"
              description="Complete restaurant operations system with smart menu management."
            />
            <ProductItem
              title="Connecto Voice"
              href="#connecto-voice"
              src="/images/sme-logo.png"
              description="AI voice reception system for seamless customer interactions."
            />
            <ProductItem
              title="Market Intelligence"
              href="#market-intelligence"
              src="/images/sme-logo.png"
              description="Advanced market analysis and competitive intelligence platform."
            />
          </div>
        </MenuItem>
        <MenuItem setActive={setActive} active={active} item="Features">
          <div className="flex flex-col space-y-4 text-sm">
            <HoveredLink href="#ai-recommendations">AI Recommendations</HoveredLink>
            <HoveredLink href="#market-analysis">Market Analysis</HoveredLink>
            <HoveredLink href="#dynamic-pricing">Dynamic Pricing</HoveredLink>
            <HoveredLink href="#sentiment-analysis">Sentiment Analysis</HoveredLink>
          </div>
        </MenuItem>
        <MenuItem setActive={setActive} active={active} item="About">
          <div className="flex flex-col space-y-4 text-sm">
            <HoveredLink href="#about">About Us</HoveredLink>
            <HoveredLink href="#contact">Contact</HoveredLink>
            <HoveredLink href="#team">Our Team</HoveredLink>
            <HoveredLink href="#careers">Careers</HoveredLink>
          </div>
        </MenuItem>
      </Menu>
    </div>
  );
}
