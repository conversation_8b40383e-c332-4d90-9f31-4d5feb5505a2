'use client';

import React from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import { languages, Language } from '../lib/i18n';

export default function LanguageSwitcher() {
  const { language, setLanguage } = useLanguage();

  return (
    <div className="flex items-center space-x-2">
      {Object.entries(languages).map(([code, name]) => (
        <button
          key={code}
          onClick={() => setLanguage(code as Language)}
          className={`px-3 py-1 rounded-md text-sm font-medium transition-all duration-200 ${
            language === code
              ? 'bg-blue-600 text-white shadow-md'
              : 'text-gray-300 hover:text-white hover:bg-gray-700'
          }`}
          aria-label={`Switch to ${name}`}
        >
          {code.toUpperCase()}
        </button>
      ))}
    </div>
  );
}
