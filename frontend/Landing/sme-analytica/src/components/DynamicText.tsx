"use client";

/**
 * @author: @dorian_baffier
 * @description: Dynamic Text
 * @version: 1.0.0
 * @date: 2025-06-26
 * @license: MIT
 * @website: https://kokonutui.com
 * @github: https://github.com/kokonut-labs/kokonutui
 */

import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "motion/react";
import { useLanguage } from "@/contexts/LanguageContext";

interface Greeting {
    text: string;
    language: string;
}

const greetings: Greeting[] = [
    { text: "Hello", language: "English" },
    { text: "こんにちは", language: "Japanese" },
    { text: "Bonjour", language: "French" },
    { text: "Hola", language: "Spanish" },
    { text: "안녕하세요", language: "Korean" },
    { text: "Ciao", language: "Italian" },
    { text: "Hallo", language: "German" },
    { text: "Привет", language: "Russian" },
];

const DynamicText = () => {
    const [currentIndex, setCurrentIndex] = useState(0);
    const [isAnimating, setIsAnimating] = useState(true);
    const [hasFinishedCycle, setHasFinishedCycle] = useState(false);
    const { language, t } = useLanguage();

    // Get the user's language greeting first
    const getUserLanguageGreeting = () => {
        if (language === 'es') {
            return greetings.find(g => g.text === 'Hola') || greetings[0];
        }
        return greetings.find(g => g.text === 'Hello') || greetings[0];
    };

    // Create ordered greetings starting with user's language
    const getOrderedGreetings = () => {
        const userGreeting = getUserLanguageGreeting();
        const otherGreetings = greetings.filter(g => g.text !== userGreeting.text);
        return [userGreeting, ...otherGreetings];
    };

    const orderedGreetings = getOrderedGreetings();

    useEffect(() => {
        // Reset animation when language changes
        setCurrentIndex(0);
        setIsAnimating(true);
        setHasFinishedCycle(false);
    }, [language]);

    useEffect(() => {
        if (!isAnimating || hasFinishedCycle) return;

        const interval = setInterval(() => {
            setCurrentIndex((prevIndex) => {
                const nextIndex = prevIndex + 1;

                if (nextIndex >= orderedGreetings.length) {
                    clearInterval(interval);
                    setIsAnimating(false);
                    setHasFinishedCycle(true);
                    // Stay on the user's language greeting
                    return 0;
                }

                return nextIndex;
            });
        }, 300);

        return () => clearInterval(interval);
    }, [isAnimating, hasFinishedCycle, orderedGreetings.length]);

    // Animation variants for the text
    const textVariants = {
        hidden: { y: 20, opacity: 0 },
        visible: { y: 0, opacity: 1 },
        exit: { y: -100, opacity: 0 },
    };

    return (
        <div
            className="flex min-h-[80px] items-center justify-center gap-1 p-4"
            aria-label="Rapid greetings in different languages"
        >
            <div className="relative h-16 w-60 flex items-center justify-center overflow-visible">
                {isAnimating ? (
                    <AnimatePresence mode="popLayout">
                        <motion.div
                            key={currentIndex}
                            className="absolute flex items-center gap-2 text-2xl font-medium text-blue-200"
                            aria-live="off"
                            initial={textVariants.hidden}
                            animate={textVariants.visible}
                            exit={textVariants.exit}
                            transition={{ duration: 0.2, ease: "easeOut" }}
                        >
                            <div
                                className="h-2 w-2 rounded-full bg-blue-400"
                                aria-hidden="true"
                            />
                            {orderedGreetings[currentIndex].text}
                        </motion.div>
                    </AnimatePresence>
                ) : (
                    <div className="flex items-center gap-2 text-2xl font-medium text-blue-200">
                        <div
                            className="h-2 w-2 rounded-full bg-blue-400"
                            aria-hidden="true"
                        />
                        {getUserLanguageGreeting().text}
                    </div>
                )}
            </div>
        </div>
    );
};

export default DynamicText;
