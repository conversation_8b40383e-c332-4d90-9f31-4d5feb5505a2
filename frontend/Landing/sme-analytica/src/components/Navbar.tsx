"use client";
import React, { useState } from "react";
import Image from 'next/image'
import { HoveredLink, Menu, MenuItem, ProductItem } from "@/components/ui/navbar-menu";
import { cn } from "@/lib/utils";
import { Menu as MenuIcon, X } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";
import LanguageSwitcher from "@/components/LanguageSwitcher";

export default function Navbar() {
  return (
    <div className="relative w-full flex items-center justify-center">
      <NavbarContent className="top-2" />
    </div>
  );
}

function NavbarContent({ className }: { className?: string }) {
  const [active, setActive] = useState<string | null>(null);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { t } = useLanguage();

  return (
    <div
      className={cn("fixed top-2 inset-x-0 max-w-6xl mx-auto z-50 px-4", className)}
    >
      <div className="flex items-center justify-between px-3 py-1.5 bg-black/20 backdrop-blur-md rounded-full border border-white/10">
        {/* Logo */}
        <a href="#Home" className="flex items-center">
          <Image
            src="/images/sme-logo.png"
            alt="SME Analytica Logo"
            width={28}
            height={28}
            className="mr-2 brightness-110 contrast-110"
          />
          <span className="text-sm font-semibold text-white drop-shadow-sm">
            SME Analytica
          </span>
        </a>

        {/* Desktop Navigation Menu */}
        <div className="hidden md:flex items-center space-x-4">
          <Menu setActive={setActive}>
            <MenuItem setActive={setActive} active={active} item={t('navbar', 'solutions')}>
              <div className="flex flex-col space-y-2 text-xs p-2">
                <HoveredLink href="#sme-analytics">{t('solutions', 'smeAnalytics')}</HoveredLink>
                <HoveredLink href="#restaurant-system">{t('solutions', 'restaurantSystem')}</HoveredLink>
                <HoveredLink href="#connecto-voice">{t('solutions', 'connectoVoice')}</HoveredLink>
                <HoveredLink href="#ai-insights">{t('solutions', 'aiInsights')}</HoveredLink>
              </div>
            </MenuItem>

            <MenuItem setActive={setActive} active={active} item={t('navbar', 'products')}>
              <div className="text-xs grid grid-cols-2 gap-3 p-2 max-w-md">
                <ProductItem
                  title={t('products', 'smeAnalytics.title')}
                  href="#sme-analytics"
                  src="/images/sme-logo.png"
                  description={t('products', 'smeAnalytics.description')}
                />
                <ProductItem
                  title={t('products', 'rosRestaurant.title')}
                  href="#restaurant-system"
                  src="/images/sme-logo.png"
                  description={t('products', 'rosRestaurant.description')}
                />
                <ProductItem
                  title={t('products', 'connectoVoice.title')}
                  href="#connecto-voice"
                  src="/images/sme-logo.png"
                  description={t('products', 'connectoVoice.description')}
                />
                <ProductItem
                  title={t('products', 'marketIntelligence.title')}
                  href="#market-intelligence"
                  src="/images/sme-logo.png"
                  description={t('products', 'marketIntelligence.description')}
                />
              </div>
            </MenuItem>

            <MenuItem setActive={setActive} active={active} item={t('navbar', 'features')}>
              <div className="flex flex-col space-y-2 text-xs p-2">
                <HoveredLink href="#ai-recommendations">{t('featuresDropdown', 'aiRecommendations')}</HoveredLink>
                <HoveredLink href="#market-analysis">{t('featuresDropdown', 'marketAnalysis')}</HoveredLink>
                <HoveredLink href="#dynamic-pricing">{t('featuresDropdown', 'dynamicPricing')}</HoveredLink>
                <HoveredLink href="#sentiment-analysis">{t('featuresDropdown', 'sentimentAnalysis')}</HoveredLink>
              </div>
            </MenuItem>

            <MenuItem setActive={setActive} active={active} item={t('navbar', 'about')}>
              <div className="flex flex-col space-y-2 text-xs p-2">
                <HoveredLink href="#about">{t('about', 'aboutUs')}</HoveredLink>
                <HoveredLink href="#contact">{t('about', 'contact')}</HoveredLink>
                <HoveredLink href="#team">{t('about', 'team')}</HoveredLink>
                <HoveredLink href="#careers">{t('about', 'careers')}</HoveredLink>
              </div>
            </MenuItem>
          </Menu>

          {/* Language Switcher */}
          <LanguageSwitcher />
        </div>

        {/* Mobile Menu Button */}
        <button
          className="md:hidden text-white p-2"
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        >
          {mobileMenuOpen ? <X size={20} /> : <MenuIcon size={20} />}
        </button>
      </div>

      {/* Mobile Menu */}
      {mobileMenuOpen && (
        <div className="md:hidden mt-2 mx-auto max-w-sm bg-black/90 backdrop-blur-md rounded-xl border border-white/10 p-3 shadow-xl">
          <div className="grid grid-cols-2 gap-3 text-xs text-center">
            {/* Solutions */}
            <div className="space-y-1">
              <h4 className="text-white/70 font-medium text-xs mb-1">{t('navbar', 'solutions')}</h4>
              <a href="#sme-analytics" className="block text-white/90 hover:text-white transition-colors">📊 {t('solutions', 'smeAnalytics')}</a>
              <a href="#restaurant-system" className="block text-white/90 hover:text-white transition-colors">🍽️ {t('solutions', 'restaurantSystem')}</a>
              <a href="#connecto-voice" className="block text-white/90 hover:text-white transition-colors">🎤 {t('solutions', 'connectoVoice')}</a>
            </div>

            {/* Products */}
            <div className="space-y-1">
              <h4 className="text-white/70 font-medium text-xs mb-1">{t('navbar', 'products')}</h4>
              <a href="#sme-analytics" className="block text-white/90 hover:text-white transition-colors">💼 {t('products', 'smeAnalytics.title')}</a>
              <a href="#restaurant-system" className="block text-white/90 hover:text-white transition-colors">🏪 {t('products', 'rosRestaurant.title')}</a>
              <a href="#connecto-voice" className="block text-white/90 hover:text-white transition-colors">🤖 {t('products', 'connectoVoice.title')}</a>
            </div>

            {/* Features */}
            <div className="space-y-1">
              <h4 className="text-white/70 font-medium text-xs mb-1">{t('navbar', 'features')}</h4>
              <a href="#ai-recommendations" className="block text-white/90 hover:text-white transition-colors">🎯 {t('features', 'aiRecommendations')}</a>
              <a href="#market-analysis" className="block text-white/90 hover:text-white transition-colors">📈 {t('features', 'marketAnalysis')}</a>
              <a href="#dynamic-pricing" className="block text-white/90 hover:text-white transition-colors">💰 {t('features', 'dynamicPricing')}</a>
            </div>

            {/* About */}
            <div className="space-y-1">
              <h4 className="text-white/70 font-medium text-xs mb-1">{t('navbar', 'about')}</h4>
              <a href="#about" className="block text-white/90 hover:text-white transition-colors">ℹ️ {t('about', 'aboutUs')}</a>
              <a href="#contact" className="block text-white/90 hover:text-white transition-colors">📞 {t('about', 'contact')}</a>
              <a href="#team" className="block text-white/90 hover:text-white transition-colors">👥 {t('about', 'team')}</a>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}