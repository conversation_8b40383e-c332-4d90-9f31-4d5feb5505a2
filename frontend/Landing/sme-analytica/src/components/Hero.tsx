'use client';

import React from 'react'
import { gsap } from 'gsap'
import { SplitText, ScrollTrigger } from 'gsap/all'
import { useGSAP } from '@gsap/react'

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger, SplitText)
import Lottie from 'lottie-react'
import ChartsAnimation from '../../public/lottie/Charts.json'
import DataAnimation from '../../public/lottie/Data.json'
import DynamicText from './DynamicText'
import { useLanguage } from '@/contexts/LanguageContext'

function Hero() {
  const { t } = useLanguage();

  useGSAP(() => {
    // Simple fade in animation
    gsap.fromTo('.hero-title',
      {
        opacity: 0,
        y: 50,
      },
      {
        duration: 1.5,
        opacity: 1,
        y: 0,
        ease: "power2.out",
        delay: 0.5
      }
    )

    // Lottie scroll animations
    // Top-left Lottie - moves down and slightly right on scroll
    gsap.to('.lottie-top-left', {
      y: 100,
      x: 50,
      rotation: 5,
      scale: 0.9,
      scrollTrigger: {
        trigger: '.hero-section',
        start: 'top top',
        end: 'bottom top',
        scrub: 1 // Smooth animation tied to scroll
      }
    });

    // Bottom-right Lottie - moves up and slightly left on scroll
    gsap.to('.lottie-bottom-right', {
      y: -100,
      x: -50,
      rotation: -5,
      scale: 0.9,
      scrollTrigger: {
        trigger: '.hero-section',
        start: 'top top',
        end: 'bottom top',
        scrub: 1 // Smooth animation tied to scroll
      }
    });
  }, [])


  return (
    <>
    <section id="hero" className='hero-section h-screen w-full relative flex flex-col items-center justify-center bg-gradient-to-br from-[#171f31] to-[#202940]'>
        {/* Top-Left Lottie Animation - Extreme Edge */}
        <Lottie
          animationData={ChartsAnimation}
          className="lottie-top-left absolute -left-20 top-15 w-80 h-80 z-10"
          loop={true}
          autoplay={true}
        />

        {/* Bottom-Right Lottie Animation - Extreme Edge */}
        <Lottie
          animationData={DataAnimation}
          className="lottie-bottom-right absolute -right-10 -bottom-15 w-80 h-80 z-10"
          loop={true}
          autoplay={true}
        />

        <div className="text-center space-y-8 max-w-4xl mx-auto px-4 z-20 relative">
          <div className="mb-8">
            <DynamicText />
          </div>

          <h1 className='text-5xl md:text-7xl font-bold text-gradient-primary mb-6 hero-title' style={{ opacity: 1 }}>
            {t('hero', 'title')}
          </h1>

          <div className="body">
            <div className="content">
              <div className="space-y-6 hidden md:block">
                <p className="text-xl md:text-2xl font-semibold text-white/90">
                  {t('hero', 'tagline')}
                </p>
                <p className="subtitle text-lg text-white/80 leading-relaxed max-w-3xl mx-auto">
                  {t('hero', 'subtitle')}
                </p>
              </div>
            </div>
          </div>

          <div className="view-sme space-y-6">
            <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
              <p className="subtitle text-white/90 leading-relaxed">
                <span className="font-bold" style={{ color: 'var(--accent-blue)' }}>SME Analytica</span>: {t('hero', 'description')}
                <span className="font-semibold" style={{ color: 'var(--accent-blue)' }}> {t('hero', 'taglineEnd')}</span>
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <a
                href="#contact"
                className="btn btn-primary btn-large transition-all duration-300 transform hover:scale-105"
                style={{
                  background: 'var(--accent-blue)',
                  color: 'white',
                  padding: '1rem 2rem',
                  borderRadius: '0.75rem',
                  fontWeight: '600',
                  textDecoration: 'none',
                  display: 'inline-block',
                  boxShadow: '0 10px 25px rgba(17, 165, 232, 0.3)'
                }}
              >
                {t('hero', 'cta.primary')}
              </a>
              <a
                href="#features"
                className="btn btn-secondary btn-large transition-all duration-300"
                style={{
                  border: '2px solid var(--accent-blue)',
                  color: 'var(--accent-blue)',
                  padding: '1rem 2rem',
                  borderRadius: '0.75rem',
                  fontWeight: '600',
                  textDecoration: 'none',
                  display: 'inline-block',
                  background: 'transparent'
                }}
              >
                {t('hero', 'cta.secondary')}
              </a>
            </div>
          </div>
        </div>
    </section>

    <div className="video absolute inset-0"></div>
    </>
  )
}

export default Hero