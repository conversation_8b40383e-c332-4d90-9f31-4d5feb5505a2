@import url('https://fonts.googleapis.com/css2?family=Figtree:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600&display=swap');
@import "tailwindcss";

/* ===== SME ANALYTICA DESIGN SYSTEM ===== */
/* The Intuitive Navigator - Unified Blueprint */

:root {
  /* Brand Color Palette */
  --primary-blue: #5f7790;           /* HSL(215, 35%, 31%) - Stability & Trust */
  --accent-blue: #11a5e8;            /* HSL(199, 100%, 49.8%) - Energy & Innovation */
  --dark-background: #171f31;        /* HSL(215, 35%, 15%) - Sophisticated Canvas */
  --light-text: #d5dce2;             /* HSL(210, 20%, 85%) - High Readability */

  /* Visualization Colors - The AI Intelligence Spectrum */
  --core-ai: hsl(215, 35%, 45%);     /* Core AI Intelligence - Luminous Brain */
  --data-streams: hsl(199, 90%, 65%); /* Incoming Data Flow */
  --insights: hsl(199, 100%, 75%);   /* Valuable Insights - Glowing Discovery */
  --growth-paths: hsl(199, 80%, 40%); /* Expansion & Progress */

  /* Semantic Colors */
  --success: hsl(142, 76%, 36%);
  --warning: hsl(38, 92%, 50%);
  --error: hsl(0, 84%, 60%);

  /* Surface Colors */
  --surface-elevated: hsl(215, 30%, 20%);
  --surface-subtle: hsl(215, 25%, 10%);
  --border-subtle: hsl(215, 20%, 25%);
  --border-focus: var(--accent-blue);

  /* Typography Scale */
  --font-heading: 'Figtree', system-ui, sans-serif;
  --font-body: 'Inter', system-ui, sans-serif;

  /* Spacing Scale */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Shadows & Glows */
  --shadow-subtle: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-large: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* AI Glows */
  --glow-core-ai: 0 0 20px hsla(215, 35%, 45%, 0.3);
  --glow-insights: 0 0 25px hsla(199, 100%, 75%, 0.4);
  --glow-accent: 0 0 15px hsla(199, 100%, 49.8%, 0.3);

  /* Animation Durations */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
  --duration-slower: 800ms;

  /* Animation Easings */
  --ease-out: cubic-bezier(0.16, 1, 0.3, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-spring: cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* ===== BASE STYLES ===== */

* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-body);
  background-color: var(--dark-background);
  color: var(--light-text);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* ===== TYPOGRAPHY SYSTEM ===== */

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: 600;
  line-height: 1.2;
  margin: 0;
  color: var(--light-text);
}

h1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  letter-spacing: -0.02em;
}

h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 600;
  letter-spacing: -0.01em;
}

h3 {
  font-size: clamp(1.5rem, 3vw, 2.25rem);
  font-weight: 600;
}

h4 {
  font-size: clamp(1.25rem, 2.5vw, 1.75rem);
  font-weight: 500;
}

h5 {
  font-size: clamp(1.125rem, 2vw, 1.5rem);
  font-weight: 500;
}

h6 {
  font-size: clamp(1rem, 1.5vw, 1.25rem);
  font-weight: 500;
}

p {
  margin: 0 0 var(--space-md) 0;
  font-size: clamp(1rem, 1.5vw, 1.125rem);
  line-height: 1.7;
}

.text-large {
  font-size: clamp(1.125rem, 2vw, 1.25rem);
  line-height: 1.6;
}

.text-small {
  font-size: clamp(0.875rem, 1.25vw, 1rem);
  line-height: 1.5;
}

.text-xs {
  font-size: clamp(0.75rem, 1vw, 0.875rem);
  line-height: 1.4;
}

/* ===== UTILITY CLASSES ===== */

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-start {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.flex-end {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.text-gradient-primary {
  background: linear-gradient(135deg, var(--accent-blue), var(--insights));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-ai {
  background: linear-gradient(135deg, var(--core-ai), var(--data-streams));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glow-core-ai {
  box-shadow: var(--glow-core-ai);
}

.glow-insights {
  box-shadow: var(--glow-insights);
}

.glow-accent {
  box-shadow: var(--glow-accent);
}

.surface-elevated {
  background-color: var(--surface-elevated);
  border: 1px solid var(--border-subtle);
}

.surface-subtle {
  background-color: var(--surface-subtle);
}

.transition-smooth {
  transition: all var(--duration-normal) var(--ease-out);
}

.transition-spring {
  transition: all var(--duration-slow) var(--ease-spring);
}

/* ===== BUTTON SYSTEM ===== */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  padding: var(--space-md) var(--space-xl);
  border: none;
  border-radius: var(--radius-md);
  font-family: var(--font-heading);
  font-weight: 500;
  font-size: 1rem;
  line-height: 1;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out);
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--primary-blue);
  color: var(--light-text);
  box-shadow: var(--shadow-medium);
}

.btn-primary:hover:not(:disabled) {
  background-color: hsl(215, 35%, 35%);
  box-shadow: var(--shadow-large), var(--glow-accent);
  transform: translateY(-2px);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-secondary {
  background-color: transparent;
  color: var(--accent-blue);
  border: 2px solid var(--accent-blue);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--accent-blue);
  color: var(--dark-background);
  box-shadow: var(--glow-accent);
  transform: translateY(-2px);
}

.btn-ghost {
  background-color: transparent;
  color: var(--light-text);
  border: 1px solid var(--border-subtle);
}

.btn-ghost:hover:not(:disabled) {
  background-color: var(--surface-elevated);
  border-color: var(--accent-blue);
  color: var(--accent-blue);
}

.btn-large {
  padding: var(--space-lg) var(--space-2xl);
  font-size: 1.125rem;
}

.btn-small {
  padding: var(--space-sm) var(--space-md);
  font-size: 0.875rem;
}

/* ===== FORM ELEMENTS ===== */

.form-group {
  margin-bottom: var(--space-lg);
}

.form-label {
  display: block;
  margin-bottom: var(--space-sm);
  font-weight: 500;
  color: var(--light-text);
  font-size: 0.875rem;
}

.form-input {
  width: 100%;
  padding: var(--space-md);
  background-color: var(--surface-subtle);
  border: 1px solid var(--border-subtle);
  border-radius: var(--radius-md);
  color: var(--light-text);
  font-family: var(--font-body);
  font-size: 1rem;
  transition: all var(--duration-normal) var(--ease-out);
}

.form-input::placeholder {
  color: hsl(210, 20%, 60%);
}

.form-input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px hsla(199, 100%, 49.8%, 0.1);
  background-color: var(--surface-elevated);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

/* ===== CARD SYSTEM ===== */

.card {
  background-color: var(--surface-elevated);
  border: 1px solid var(--border-subtle);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  transition: all var(--duration-normal) var(--ease-out);
}

.card:hover {
  border-color: var(--accent-blue);
  box-shadow: var(--shadow-large);
  transform: translateY(-4px);
}

.card-feature {
  text-align: center;
  padding: var(--space-2xl);
}

.card-feature .icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--space-lg);
  color: var(--accent-blue);
}

.card-testimonial {
  position: relative;
  padding: var(--space-2xl);
}

.card-testimonial::before {
  content: '"';
  position: absolute;
  top: var(--space-md);
  left: var(--space-md);
  font-size: 4rem;
  color: var(--accent-blue);
  opacity: 0.3;
  font-family: serif;
}

.card-pricing {
  position: relative;
  text-align: center;
  padding: var(--space-2xl);
}

.card-pricing.featured {
  border-color: var(--accent-blue);
  box-shadow: var(--glow-accent);
}

.card-pricing.featured::before {
  content: 'Most Popular';
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--accent-blue);
  color: var(--dark-background);
  padding: var(--space-xs) var(--space-md);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 600;
}

/* ===== NAVIGATION SYSTEM ===== */

.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: hsla(215, 35%, 15%, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-subtle);
  transition: all var(--duration-normal) var(--ease-out);
}

.navbar.scrolled {
  background-color: hsla(215, 35%, 15%, 0.98);
  box-shadow: var(--shadow-large);
}

.navbar-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
}

.navbar-logo {
  font-family: var(--font-heading);
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--light-text);
  text-decoration: none;
  transition: all var(--duration-normal) var(--ease-out);
}

.navbar-logo:hover {
  color: var(--accent-blue);
  text-shadow: var(--glow-accent);
}

.navbar-nav {
  display: flex;
  align-items: center;
  gap: var(--space-xl);
  list-style: none;
  margin: 0;
  padding: 0;
}

.navbar-link {
  color: var(--light-text);
  text-decoration: none;
  font-weight: 500;
  transition: all var(--duration-normal) var(--ease-out);
  position: relative;
}

.navbar-link:hover {
  color: var(--accent-blue);
}

.navbar-link::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--accent-blue);
  transition: width var(--duration-normal) var(--ease-out);
}

.navbar-link:hover::after {
  width: 100%;
}

.navbar-cta {
  margin-left: var(--space-lg);
}

/* Mobile Navigation */
.navbar-toggle {
  display: none;
  flex-direction: column;
  gap: 4px;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-sm);
}

.navbar-toggle span {
  width: 24px;
  height: 2px;
  background-color: var(--light-text);
  transition: all var(--duration-normal) var(--ease-out);
}

.navbar-toggle.active span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.navbar-toggle.active span:nth-child(2) {
  opacity: 0;
}

.navbar-toggle.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* ===== AI VISUALIZATION ANIMATIONS ===== */

@keyframes dataFlow {
  0% {
    transform: translateX(-100%) scale(0.8);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%) scale(1.2);
    opacity: 0;
  }
}

@keyframes coreAIPulse {
  0%, 100% {
    box-shadow: var(--glow-core-ai);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 40px hsla(215, 35%, 45%, 0.6);
    transform: scale(1.05);
  }
}

@keyframes insightsGlow {
  0%, 100% {
    box-shadow: var(--glow-insights);
    opacity: 0.8;
  }
  50% {
    box-shadow: 0 0 50px hsla(199, 100%, 75%, 0.8);
    opacity: 1;
  }
}

@keyframes growthPath {
  0% {
    stroke-dashoffset: 1000;
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    stroke-dashoffset: 0;
    opacity: 0.8;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Animation Classes */
.animate-data-flow {
  animation: dataFlow 3s ease-in-out infinite;
}

.animate-core-ai-pulse {
  animation: coreAIPulse 4s ease-in-out infinite;
}

.animate-insights-glow {
  animation: insightsGlow 2s ease-in-out infinite;
}

.animate-growth-path {
  animation: growthPath 5s ease-in-out infinite;
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s var(--ease-out) forwards;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.8s var(--ease-out) forwards;
}

.animate-fade-in-right {
  animation: fadeInRight 0.8s var(--ease-out) forwards;
}

/* ===== LAYOUT UTILITIES ===== */

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
}

.container-wide {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
}

.container-narrow {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
}

.section {
  padding: var(--space-3xl) 0;
}

.section-large {
  padding: calc(var(--space-3xl) * 1.5) 0;
}

.section-small {
  padding: var(--space-2xl) 0;
}

.grid-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-xl);
}

.grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-xl);
}

.grid-4 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--space-xl);
}

/* ===== HERO SECTION STYLES ===== */

.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  background: radial-gradient(ellipse at center, hsla(215, 35%, 20%, 0.3) 0%, var(--dark-background) 70%);
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, hsla(199, 100%, 75%, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, hsla(215, 35%, 45%, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  margin-bottom: var(--space-lg);
  opacity: 0;
  animation: fadeInUp 1s var(--ease-out) 0.2s forwards;
}

.hero-subtitle {
  font-size: clamp(1.125rem, 2vw, 1.5rem);
  margin-bottom: var(--space-2xl);
  opacity: 0.9;
  opacity: 0;
  animation: fadeInUp 1s var(--ease-out) 0.4s forwards;
}

.hero-cta {
  opacity: 0;
  animation: fadeInUp 1s var(--ease-out) 0.6s forwards;
}

/* ===== SPECIAL EFFECTS ===== */

.ai-orb {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: radial-gradient(circle, var(--core-ai) 0%, transparent 70%);
  position: relative;
  animation: coreAIPulse 4s ease-in-out infinite;
}

.ai-orb::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: var(--insights);
  opacity: 0.6;
  animation: insightsGlow 2s ease-in-out infinite;
}

.data-stream {
  position: absolute;
  width: 2px;
  height: 100px;
  background: linear-gradient(to bottom, transparent, var(--data-streams), transparent);
  animation: dataFlow 3s ease-in-out infinite;
}

.floating-element {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 1024px) {
  .container {
    padding: 0 var(--space-md);
  }

  .grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }

  .grid-3 {
    grid-template-columns: repeat(2, 1fr);
  }

  .navbar-nav {
    display: none;
  }

  .navbar-toggle {
    display: flex;
  }
}

@media (max-width: 768px) {
  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
  }

  .section {
    padding: var(--space-2xl) 0;
  }

  .section-large {
    padding: var(--space-3xl) 0;
  }

  .card {
    padding: var(--space-lg);
  }

  .btn {
    width: 100%;
    justify-content: center;
  }

  .navbar-container {
    padding: 0 var(--space-md);
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--space-sm);
  }

  .section {
    padding: var(--space-xl) 0;
  }

  .card {
    padding: var(--space-md);
  }

  .hero {
    min-height: 80vh;
  }
}

/* ===== ACCESSIBILITY ===== */

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles for accessibility */
.btn:focus-visible,
.form-input:focus-visible,
.navbar-link:focus-visible {
  outline: 2px solid var(--accent-blue);
  outline-offset: 2px;
}

/* ===== PRINT STYLES ===== */

@media print {
  .navbar,
  .btn,
  .animate-data-flow,
  .animate-core-ai-pulse,
  .animate-insights-glow,
  .animate-growth-path {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }

  .card {
    border: 1px solid #ccc !important;
    box-shadow: none !important;
  }
}