'use client';

import React, { useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger, SplitText } from 'gsap/all';
import Navbar from '@/components/Navbar';
import Hero from '@/components/Hero';

export default function Home() {
  useEffect(() => {
    gsap.registerPlugin(ScrollTrigger, SplitText);


    console.log('GSAP plugins registered successfully');

    // Cleanup function
    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  return (
    <main>
      <Navbar />
      <Hero />
    </main>  
  );
}