/**
 * Environment Configuration
 * 
 * Centralizes environment variable access and provides validation
 * to prevent runtime errors due to missing configuration.
 */

export interface EnvironmentConfig {
  supabase: {
    url: string;
    anonKey: string;
  };
  stripe: {
    publishableKey: string;
  };
  api: {
    baseUrl: string;
  };
  features: {
    enableMockData: boolean;
  };
}

/**
 * Validates that required environment variables are present
 */
function validateEnvironment(): EnvironmentConfig {
  const requiredVars = {
    VITE_SUPABASE_URL: import.meta.env.VITE_SUPABASE_URL,
    VITE_SUPABASE_ANON_KEY: import.meta.env.VITE_SUPABASE_ANON_KEY,
    VITE_STRIPE_PUBLISHABLE_KEY: import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY,
  };

  const missingVars = Object.entries(requiredVars)
    .filter(([, value]) => !value)
    .map(([key]) => key);

  if (missingVars.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missingVars.join(', ')}. ` +
      'Please check your .env.local file and ensure all required variables are set.'
    );
  }

  return {
    supabase: {
      url: requiredVars.VITE_SUPABASE_URL!,
      anonKey: requiredVars.VITE_SUPABASE_ANON_KEY!,
    },
    stripe: {
      publishableKey: requiredVars.VITE_STRIPE_PUBLISHABLE_KEY!,
    },
    api: {
      baseUrl: import.meta.env.VITE_API_URL || import.meta.env.VITE_API_BASE_URL || 'https://api.smeanalytica.dev',
    },
    features: {
      enableMockData: import.meta.env.VITE_ENABLE_MOCK_DATA === 'true',
    },
  };
}

/**
 * Environment configuration instance
 * Will throw an error during module load if required variables are missing
 */
export const env = validateEnvironment();

/**
 * Helper function to check if we're in development mode
 */
export const isDevelopment = import.meta.env.DEV;

/**
 * Helper function to check if we're in production mode
 */
export const isProduction = import.meta.env.PROD;