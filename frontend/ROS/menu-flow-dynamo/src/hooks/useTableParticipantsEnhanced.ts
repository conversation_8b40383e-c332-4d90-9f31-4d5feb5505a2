/**
 * Enhanced Table Participants Hook
 *
 * Manages real-time table participant state and activities
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { collaborativeCartService, TableParticipant } from '@/services/collaborativeCartService';

export interface UseTableParticipantsOptions {
  tableId: string;
  customerSessionId: string;
  enabled?: boolean;
}

export interface UseTableParticipantsReturn {
  // Participant data
  participants: TableParticipant[];
  participantCount: number;
  activeParticipants: TableParticipant[];
  myParticipant: TableParticipant | null;

  // Loading states
  isLoading: boolean;
  error: string | null;

  // Actions
  refreshParticipants: () => Promise<void>;
  updateActivity: () => Promise<void>;

  // Utilities
  isParticipantActive: (participant: TableParticipant) => boolean;
  getParticipantDisplayName: (participant: TableParticipant) => string;
  isMe: (participant: TableParticipant) => boolean;
}

export function useTableParticipantsEnhanced({
  tableId,
  customerSessionId,
  enabled = true
}: UseTableParticipantsOptions): UseTableParticipantsReturn {
  const [participants, setParticipants] = useState<TableParticipant[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const mountedRef = useRef(true);
  const unsubscribeRef = useRef<(() => void) | null>(null);

  // Load participants
  const loadParticipants = useCallback(async () => {
    if (!enabled || !tableId) return;

    try {
      setIsLoading(true);
      setError(null);

      const participantsData = await collaborativeCartService.getTableParticipants(tableId);

      if (mountedRef.current) {
        setParticipants(participantsData);

      }
    } catch (err) {
      console.error('Error loading participants:', err);
      if (mountedRef.current) {
        setError(err instanceof Error ? err.message : 'Failed to load participants');
      }
    } finally {
      if (mountedRef.current) {
        setIsLoading(false);
      }
    }
  }, [enabled, tableId]);

  // Refresh participants (public method)
  const refreshParticipants = useCallback(async () => {
    await loadParticipants();
  }, [loadParticipants]);

  // Update participant activity
  const updateActivity = useCallback(async () => {
    if (!enabled || !tableId || !customerSessionId) return;

    try {
      await collaborativeCartService.updateParticipantActivity(tableId, customerSessionId);
    } catch (err) {
      console.error('Error updating participant activity:', err);
    }
  }, [enabled, tableId, customerSessionId]);

  // Setup real-time subscription
  useEffect(() => {
    if (!enabled || !tableId) return;

    const unsubscribe = collaborativeCartService.subscribeToCartChanges(
      tableId,
      (event) => {
        if (event.type === 'participants_updated') {

          loadParticipants();
        }
      }
    );

    unsubscribeRef.current = unsubscribe;

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    };
  }, [enabled, tableId, loadParticipants]);

  // Initial load
  useEffect(() => {
    loadParticipants();
  }, [loadParticipants]);

  // Update activity periodically
  useEffect(() => {
    if (!enabled || !tableId || !customerSessionId) return;

    // Update activity immediately
    updateActivity();

    // Set up periodic activity updates (every 30 seconds)
    const interval = setInterval(updateActivity, 30000);

    return () => clearInterval(interval);
  }, [enabled, tableId, customerSessionId, updateActivity]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
    };
  }, []);

  // Computed values
  const activeParticipants = participants.filter(p => p.is_active);
  const participantCount = activeParticipants.length;
  const myParticipant = participants.find(p => p.customer_session_id === customerSessionId) || null;

  // Utility functions
  const isParticipantActive = useCallback((participant: TableParticipant): boolean => {
    if (!participant.is_active) return false;

    // Check if last activity was within reasonable time (2 hours)
    const lastActivity = new Date(participant.last_activity_at);
    const twoHoursAgo = new Date(Date.now() - 2 * 60 * 60 * 1000);

    return lastActivity > twoHoursAgo;
  }, []);

  const getParticipantDisplayName = useCallback((participant: TableParticipant): string => {
    return participant.customer_name || 'Anonymous Customer';
  }, []);

  const isMe = useCallback((participant: TableParticipant): boolean => {
    return participant.customer_session_id === customerSessionId;
  }, [customerSessionId]);

  return {
    // Participant data
    participants,
    participantCount,
    activeParticipants,
    myParticipant,

    // Loading states
    isLoading,
    error,

    // Actions
    refreshParticipants,
    updateActivity,

    // Utilities
    isParticipantActive,
    getParticipantDisplayName,
    isMe
  };
}