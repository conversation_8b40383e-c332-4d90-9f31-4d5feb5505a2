// Utility functions for restaurant context
// Moved from RestaurantContext.tsx to fix fast refresh issues

import { RestaurantInfo } from './RestaurantContext';

// Default restaurant info (used while loading)
export const defaultRestaurantInfo: RestaurantInfo = {
  id: '',
  name: 'Restaurant',
  logo_url: '',
  address: '',
  contact_email: '',
  contact_phone: '',
  description: '',
  dynamic_pricing_enabled: false,
  pricing_rules: {
    highTraffic: { threshold: 80, percentage: 10 },
    mediumTraffic: { threshold: 50, percentage: 5 },
    lowTraffic: { threshold: 20, percentage: 0 },
    applyToCategories: { food: true, drinks: false }
  }
};

// Format restaurant name for display
export const formatRestaurantName = (name: string): string => {
  return name || 'My Restaurant';
};

// Check if restaurant info is valid
export const isValidRestaurant = (info: RestaurantInfo | null): boolean => {
  return !!info && !!info.id && !!info.name;
};

// Format restaurant address for display
export const formatAddress = (address: string | undefined): string => {
  return address || 'No address provided';
};
