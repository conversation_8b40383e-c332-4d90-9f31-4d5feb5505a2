// Subscription-specific translations
export const subscriptionTranslations = {
  en: {
    // Subscription & Billing
    subscription: "Subscription",
    subscriptionBilling: "Subscription & Billing",
    currentSubscription: "Current Subscription",
    billingManagement: "Billing Management",
    manageBilling: "Manage Billing",
    upgradeToBasic: "Upgrade to Basic",
    upgradeToPremium: "Upgrade to Premium",
    getPremium: "Get Premium",
    freeTrialActive: "Free Trial Active",
    daysRemaining: "Days Remaining",
    trialEndsIn: "Trial ends in",
    days: "days",
    day: "day",
    subscribeNow: "Subscribe now",
    continueUsingFeatures: "to continue using all features after your trial ends.",
    nextBillingDate: "Next Billing Date",
    customerID: "Customer ID",
    planFeatures: "Plan Features",
    restaurant: "Restaurant",
    restaurants: "Restaurants",
    menuItem: "Menu Item",
    menuItems: "Menu Items",
    table: "Table",
    tables: "Tables",
    unlockAIPoweredFeatures: "Unlock AI-powered features and unlimited access",
    securelyManageSubscription: "Securely manage your subscription through Stripe",
    aiDynamicPricing: "AI Dynamic Pricing",
    advancedAnalytics: "Advanced Analytics",
    unlimitedEverything: "Unlimited Everything",
    posIntegration: "POS Integration",
    freeTrial: "Free Trial",
    basic: "Basic",
    premium: "Premium",
    currentPlan: "Current Plan",
    tryThisPremiumFeature: "You're experiencing all premium features! See how our AI pricing boosts revenue.",
    loading: "Loading"
  },
  es: {
    // Subscription & Billing
    subscription: "Suscripción",
    subscriptionBilling: "Suscripción y Facturación",
    currentSubscription: "Suscripción Actual",
    billingManagement: "Gestión de Facturación",
    manageBilling: "Gestionar Facturación",
    upgradeToBasic: "Actualizar a Básico",
    upgradeToPremium: "Actualizar a Premium",
    getPremium: "Obtener Premium",
    freeTrialActive: "Prueba Gratuita Activa",
    daysRemaining: "Días Restantes",
    trialEndsIn: "La prueba termina en",
    days: "días",
    day: "día",
    subscribeNow: "Suscríbete ahora",
    continueUsingFeatures: "para continuar usando todas las funciones después de que termine tu prueba.",
    nextBillingDate: "Próxima Fecha de Facturación",
    customerID: "ID de Cliente",
    planFeatures: "Características del Plan",
    restaurant: "Restaurante",
    restaurants: "Restaurantes",
    menuItem: "Elemento del Menú",
    menuItems: "Elementos del Menú",
    table: "Mesa",
    tables: "Mesas",
    unlockAIPoweredFeatures: "Desbloquea funciones impulsadas por IA y acceso ilimitado",
    securelyManageSubscription: "Gestiona de forma segura tu suscripción a través de Stripe",
    aiDynamicPricing: "Precios Dinámicos IA",
    advancedAnalytics: "Análisis Avanzados",
    unlimitedEverything: "Todo Ilimitado",
    posIntegration: "Integración POS",
    freeTrial: "Prueba Gratuita",
    basic: "Básico",
    premium: "Premium",
    currentPlan: "Plan Actual",
    tryThisPremiumFeature: "¡Prueba esta función premium durante tu prueba!",
    loading: "Cargando"
  }
};

// Helper function to get subscription translation
export const getSubscriptionTranslation = (key: string, language: 'en' | 'es' = 'es'): string => {
  return subscriptionTranslations[language][key] || subscriptionTranslations.en[key] || key;
}; 