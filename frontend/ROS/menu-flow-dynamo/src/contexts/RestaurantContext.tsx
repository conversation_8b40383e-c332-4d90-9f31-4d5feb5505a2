import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { getRestaurantId, getRestaurantById, getRestaurantByTableId, updateRestaurant } from '@/services/restaurantDbService.unified';
import { useAuth } from '@/contexts/AuthContext';
import type { PricingRules } from '@/services/dynamicPricingService';
import { useSearchParams } from 'react-router-dom';

// Define restaurant information type
export interface RestaurantInfo {
  id: string;
  restaurant_details_id?: string; // For theme service - restaurant_details.id
  name: string;
  logo_url?: string;
  address?: string;
  contact_email?: string;
  contact_phone?: string;
  description?: string;
  opening_hours?: unknown;
  dynamic_pricing_enabled?: boolean;
  pricing_rules?: PricingRules;
}

// Default restaurant info (used while loading)
const defaultRestaurantInfo: RestaurantInfo = {
  id: '',
  name: 'Restaurant',
  logo_url: '',
  address: '',
  contact_email: '',
  contact_phone: '',
  description: '',
  dynamic_pricing_enabled: false,
  pricing_rules: {
    highTraffic: { threshold: 80, percentage: 10 },
    mediumTraffic: { threshold: 50, percentage: 5 },
    lowTraffic: { threshold: 20, percentage: 0 },
    applyToCategories: { food: true, drinks: false }
  }
};

// Create restaurant context
interface RestaurantContextType {
  restaurantInfo: RestaurantInfo;
  selectedRestaurant: RestaurantInfo; // Alias for backward compatibility
  userRestaurants: RestaurantInfo[];
  setRestaurantInfo: (info: Partial<RestaurantInfo>) => void;
  updateRestaurantInDb: (info: Partial<RestaurantInfo>) => Promise<void>;
  selectRestaurant: (restaurantId: string) => void;
  isLoading: boolean;
}

const RestaurantContext = createContext<RestaurantContextType | undefined>(undefined);

// Create restaurant provider
export const RestaurantProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [restaurantInfo, setRestaurantInfoState] = useState<RestaurantInfo>(defaultRestaurantInfo);
  const [userRestaurants, setUserRestaurants] = useState<RestaurantInfo[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();
  const [searchParams] = useSearchParams();

  // Function to update restaurant info state
  const setRestaurantInfo = (info: Partial<RestaurantInfo>) => {
    setRestaurantInfoState(prev => {
      const updated = { ...prev, ...info };

      // Save to localStorage for persistence
      if (updated.id) {
        localStorage.setItem('selectedRestaurantId', updated.id);
      }

      return updated;
    });
  };

  // Function to update restaurant in database
  const updateRestaurantInDb = async (info: Partial<RestaurantInfo>) => {
    if (!restaurantInfo.id) {
      console.error('Cannot update restaurant: No restaurant ID');
      return;
    }

    try {
      await updateRestaurant(restaurantInfo.id, info);
      setRestaurantInfo(info);
    } catch (error) {
      console.error('Error updating restaurant:', error);
    }
  };

  // Function to select a restaurant from the dropdown
  const selectRestaurant = async (restaurantId: string) => {
    if (!restaurantId) {
      console.error('Cannot select restaurant: No restaurant ID provided');
      return;
    }

    try {
      setIsLoading(true);

      // Find the restaurant in the user's restaurants
      const selectedRestaurant = userRestaurants.find(r => r.id === restaurantId);

      if (selectedRestaurant) {
        // If we already have basic info, use it immediately
        setRestaurantInfoState(selectedRestaurant);

        // Save selection to localStorage
        localStorage.setItem('selectedRestaurantId', restaurantId);

        // Also fetch full details from the database
        const fullDetails = await getRestaurantById(restaurantId);

        if (fullDetails) {
          setRestaurantInfoState(prev => ({
            ...prev,
            ...fullDetails
          }));
        }
      } else {
        // If not found in user's restaurants, fetch from database
        const restaurant = await getRestaurantById(restaurantId);

        if (restaurant) {
          setRestaurantInfoState(restaurant);
          localStorage.setItem('selectedRestaurantId', restaurantId);
        } else {
          console.error(`Restaurant with ID ${restaurantId} not found`);
        }
      }
    } catch (error) {
      console.error('Error selecting restaurant:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Effect to fetch restaurant info when user or table ID changes
  useEffect(() => {
    // Fetch restaurant info from database
    const fetchRestaurantInfo = async () => {
      try {
        setIsLoading(true);

        // Check if there's a table ID in the URL
        const tableId = searchParams.get('table');

        // For customer views (with table ID), allow unauthenticated access
        // For admin views (without table ID), require authentication
        if (!user && !tableId) {
          // Only log once during initial load, not repeatedly
          if (isLoading) {

          }
          setIsLoading(false);
          return;
        }

        if (tableId) {
          try {

            // Use unified service to get restaurant by table ID
            const restaurant = await getRestaurantByTableId(tableId);


            if (restaurant) {


              const newRestaurantInfo = {
                id: restaurant.id, // business.id for general use
                restaurant_details_id: restaurant.restaurant_details_id, // restaurant_details.id for themes
                  name: restaurant.name,
                  logo_url: restaurant.logo_url,
                  address: restaurant.address,
                  contact_email: restaurant.contact_email,
                  contact_phone: restaurant.contact_phone,
                  description: restaurant.description,
                  opening_hours: restaurant.opening_hours,
                  dynamic_pricing_enabled: restaurant.dynamic_pricing_enabled,
                  pricing_rules: restaurant.pricing_rules
                };


              setRestaurantInfo(newRestaurantInfo);
                localStorage.setItem('selectedRestaurantId', restaurant.id);
                setIsLoading(false);
                return;
              }
          } catch (err) {
            console.error('RestaurantContext: Error processing table ID:', err);
          }
        }

        // If no user is authenticated and no table ID, we can't proceed
        if (!user) {
          console.warn('No authenticated user and no table ID provided');
          setIsLoading(false);
          return;
        }



        // Try to get restaurant ID from user context

        let restaurantId: string | null = null;

        // Check for previously selected restaurant ID in localStorage
        const savedRestaurantId = localStorage.getItem('selectedRestaurantId');
        const savedUserId = localStorage.getItem('lastUserId');

        // Clear saved restaurant if user changed
        if (savedUserId !== user.id) {

          localStorage.removeItem('selectedRestaurantId');
        }

        // Save current user ID
        localStorage.setItem('lastUserId', user.id);

        // If we have a saved restaurant ID and the user hasn't changed, try to use it
        if (savedRestaurantId && savedUserId === user.id) {

          restaurantId = savedRestaurantId;
        } else {
          // FIXED: Get businesses with ROS (Restaurant Operations) app_type
          console.log('🔍 Fetching ROS businesses for user:', user.id);

          const { data: rosBusinesses, error: businessError } = await supabase
            .from('businesses')
            .select('id, name, user_id, app_type')
            .eq('user_id', user.id)
            .in('app_type', ['ros', 'ros,sme']) // ROS or dual-app businesses
            .eq('is_active', true);

          if (businessError) {
            console.error('❌ Error fetching ROS businesses:', businessError);
            setIsLoading(false);
            return;
          }

          console.log('✅ Found ROS businesses:', rosBusinesses?.length || 0, rosBusinesses);

          // Find the business with the most restaurant data (menus, items, tables)
          let selectedBusinessId = null;

          if (rosBusinesses && rosBusinesses.length > 0) {
            console.log('🔍 Selecting best ROS business from', rosBusinesses.length, 'candidates');

            // Optimize: Get menu counts for all businesses in a single query
            const businessIds = rosBusinesses.map(business => business.id);
            const { data: menuCounts } = await supabase
              .from('menus')
              .select('restaurant_id')
              .in('restaurant_id', businessIds);

            // Count menus per business
            const menuCountMap = menuCounts?.reduce((acc, menu) => {
              acc[menu.restaurant_id] = (acc[menu.restaurant_id] || 0) + 1;
              return acc;
            }, {} as Record<string, number>) || {};

            // 🎯 SMART RESTAURANT SELECTION - Prevent wrong restaurant access
            let selectedBusiness = null;
            let maxScore = -1000; // Start with negative score to handle edge cases

            for (const business of rosBusinesses) {
              const businessId = business.id;
              const businessName = business.name;
              const menuCount = menuCountMap[businessId] || 0;

              // 🧮 SCORING ALGORITHM - Multiple factors to identify real restaurants
              let score = 0;

              // Factor 1: Menu count (restaurants should have menus, but not the only factor)
              if (menuCount > 0) {
                const menuPoints = Math.min(menuCount * 5, 25); // Cap at 25 points
                score += menuPoints;
              }

              // Factor 2: Name analysis - Strong indicators of actual restaurants
              const nameLower = businessName.toLowerCase();
              if (nameLower.includes('restaurant') || nameLower.includes('restaurante')) {
                score += 100;
              }
              if (nameLower.includes('café') || nameLower.includes('cafe') || nameLower.includes('bar') ||
                  nameLower.includes('bistro') || nameLower.includes('pizzeria') || nameLower.includes('tavern')) {
                score += 80;
              }

              // Factor 3: Business entity detection - Strong negative indicators
              if (nameLower.includes('analytica') || nameLower.includes('analytics')) {
                score -= 200;
              }
              if (nameLower.includes('llc') || nameLower.includes('inc') || nameLower.includes('corp') ||
                  nameLower.includes('ltd') || nameLower.includes('company')) {
                score -= 150;
              }
              if (nameLower.includes('sme') || nameLower.includes('software') || nameLower.includes('tech')) {
                score -= 100;
              }

              if (score > maxScore) {
                maxScore = score;
                selectedBusiness = business;
                selectedBusinessId = businessId;
              }
            }

            // Final validation
            if (selectedBusiness) {

            } else {

            }

            // Enhanced fallback logic with additional validation
            if (!selectedBusinessId && rosBusinesses.length > 0) {
              console.log('🔄 Using fallback selection logic');

              // Try to get additional business details for better selection
              for (const business of rosBusinesses) {
                const businessId = business.id;
                const businessName = business.name;

                // Get restaurant details to make better decision
                const { data: restaurantDetails } = await supabase
                  .from('restaurants_unified')
                  .select('address, contact_email, opening_hours, description')
                  .eq('id', businessId)
                  .single();

                if (restaurantDetails) {

                  // Prefer businesses with restaurant-like characteristics
                  const hasRestaurantEmail = restaurantDetails.contact_email?.includes('restaurant') ||
                                           restaurantDetails.contact_email?.includes('resto');
                  const hasOpeningHours = restaurantDetails.opening_hours &&
                                        Object.keys(restaurantDetails.opening_hours).length > 0;
                  const hasSpecificAddress = restaurantDetails.address?.includes(',') &&
                                           restaurantDetails.address?.length > 20;

                  if (hasRestaurantEmail || hasOpeningHours || hasSpecificAddress) {
                    selectedBusinessId = businessId;

                    break;
                  }
                }
              }

              // Final fallback - use first business but log warning
              if (!selectedBusinessId) {
                selectedBusinessId = rosBusinesses[0].id;
                console.log('⚠️ Using first ROS business as fallback:', rosBusinesses[0].name);
              }
            }
          }

          if (selectedBusinessId) {
            restaurantId = selectedBusinessId;

            // FIXED: Get all ROS restaurants for the dropdown using app_type
            const { data: userRestaurants, error: userRestaurantsError } = await supabase
              .from('businesses')
              .select('id, name, address, email, logo_url, app_type')
              .eq('user_id', user.id)
              .in('app_type', ['ros', 'ros,sme']) // ROS or dual-app businesses
              .eq('is_active', true);

            if (!userRestaurantsError && userRestaurants) {
              console.log('✅ Found user restaurants:', userRestaurants.length);
              setUserRestaurants(userRestaurants.map(r => ({
                id: r.id,
                name: r.name,
                address: r.address,
                contact_email: r.email,
                logo_url: r.logo_url
              })));
            }
          } else {
            // No ROS businesses found - show appropriate message
            console.log('❌ No ROS businesses found for user');
            setIsLoading(false);
            return;
          }
        }

        // CRITICAL: Ensure we have a valid restaurant ID before proceeding
        if (!restaurantId) {
          console.error('No restaurant ID available after all attempts');
          setIsLoading(false);
          return;
        }

        try {
          // Fetch restaurant details from the database
          const restaurant = await getRestaurantById(restaurantId);

          if (restaurant) {

            setRestaurantInfo({
              id: restaurant.id,
              name: restaurant.name,
              logo_url: restaurant.logo_url,
              address: restaurant.address,
              contact_email: restaurant.contact_email,
              contact_phone: restaurant.contact_phone,
              description: restaurant.description,
              opening_hours: restaurant.opening_hours,
              dynamic_pricing_enabled: restaurant.dynamic_pricing_enabled,
              pricing_rules: restaurant.pricing_rules
            });

            // Save to localStorage for persistence
            localStorage.setItem('selectedRestaurantId', restaurant.id);
          } else {
            // If no restaurant found, use the ID we already have with default values

            setRestaurantInfo({
              id: restaurantId,
              name: 'My Restaurant',
              address: '123 Main St',
              contact_email: user?.email || '<EMAIL>',
              dynamic_pricing_enabled: defaultRestaurantInfo.dynamic_pricing_enabled,
              pricing_rules: defaultRestaurantInfo.pricing_rules
            });

            // Save to localStorage for persistence
            localStorage.setItem('selectedRestaurantId', restaurantId);
          }
        } catch (restaurantError) {
          console.error('Error fetching restaurant details:', restaurantError);
          // Still set the restaurant ID we have, even if details fetch failed
          setRestaurantInfo({
            id: restaurantId,
            name: 'My Restaurant',
            address: '123 Main St',
            contact_email: user?.email || '<EMAIL>',
          });

          // Save to localStorage for persistence
          localStorage.setItem('selectedRestaurantId', restaurantId);
        }

        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching restaurant info:', error);
        setIsLoading(false);
      }
    };

    // Fetch restaurant info when component mounts or when user or table ID changes
    fetchRestaurantInfo();
  }, [user, searchParams]);

  return (
    <RestaurantContext.Provider
      value={{
        restaurantInfo,
        selectedRestaurant: restaurantInfo, // Alias for backward compatibility
        userRestaurants,
        setRestaurantInfo,
        updateRestaurantInDb,
        selectRestaurant,
        isLoading
      }}
    >
      {children}
    </RestaurantContext.Provider>
  );
};

// Create hook for using restaurant context
export const useRestaurant = () => {
  const context = useContext(RestaurantContext);
  if (context === undefined) {
    throw new Error('useRestaurant must be used within a RestaurantProvider');
  }
  return context;
};

export default RestaurantProvider;
