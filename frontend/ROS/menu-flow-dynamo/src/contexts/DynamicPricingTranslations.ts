// Dynamic pricing translations used in the LanguageContext
export const dynamicPricingTranslations = {
  en: {
    // Dynamic Pricing Dashboard
    pricingDashboard: "Dynamic Pricing Dashboard",
    pricingOverview: "Pricing Overview",
    timeBasedRules: "Time-Based Rules",
    categoryBasedRules: "Category-Based Rules",
    currentAdjustments: "Current Price Adjustments",
    priceIncreased: "Price Increased",
    priceDecreased: "Price Decreased",
    priceUnchanged: "Price Unchanged",
    priceTrends: "Price Trends",
    lastUpdated: "Last Updated",
    aiInsights: "AI Pricing Insights",
    suggestedAdjustments: "Suggested Adjustments",
    applyAllSuggestions: "Apply All Suggestions",
    peakHourPricing: "Peak Hour Pricing",
    offPeakPricing: "Off-Peak Pricing",
    normalPricing: "Normal Pricing",
    highTrafficThreshold: "High Traffic Threshold",
    lowTrafficThreshold: "Low Traffic Threshold",
    highTrafficAdjustment: "High Traffic Adjustment (%)",
    lowTrafficAdjustment: "Low Traffic Adjustment (%)",
    categoryRules: "Category Rules",
    addCategoryRule: "Add Category Rule",
    editRule: "Edit Rule",
    deleteRule: "Delete Rule",
    saveChanges: "Save Changes",
    discardChanges: "Discard Changes",
    confirmationNeeded: "Confirmation Needed",
    confirmMessage: "Are you sure you want to apply these changes? This will affect your menu prices immediately.",
    confirmAction: "Confirm",
    cancelAction: "Cancel",
    noItemsInCategory: "No items in this category",
  },
  es: {
    // Dynamic Pricing Dashboard
    pricingDashboard: "Panel de Precios Dinámicos",
    pricingOverview: "Visión General de Precios",
    timeBasedRules: "Reglas Basadas en Tiempo",
    categoryBasedRules: "Reglas Basadas en Categoría",
    currentAdjustments: "Ajustes de Precios Actuales",
    priceIncreased: "Precio Aumentado",
    priceDecreased: "Precio Reducido",
    priceUnchanged: "Precio Sin Cambios",
    priceTrends: "Tendencias de Precios",
    lastUpdated: "Última Actualización",
    aiInsights: "Información de Precios IA",
    suggestedAdjustments: "Ajustes Sugeridos",
    applyAllSuggestions: "Aplicar Todas las Sugerencias",
    peakHourPricing: "Precios en Hora Punta",
    offPeakPricing: "Precios en Hora Baja",
    normalPricing: "Precios Normales",
    highTrafficThreshold: "Umbral de Tráfico Alto",
    lowTrafficThreshold: "Umbral de Tráfico Bajo",
    highTrafficAdjustment: "Ajuste de Tráfico Alto (%)",
    lowTrafficAdjustment: "Ajuste de Tráfico Bajo (%)",
    categoryRules: "Reglas de Categoría",
    addCategoryRule: "Añadir Regla de Categoría",
    editRule: "Editar Regla",
    deleteRule: "Eliminar Regla",
    saveChanges: "Guardar Cambios",
    discardChanges: "Descartar Cambios",
    confirmationNeeded: "Confirmación Necesaria",
    confirmMessage: "¿Está seguro de que desea aplicar estos cambios? Esto afectará inmediatamente los precios de su menú.",
    confirmAction: "Confirmar",
    cancelAction: "Cancelar",
    noItemsInCategory: "No hay elementos en esta categoría",
  }
};
