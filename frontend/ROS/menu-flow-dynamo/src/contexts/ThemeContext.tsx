import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { useRestaurant } from './RestaurantContext';
import { supabase } from '@/integrations/supabase/client';
import { RestaurantTheme, ThemeColors, ThemeLayout, ThemeTypography, ThemeBranding, DEFAULT_THEME, generateCSSVariables } from '@/types/theme';
import { themeService } from '@/services/themeService';

interface BrandSettings {
  tagline: string;
  featured_section_title: string;
}

interface LegacyThemeColors {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  card: string;
  text: string;
}

interface ThemeContextType {
  currentTheme: RestaurantTheme | null;
  isLoading: boolean;
  error: string | null;
  themeColors: LegacyThemeColors;
  brandSettings: BrandSettings;
  loadTheme: (restaurantId: string) => Promise<void>;
  updateTheme: (updates: Partial<RestaurantTheme>) => Promise<void>;
  applyPreset: (presetName: string) => Promise<void>;
  applyTheme: (colors: LegacyThemeColors) => void;
  resetTheme: () => void;
}

const defaultTheme: LegacyThemeColors = {
  primary: "#5f7790",
  secondary: "#11a5e8",
  accent: "#11a5e8",
  background: "#171f31",
  card: "#202940",
  text: "#d5dce2"
};

const defaultBrandSettings: BrandSettings = {
  tagline: "",
  featured_section_title: "Chef's Recommendations"
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // New theme system state
  const [currentTheme, setCurrentTheme] = useState<RestaurantTheme | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Legacy compatibility state
  const [themeColors, setThemeColors] = useState<LegacyThemeColors>(defaultTheme);
  const [brandSettings, setBrandSettings] = useState<BrandSettings>(defaultBrandSettings);
  const [isLoading, setIsLoading] = useState(true);
  const { restaurantInfo } = useRestaurant();

  // Helper function to get restaurant_details.id from business.id
  const getRestaurantDetailsId = async (businessId: string): Promise<string | null> => {
    try {
      const { data, error } = await supabase
        .from('restaurant_details')
        .select('id')
        .eq('business_id', businessId)
        .single();

      if (error) {
        console.error('Error fetching restaurant details ID for theme:', error);
        return null;
      }

      return data?.id || null;
    } catch (error) {
      console.error('Error in getRestaurantDetailsId for theme:', error);
      return null;
    }
  };

  // Apply theme to DOM without setting state to avoid loops
  const applyThemeToDOM = useCallback((theme: RestaurantTheme) => {
    if (typeof document === 'undefined') return;

    const cssVariables = generateCSSVariables(theme);
    const root = document.documentElement;

    // Apply new theme system CSS variables
    Object.entries(cssVariables).forEach(([property, value]) => {
      root.style.setProperty(property, value);
    });

    // Maintain legacy CSS variables for backward compatibility
    root.style.setProperty('--restaurant-primary', theme.colors.primary);
    root.style.setProperty('--restaurant-secondary', theme.colors.secondary);
    root.style.setProperty('--restaurant-accent', theme.colors.accent);
    root.style.setProperty('--restaurant-background', theme.colors.background);
    root.style.setProperty('--restaurant-card', theme.colors.cardBackground);
    root.style.setProperty('--restaurant-text', theme.colors.textPrimary);
  }, []);

  const applyLegacyThemeColors = useCallback((colors: LegacyThemeColors) => {
    if (typeof document === 'undefined') return;
    const root = document.documentElement;

    // Apply legacy CSS custom properties
    root.style.setProperty('--restaurant-primary', colors.primary);
    root.style.setProperty('--restaurant-secondary', colors.secondary);
    root.style.setProperty('--restaurant-accent', colors.accent);
    root.style.setProperty('--restaurant-background', colors.background);
    root.style.setProperty('--restaurant-card', colors.card);
    root.style.setProperty('--restaurant-text', colors.text);
  }, []);

  // Load theme colors from new restaurant_themes table or fallback to legacy businesses table
  useEffect(() => {
    let cancelled = false;

    const loadLegacyTheme = async (restaurantId: string, cancelled: boolean) => {
      let businessData = null;
      let businessError = null;

      const { data: directBusiness, error: directError } = await supabase
        .from('businesses')
        .select('theme_colors, brand_settings, logo_url, name')
        .eq('id', restaurantId)
        .single();

      if (!directError && directBusiness) {
        businessData = directBusiness;
      } else {
        const { data: restaurantDetail, error: rdError } = await supabase
          .from('restaurant_details')
          .select('business_id')
          .eq('business_id', restaurantId)
          .maybeSingle();

        if (rdError || !restaurantDetail) {
          console.error('Error fetching restaurant detail:', rdError);
          return;
        }

        const { data: business, error: businessErr } = await supabase
          .from('businesses')
          .select('theme_colors, brand_settings, logo_url, name')
          .eq('id', restaurantDetail.business_id)
          .single();

        businessData = business;
        businessError = businessErr;
      }

      if (businessError || !businessData) {
        console.error('Error fetching business theme:', businessError);
        return;
      }

      if (businessData?.theme_colors) {
        const colors = businessData.theme_colors as LegacyThemeColors;
        if (!cancelled) {
          setThemeColors({ ...defaultTheme, ...colors });
          applyLegacyThemeColors({ ...defaultTheme, ...colors });
        }
      }

      if (businessData?.brand_settings) {
        const settings = businessData.brand_settings as BrandSettings;
        if (!cancelled) {
          setBrandSettings({ ...defaultBrandSettings, ...settings });
        }
      }
    };

    const loadTheme = async () => {
      try {
        setError(null);

        if (!restaurantInfo.id) {

          if (!cancelled) setIsLoading(false);
          return;
        }

        // Get the correct restaurant_details.id from business.id
        const restaurantDetailsId = await getRestaurantDetailsId(restaurantInfo.id);

        if (!restaurantDetailsId) {

          if (!cancelled) setIsLoading(false);
          return;
        }

        // Try to load from new restaurant_themes table first using restaurant_details.id
        try {
          const theme = await themeService.getActiveTheme(restaurantDetailsId);
          if (theme && !cancelled) {
            setCurrentTheme(theme);
            applyThemeToDOM(theme);
            setIsLoading(false);
            return;
          }
        } catch (themeError) {
          console.log('Could not load theme from restaurant_themes, falling back to legacy theme system');
          // Continue to legacy theme loading
        }

        // Fallback to legacy system (businesses.theme_colors)
        await loadLegacyTheme(restaurantInfo.id, cancelled);

      } catch (error) {
        console.error('Error loading theme:', error);
        setError('Failed to load theme');
      } finally {
        if (!cancelled) setIsLoading(false);
      }
    };

    loadTheme();
    return () => { cancelled = true };
  }, [restaurantInfo?.restaurant_details_id, applyLegacyThemeColors, applyThemeToDOM]);

  // Apply theme when it changes
  useEffect(() => {
    if (currentTheme) {
      applyThemeToDOM(currentTheme);
      
      // Update legacy state for compatibility - moved here to prevent infinite loop
      const colors = {
        primary: currentTheme.colors.primary,
        secondary: currentTheme.colors.secondary,
        accent: currentTheme.colors.accent,
        background: currentTheme.colors.background,
        card: currentTheme.colors.cardBackground,
        text: currentTheme.colors.textPrimary
      };
      
      // Compare with current state to prevent unnecessary updates
      if (JSON.stringify(colors) !== JSON.stringify(themeColors)) {
        setThemeColors(colors);
      }
    } else if (themeColors) {
      applyLegacyThemeColors(themeColors);
    }
  }, [currentTheme, applyThemeToDOM, applyLegacyThemeColors, themeColors]);
  
  // Separate effect for themeColors to prevent dependency loops
  useEffect(() => {
    if (!currentTheme && themeColors) {
      applyLegacyThemeColors(themeColors);
    }
  }, [themeColors, currentTheme, applyLegacyThemeColors]);

  const loadThemeAction = useCallback(async (restaurantId: string) => {
    try {
      setIsLoading(true);
      setError(null);
      const theme = await themeService.getActiveTheme(restaurantId);
      if (theme) {
        setCurrentTheme(theme);
        applyThemeToDOM(theme);
      }
    } catch (err) {
      setError('Failed to load theme');
      console.error('Error loading theme:', err);
    } finally {
      setIsLoading(false);
    }
  }, [applyThemeToDOM]);

  const updateThemeAction = useCallback(async (updates: Partial<RestaurantTheme>) => {
    if (!currentTheme) return;

    try {
      setError(null);
      const updatedTheme = await themeService.updateTheme({
        id: currentTheme.id,
        ...updates
      });
      setCurrentTheme(updatedTheme);
      applyThemeToDOM(updatedTheme);
    } catch (err) {
      setError('Failed to update theme');
      console.error('Error updating theme:', err);
    }
  }, [currentTheme, applyThemeToDOM]);

  const applyPresetAction = useCallback(async (presetName: string) => {
    if (!restaurantInfo.id) return;

    try {
      setError(null);
      const theme = await themeService.applyPresetTheme(restaurantInfo.id, presetName);
      setCurrentTheme(theme);
      applyThemeToDOM(theme);
    } catch (err) {
      setError('Failed to apply preset theme');
      console.error('Error applying preset:', err);
    }
  }, [restaurantInfo.id, applyThemeToDOM]);

  const applyTheme = useCallback((colors: LegacyThemeColors) => {
    setThemeColors(colors);
    applyLegacyThemeColors(colors);
  }, [applyLegacyThemeColors]);

  const resetThemeAction = useCallback(() => {
    if (typeof document === 'undefined') return;
    const root = document.documentElement;
    
    // Reset to default theme
    applyLegacyThemeColors(defaultTheme);
    setThemeColors(defaultTheme);
    setCurrentTheme(null);
    setError(null);
  }, [applyLegacyThemeColors]);

  return (
    <ThemeContext.Provider
      value={{
        currentTheme,
        isLoading,
        error,
        loadTheme: loadThemeAction,
        updateTheme: updateThemeAction,
        applyPreset: applyPresetAction,
        themeColors,
        brandSettings,
        applyTheme,
        resetTheme: resetThemeAction
      }}
    >
      {children}
    </ThemeContext.Provider>
  );
};


export default ThemeProvider;
