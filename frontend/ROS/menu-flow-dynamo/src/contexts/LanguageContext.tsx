import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Define available languages
export type Language = 'en' | 'es';

// Define supported languages with their variants
const SUPPORTED_LANGUAGES: Record<string, Language> = { en: 'en', es: 'es' };

// Language detection utilities
const detectBrowserLanguage = (): Language => {
  // Get browser languages in order of preference
  if (typeof navigator === 'undefined') return 'en';
  const browserLanguages = navigator.languages ?? [navigator.language];

  // Find the first supported language
  for (const lang of browserLanguages) {
    // Check exact match first
    if (SUPPORTED_LANGUAGES[lang]) {
      return SUPPORTED_LANGUAGES[lang];
    }

    // Check language code without region (e.g., 'es' from 'es-MX')
    const baseLanguage = lang.split('-')[0];
    if (SUPPORTED_LANGUAGES[baseLanguage]) {
      return SUPPORTED_LANGUAGES[baseLanguage];
    }
  }

  // Default to English if no supported language found
  return 'en';
};

const getStoredLanguage = (): Language | null => {
  try {
    const stored = localStorage.getItem('preferred_language');
    if (stored && (stored === 'en' || stored === 'es')) {
      return stored as Language;
    }
  } catch (error) {
    console.warn('Error reading stored language:', error);
  }
  return null;
};

const storeLanguage = (language: Language): void => {
  try {
    localStorage.setItem('preferred_language', language);
    // Also store with timestamp for analytics
    localStorage.setItem('language_detection_data', JSON.stringify({
      detectedLanguage: detectBrowserLanguage(),
      selectedLanguage: language,
      timestamp: new Date().toISOString(),
      browserLanguages: navigator.languages || [navigator.language]
    }));
  } catch (error) {
    console.warn('Error storing language:', error);
  }
};

// Geolocation-based language detection (optional enhancement)
const detectLanguageByLocation = async (): Promise<Language | null> => {
  try {
    // This could be enhanced with IP geolocation service
    // For now, we'll use timezone as a rough indicator
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    // Spanish-speaking countries' timezones
    const spanishTimezones = [
      'America/Mexico_City', 'America/Monterrey', 'America/Cancun',
      'America/Argentina/Buenos_Aires', 'America/Argentina/Cordoba',
      'America/Bogota', 'America/Santiago', 'America/Lima',
      'America/Caracas', 'America/Montevideo', 'America/Asuncion',
      'America/Guayaquil', 'America/La_Paz', 'America/Costa_Rica',
      'America/Panama', 'America/Guatemala', 'America/Managua',
      'America/Tegucigalpa', 'America/El_Salvador', 'America/Santo_Domingo',
      'America/Havana', 'America/Puerto_Rico', 'Europe/Madrid',
      'Atlantic/Canary', 'Africa/Ceuta'
    ];

    if (spanishTimezones.includes(timezone)) {
      return 'es';
    }

    return 'en'; // Default to English
  } catch (error) {
    console.warn('Error detecting language by location:', error);
    return null;
  }
};

// Define translation type
type Translations = {
  [key in Language]: {
    [key: string]: string;
  };
};

// App translations
export const translations: Translations = {
  en: {
    // Navigation
    dashboard: "Dashboard",
    menuManagement: "Menu Management",
    menusManagement: "Menus Management",
    manageMenuItems: "Manage Menu Items",

    // Traffic Analytics
    trafficAnalytics: "Traffic Analytics",
    salesAnalytics: "Sales Analytics",
    menuAnalytics: "Menu Analytics",
    totalSales: "Total Sales",
    orderCount: "Order Count",
    averageOrderValue: "Average Order Value",
    salesByHour: "Sales by Hour",
    salesByCategory: "Sales by Category",
    salesByDayOfWeek: "Sales by Day of Week",
    popularItems: "Popular Items",
    bestSellingItems: "Best-selling menu items",
    priceRecommendations: "Price Recommendations",
    smartPricingSuggestions: "Smart pricing suggestions based on popularity and traffic",
    categoryPerformance: "Category Performance",
    last24Hours: "Last 24 hours",
    last7Days: "Last 7 days",
    last30Days: "Last 30 days",
    day: "Day",
    week: "Week",
    month: "Month",
    orders: "orders",
    sales: "sales",
    applyToMenu: "Apply to Menu",
    noPricingRecommendations: "No pricing recommendations available",
    noCategoryData: "No category data available",
    refreshData: "Refresh Data",
    currentTraffic: "Current Traffic",
    tableOccupancy: "Table Occupancy",
    pricingStatus: "Pricing Status",
    dataConfidence: "Data Confidence",
    high: "High",
    medium: "Medium",
    low: "Low",
    normal: "Normal",
    lastUpdated: "Last updated",
    tables: "tables",
    occupancy: "occupancy",
    currentDynamicPricing: "Current dynamic pricing",
    highConfidence: "High confidence",
    mediumConfidence: "Medium confidence",
    lowConfidence: "Low confidence",
    trafficTrend: "Traffic Trend",
    todayTraffic: "Today's hourly traffic",
    tableStatus: "Table Status",
    currentOccupancy: "Current occupancy",
    occupiedTable: "Occupied",
    availableTable: "Available",
    time: "Time",
    noTrafficData: "No traffic data available for today",
    noTableData: "No table data available",
    weeklyTrafficPattern: "Weekly Traffic Pattern",
    weeklyLast7Days: "Last 7 days",
    averageOccupancy: "Average occupancy",
    weeklyDay: "Day",
    noWeeklyData: "No weekly data available",
    pricingRules: "Pricing Rules",
    currentDynamicPricingRules: "Current dynamic pricing rules",
    editRules: "Edit Rules",
    priceAdjustment: "price adjustment",
    noPricingRules: "No pricing rules available",
    noSalesData: "No sales data available",
    noPopularItems: "No popular items data available",
    reviewAnalytics: "Review your restaurant's performance analytics",
    tableManagement: "Table Management",
    orderManagement: "Order Management",
    analytics: "Analytics",
    settings: "Settings",
    signOut: "Sign Out",
    login: "Login",

    // Common
    welcome: "Welcome back!",
    todaysOverview: "Here's what's happening with your restaurant today.",
    cookieConsentMessage: "We use cookies to enhance your experience and remember your order history. By using our service, you agree to our use of cookies.",
    accept: "Accept",
    decline: "Decline",
    todaysOrders: "Today's Orders",
    currentTrafficOverview: "Current Traffic",
    dynamicPricing: "Dynamic Pricing",
    recentOrders: "Recent Orders",
    viewAllOrders: "View All Orders",
    trafficHeatmap: "Traffic Heatmap",
    quickMenuManagement: "Quick Menu Management",
    addItem: "Add Item",
    configureRules: "Configure Pricing Rules",
    optional: "optional",

    // Hero Section
    welcomeToRestaurant: "Welcome to our Restaurant",
    enjoyDiningExperience: "Enjoy a delightful dining experience with our digital menu service",
    viewMenuAndOrder: "View Menu & Order",
    restaurantOwnerLogin: "Restaurant Owner Login",
    scanQrCode: "Scan QR Code",
    scanQrCodeDesc: "Scan the QR code on your table to access our digital menu",
    placeYourOrder: "Place Your Order",
    placeYourOrderDesc: "Browse our menu and select items to add to your order",
    enjoyYourMeal: "Enjoy Your Meal",
    enjoyYourMealDesc: "Receive updates on your order status and enjoy when it arrives",

    // Menu Management
    menuItems: "Menu Items",
    name: "Name",
    basePrice: "Base Price",
    currentPrice: "Current Price",
    available: "Available",
    duration: "Duration",
    edit: "Edit",
    delete: "Delete",
    addMenuItem: "Add Menu Item",
    editMenuItem: "Edit Menu Item",
    save: "Save",
    cancel: "Cancel",
    createMenuItem: "Create a new menu item for your restaurant",
    updateMenuItem: "Update the details of this menu item",
    image: "Image",
    uploadImage: "Upload Image",
    imageUploadHint: "Upload a photo of your menu item (up to 2MB)",
    description: "Description",
    menu: "Menu",
    availableDescription: "Whether this item is currently available for ordering",
    food: "Food",
    drinks: "Drinks",
    desserts: "Desserts",
    starters: "Starters",
    mains: "Main Courses",
    sides: "Side Dishes",
    specials: "Specials",
    selectMenu: "Select a menu",
    startTime: "Start Time",
    endTime: "End Time",
    create: "Create",
    addMenu: "Add Menu",

    // Customer Menu Interface
    allMenu: "All Menu",

    // Table Management
    tablesSection: "Tables",
    tableNumber: "Table Number",
    capacity: "Capacity",
    status: "Status",
    location: "Location",
    occupied: "Occupied",
    tableAvailable: "Available",
    addTable: "Add Table",
    editTable: "Edit Table",
    generateQR: "Generate QR Code",

    // Order Management
    ordersList: "Orders",
    orderId: "Order ID",
    table: "Table",
    items: "Items",
    total: "Total",
    orderStatus: "Status",
    orderTime: "Order Time",
    pending: "Pending",
    preparing: "Preparing",
    ready: "Ready",
    delivered: "Delivered",
    completed: "Completed",
    cancelled: "Cancelled",
    viewOrder: "View Order",
    updateStatus: "Update Status",

    // Analytics
    salesOverview: "Sales Overview",
    analyticsPopularItems: "Popular Items",
    peakHours: "Peak Hours",
    revenueMetric: "Revenue",
    dailySales: "Daily Sales",
    monthlySales: "Monthly Sales",

    // Settings
    generalSettings: "General Settings",
    restaurantProfile: "Restaurant Profile",
    pricingRulesSettings: "Pricing Rules",
    userProfile: "User Profile",
    language: "Language",
    english: "English",
    spanish: "Spanish",
    peakHoursAdjustment: "Peak Hours Adjustment",
    quietHoursAdjustment: "Quiet Hours Adjustment",
    restaurantName: "Restaurant Name",
    logoUrl: "Logo URL",
    saveProfile: "Save Restaurant Profile",
    saving: "Saving...",
    profileUpdated: "Profile Updated",
    profileUpdateSuccess: "Your restaurant profile has been updated successfully",

    // Dynamic Pricing
    highTrafficAdjustment: "High Traffic Adjustment",
    mediumTrafficAdjustment: "Medium Traffic Adjustment",
    lowTrafficAdjustment: "Low Traffic Adjustment",
    applyDynamicPricingTo: "Apply Dynamic Pricing To",
    foodItems: "Food Items",
    drinkItems: "Drink Items",
    aiConfidenceThreshold: "AI Confidence Threshold",

    // Dashboard specific
    dashboardOverview: "Dashboard Overview",
    realTimeInsights: "Real-time insights for your business",
    refresh: "Refresh",
    testLowTraffic: "Test Low Traffic",
    testHighTraffic: "Test High Traffic",
    todaysRevenue: "Today's Revenue",
    vsYesterday: "vs yesterday",
    highTraffic: "HIGH TRAFFIC",
    mediumTraffic: "MEDIUM TRAFFIC",
    lowTraffic: "LOW TRAFFIC",
    normalTraffic: "NORMAL TRAFFIC",
    on: "ON",
    off: "OFF",
    loading: "Loading",
    confidence: "Confidence",
    analyzingTraffic: "Analyzing traffic",
    enableAiPricing: "Enable AI pricing",
    noOrdersToday: "No orders yet today",
    loadingPopularItems: "Loading popular items",
    loadingOrders: "Loading orders",
    anonymous: "Anonymous",
    noRecentOrders: "No recent orders",
    viewReports: "View Reports",
    customerExperience: "Customer Experience",
    manageTables: "Manage Tables",
    manageItems: "Manage Items",
    previewMenu: "Preview Menu",

    // Error handling
    errorLoadingDashboard: "Error loading dashboard",
    failedToLoadDashboardData: "Failed to load dashboard data",
    tryAgain: "Try Again",

    // Notifications
    newOrderReceived: "New order received!",
    order: "Order",
    hasBeenPlaced: "has been placed.",
    savePricingRules: "Save Pricing Rules",
    pricingRulesUpdated: "Pricing Rules Updated",
    pricingRulesUpdateSuccess: "Your dynamic pricing rules have been saved successfully",
    highTrafficExplanation: "During high traffic periods (80% or more capacity), prices will increase by this percentage.",
    mediumTrafficExplanation: "During medium traffic periods (50-80% capacity), prices will increase by this percentage.",
    lowTrafficExplanation: "During low traffic periods (under 30% capacity), prices can be decreased to attract more customers.",
    confidenceExplanation: "Only apply price changes when the AI is at least this confident in its recommendations.",

    // Feedback Form
    shareFeedback: "Share Feedback",
    foodQuality: "Food Quality",
    serviceQuality: "Service Quality",
    appExperience: "App Experience",
    additionalComments: "Additional Comments",
    tellUsMoreAboutExperience: "Tell us more about your experience",
    shareEmailForUpdates: "Share email for updates",
    submitFeedback: "Submit Feedback",
    skipFeedback: "Skip Feedback",
    thankYouForFeedback: "Thank you for your feedback",
    errorSubmittingFeedback: "Error submitting feedback",
    pleaseTryAgainLater: "Please try again later",

    // Notifications Extended
    notifications: "Notifications",
    notificationsBellTitle: "Notifications",
    unreadNotifications: "unread notifications",
    allCaughtUp: "All caught up!",
    loadingNotifications: "Loading notifications...",
    unread: "unread",
    allNotifications: "All notifications",
    markAllRead: "Mark All Read",
    all: "All",
    read: "Read",
    noNotifications: "No notifications",
    youreAllCaughtUp: "You're all caught up! No unread notifications.",
    noReadNotifications: "No read notifications yet.",
    noNotificationsToDisplay: "No notifications to display.",
    viewAllNotifications: "View All Notifications",

    // Staff Requests
    staffRequests: "Staff Requests",
    manageCustomerAssistanceRequests: "Manage customer assistance requests",
    generalAssistance: "General Assistance",
    tableService: "Table Service",
    orderIssue: "Order Issue",
    other: "Other",
    newStaffRequestReceived: "New staff request received!",
    markAsInProgress: "Mark as In Progress",
    markAsCompleted: "Mark as Completed",
    requestMarkedAs: "Request marked as",
    failedToUpdateRequestStatus: "Failed to update request status",
    noRequestsAvailable: "No requests available",
    allRequestsComplete: "All requests are complete!",

    // Customer Sentiment
    customerSentiment: "Customer Sentiment",
    sentimentAnalysis: "Sentiment Analysis",
    overallRating: "Overall Rating",
    totalFeedback: "Total Feedback",
    positive: "Positive",
    neutral: "Neutral",
    negative: "Negative",
    recentFeedback: "Recent Feedback",
    trends: "Trends",
    foodRating: "Food Rating",
    serviceRating: "Service Rating",
    appRating: "App Rating",
    noFeedbackData: "No feedback data available",

    // AI Training Sync
    aiTrainingSync: "AI Training Sync",
    feedbackSyncDashboard: "AI Training Feedback Sync Dashboard",
    manageFeedbackSync: "Manage customer feedback sync to centralized database for AI training",
    connectionStatus: "Connection Status",
    connectedToCentralizedDatabase: "Connected to centralized database",
    cannotConnect: "Cannot connect to centralized database",
    testingConnection: "Testing connection...",
    syncStatus: "Sync Status",
    localFeedback: "Local Feedback",
    needsMigration: "Needs Migration",
    yes: "Yes",
    no: "No",
    checkStatus: "Check Status",
    testSync: "Test Sync",
    migrateData: "Migrate Data",
    setupAutoSync: "Setup Auto-Sync",
    results: "Results",
    migrationResults: "Migration Results",
    migrated: "Migrated",
    failed: "Failed",
    details: "Details",
    aboutAiTrainingSync: "About AI Training Sync",
    dashboardDescription: "This dashboard allows you to sync customer feedback to our centralized SME Analytica database for AI training and insights.",
    migration: "Migration",
    migrationDescription: "One-time sync of existing feedback data to the central system.",
    autoSync: "Auto-Sync",
    autoSyncDescription: "Real-time automatic sync of new feedback as it's received.",
    purpose: "Purpose",
    purposeDescription: "Your feedback helps train AI models to provide better insights across all SME Analytica restaurants while keeping your data secure.",
    startingFeedbackMigration: "Starting feedback migration...",
    migrationSuccessful: "Migration successful!",
    recordsSyncedToCentralized: "records synced to centralized database for AI training.",
    migrationFailed: "Migration failed:",
    migrationError: "Migration error:",
    unknownError: "Unknown error",
    autoSyncConfigured: "Auto-sync configured! New feedback will automatically sync to centralized database.",
    autoSyncSetupFailed: "Auto-sync setup failed:",
    setupError: "Setup error:",
    testingSampleFeedbackSync: "Testing sample feedback sync...",
    sampleSyncSuccessful: "Sample sync successful! Test data is now available for AI training.",
    sampleSyncFailed: "Sample sync failed:",
    testSyncError: "Test sync error:",

    // Settings - Pricing Rules
    pricingRulesSection: "Pricing Rules",
    currentDynamicPricingRulesSettings: "Current dynamic pricing rules settings",
    enableDynamicPricing: "Enable Dynamic Pricing",
    dynamicPricingEnabled: "Dynamic pricing is enabled",
    dynamicPricingDisabled: "Dynamic pricing is disabled",
    foodPricing: "Food pricing",
    drinksPricing: "Drinks pricing",
    enabled: "ENABLED",
    disabled: "DISABLED",
    loadingSettings: "Loading settings...",
    noSettingsFound: "No settings found",
    failedToLoadSettings: "Failed to load settings",

     // AdminLayout Descriptions
     manageMenuItemsDescription: "Manage your restaurant's menu items",
     manageTablesAndQR: "Manage your restaurant's tables and QR codes",
     viewManageOrders: "View and manage all customer orders",
     manageRestaurantMenus: "Manage your restaurant's menus",
     configureRestaurantSettings: "Configure your restaurant's settings and preferences",

     // QR Code Management
     qrCodeManagement: "QR Code Management",
     backToDashboard: "Back to Dashboard",
     existingTables: "Existing Tables",
     newTable: "New Table",
     generateQRCodesForTables: "Generate QR Codes for Existing Tables",
     selectTableToGenerate: "Select a table to generate or regenerate its QR code",
     loadingTables: "Loading tables...",
     noTablesFound: "No tables found.",
     createYourFirstTable: "Create Your First Table",
     qrCode: "QR Code",
     download: "Download",
     createNewTable: "Create New Table",
     addNewTableAndQR: "Add a new table and generate its QR code",
     tableNumberRequired: "Table Number*",
     numberOfSeats: "Number of Seats",
     zoneOptional: "Zone (Optional)",
     creating: "Creating...",
     createTableAndQR: "Create Table & Generate QR Code",
     qrCodeDownload: "QR Code Download",
     qrCodeBeingDownloaded: "Your QR code is being downloaded.",
     downloadFailed: "Download Failed",
     couldNotDownloadQR: "Could not download the QR code. Please try again.",

     // Menu Pages
     allMenuPage: "All Menu",
     loadingMenuItems: "Loading menu items...",
     noDrinksAvailable: "No drinks available",
     noFoodItemsAvailable: "No food items available",
     checkBackLater: "Please check back later or contact the restaurant.",
     noMenuItemsAvailable: "No menu items available",
     addedToOrder: "Added to your order",

     // Enhanced Menu Interface - Recommendations
     chefsPickLabel: "Chef's Pick",
     popularLabel: "Popular",
     recommendedLabel: "Recommended",
     addToCartButton: "Add to Cart",
     orderedTimes: "ordered {count} times",
     recommendationsTitle: "Recommendations",
    featuredItems: "Featured Items",

     // Enhanced Menu Interface - Cart
     yourOrder: "Your Order",
     yourCartIsEmpty: "Your cart is empty",
     addItemsToStart: "Add items from the menu to start your order",
     specialInstructions: "Special Instructions",
     specialInstructionsPlaceholder: "e.g., \"no cheese\", \"meat well done\", \"extra spicy\"...",
     specialInstructionsHint: "Tell us about your food preferences",
     subtotal: "Subtotal",
     placeOrder: "Place Order",
     removeItem: "Remove item",
     increaseQuantity: "Increase quantity",
     decreaseQuantity: "Decrease quantity",

     // Order History
     yourOrders: "Your Orders",
     noOrders: "No orders yet",
     noOrdersMessage: "You haven't placed any orders yet. Start by browsing our delicious menu!",
     browseMenu: "Browse Menu",
     activeOrders: "Active Orders",
     pastOrders: "Past Orders",
     active: "active",

     // Enhanced Menu Interface - Menu Status
     menuCurrentlyUnavailable: "Menu Currently Unavailable",
     chefsPreparingSomething: "Our chefs are preparing something special. Please check back during our regular hours.",
     liveMenu: "Live menu",
     menuAvailable: "Menu Available",

     // Enhanced Menu Interface - Restaurant Status
     dineInAvailable: "Dine In Available",
     takeoutAvailable: "Takeout Available",
     deliveryAvailable: "Delivery Available",
     openNow: "Open Now",
     closedNow: "Closed Now",
     opensAt: "Opens at {time}",
     closesAt: "Closes at {time}",

     // Dietary Options
     dietaryOptions: "Dietary Options",
     allergyInformation: "Allergy Information",
     isVegan: "Vegan",
     isVegetarian: "Vegetarian",
     isGlutenFree: "Gluten-Free",
     isDairyFree: "Dairy-Free",
     isNutFree: "Nut-Free",
     spiceLevel: "Spice Level",
     dietaryNotes: "Dietary Notes",
     allergens: "Allergens",
     mild: "Mild",
     mediumSpice: "Medium",
     spicy: "Spicy",
     verySpicy: "Very Spicy",
     extraSpicy: "Extra Spicy",
     noSpice: "No Spice",
     contains: "Contains",
     mayContain: "May Contain",
     additionalDietaryInfo: "Additional dietary information",
     selectSpiceLevel: "Select spice level",
     enterAllergens: "Enter allergens (comma separated)",
     allergensHint: "List any allergens this item contains, separated by commas (e.g., nuts, dairy, gluten)",

     // Settings Extended
     restaurantLogo: "Restaurant Logo",
     restaurantAddress: "Restaurant Address",
     enterOpeningHours: "Enter opening hours information",
     briefRestaurantDescription: "A brief description of your restaurant",
     languageChanged: "Language Changed",
     languageSetToEnglish: "Language set to English",
     languageSetToSpanish: "Language set to Spanish",
     error: "Error",
     mustBeLoggedIn: "You must be logged in to update your restaurant profile",
     failedToUpdateProfile: "Failed to update restaurant profile",
     mustBeLoggedInPricing: "You must be logged in to update pricing rules",
     pricingRulesUpdatedSuccess: "Your dynamic pricing rules have been saved successfully",
     failedToUpdatePricingRules: "Failed to update pricing rules",

     // Enhanced Visibility Dashboard
     enhancedVisibility: "Enhanced Visibility",
     chefRecommendations: "Chef Recommendations",
     enhancedVisibilityDashboard: "Enhanced Visibility Dashboard",
     manageRestaurantOnlinePresenceDescription: "Manage your restaurant's online presence and visibility across review platforms, search engines, and AI systems",
     loadingRestaurantData: "Loading restaurant data...",

     // Chef Recommendations Admin Interface
     manageChefRecommendations: "Manage Chef Recommendations",
     manageFeaturedMenuItems: "Manage Featured Menu Items",
     featuredItemsCount: "Featured Items ({count})",
     availableMenuItems: "Available Menu Items",
     priority: "Priority",
     category: "Category",
     price: "Price",
     chefNotes: "Chef Notes",
     actions: "Actions",
     addChefRecommendation: "Add Chef Recommendation",
     addFirstRecommendation: "Add First Recommendation",
     selectMenuItem: "Select a menu item",
     selectMenuItemPlaceholder: "Select a menu item",
     priorityLabel: "Priority (1 = highest)",
     chefNotesLabel: "Chef's Notes (optional)",
     chefNotesPlaceholder: "Add special notes about this dish...",
     recommendationType: "Recommendation Type",
     signature: "Signature",
     popular: "Popular",
     seasonal: "Seasonal",
     special: "Special",
     loadingChefRecommendations: "Loading chef recommendations...",
     failedToLoadChefRecommendations: "Failed to load chef recommendations. Please try again.",
     noRecommendationsYet: "No recommendations yet",
     startByAddingFeaturedItems: "Start by adding your chef's featured items",
     manageFeaturedItemsDescription: "Manage featured items and chef's special recommendations",
     selectMenuItemAndConfigure: "Select a menu item and configure the recommendation settings",
     unavailable: "Unavailable",
     priorityNumber: "Priority: {number}",
     featuredVisibleToCustomers: "Featured (visible to customers)",
     updateRecommendation: "Update Recommendation",
     addRecommendation: "Add Recommendation",
     addChefNotes: "Add chef notes...",
     saveNotes: "Save Notes",
     customerFeedback: "Customer Feedback",
     publicReviews: "Public Reviews",
     platformStatus: "Platform Status",
     boostOnlinePresence: "Boost your restaurant's online presence with SME Analytica",
     runSEOAudit: "Run SEO Audit",
     publishedReviews: "Published Reviews",
     averageRating: "Average Rating",
     seoScore: "SEO Score",
     aiVisibility: "AI Visibility",
     fromLastMonth: "from last month",
     thisMonth: "this month",
     aiMentions: "AI mentions",
     overview: "Overview",
     customerReviews: "Customer Reviews",
     seoTools: "SEO Tools",
     contentHub: "Content Hub",
     platformIntegrations: "Platform Integrations",
     yourVisibilityAcrossPlatforms: "Your visibility across all platforms",
     performanceTrends: "Performance Trends",
     yourVisibilityGrowthOverTime: "Your visibility growth over time",
     searchVisibility: "Search Visibility",
     reviewEngagement: "Review Engagement",
     noPlatformIntegrationsConfiguredYet: "No platform integrations configured yet",
     failedToLoadDashboard: "Failed to load dashboard data",
     loadingDashboardData: "Loading dashboard data...",
     retry: "Retry",
     seoAuditStarted: "SEO Audit Started",
     quickActions: "Quick Actions",
     boostYourVisibilityWithTheseRecommendedActions: "Boost your visibility with these recommended actions",
     autoPublishReviews: "Auto-Publish Reviews",
     shareCustomerFeedbackWithAIAndGoogle: "Share customer feedback with AI and Google",
     seoHealthCheck: "SEO Health Check",
     analyzeAndImproveSearchRankings: "Analyze and improve search rankings",
     createContent: "Create Content",
     generateAIoptimizedContent: "Generate AI-optimized content",
     reviewSharingSettings: "Review Sharing Settings",
     configureHowYourReviewsAreSharedAcrossPlatforms: "Configure how your reviews are shared across platforms",
     autoPublishThreshold: "Auto-Publish Threshold",
     moderation: "Moderation",
     required: "Required",
     automatic: "Automática",
     reviewApproval: "review approval",
     customerFeedbackReadyToPublish: "Customer Feedback Ready to Publish",
     recentCustomerFeedbackThatCanBeSharedToBoostYourVisibility: "Recent customer feedback that can be shared to boost your visibility",
     autoPublishAll: "Auto-Publish All",
     noEligibleCustomerFeedbackFound: "No eligible customer feedback found.",
     customerReviewsWillAppearHereOnceReceived: "Customer reviews will appear here once received.",
     publish: "Publish",
     shareWithAI: "Share with AI",
     shareWithGoogle: "Share with Google",
     noPublishedReviewsYet: "No published reviews yet.",
     publishedReviewsWillAppearHereForPlatformSharing: "Published reviews will appear here for platform sharing.",
     featured: "Featured",
     google: "Google",
     social: "Social",
     aiReady: "AI Ready",
     reviewPublished: "Review Published",
     customerReviewHasBeenPublishedSuccessfully: "Customer review has been published successfully",
     success: "Success",
     seoPerformance: "SEO Performance",
     yourSearchEngineOptimizationStatus: "Your search engine optimization status",
     runFullAudit: "Run Full Audit",
     overallSEOScore: "Overall SEO Score",
     technicalSEO: "Technical SEO",
     localSEO: "Local SEO",
     seoRecommendations: "SEO Recommendations",
     actionsToImproveYourSearchRanking: "Actions to improve your search ranking",
     runAnSEOAuditToGetPersonalizedRecommendations: "Run an SEO audit to get personalized recommendations",
     startSEOAudit: "Start SEO Audit",
     structuredDataAndSchema: "Structured Data and Schema",
     helpSearchEnginesUnderstandYourRestaurant: "Help search engines understand your restaurant",
     restaurantSchema: "Restaurant Schema",
     reviewSchema: "Review Schema",
     menuSchema: "Menu Schema",
     updateSchemaMarkup: "Update Schema Markup",
     schemaMarkupHasBeenUpdated: "Schema markup has been updated",
     publishedContent: "Published Content",
     yourLatestContentForAISearchVisibility: "Your latest content for AI and search visibility",
     createNew: "Create New",
     noPublishedContentYet: "No published content yet.",
     createContentToImproveYourOnlinePresence: "Create content to improve your online presence.",
     engagement: "Engagement",
     aiGeneratedContentIdeas: "AI Generated Content Ideas",
     smartContentSuggestionsToBoostYourVisibility: "Smart content suggestions to boost your visibility",
     noContentIdeasAvailable: "No content ideas available.",
     aiWillGenerateIdeasBasedOnYourRestaurantData: "AI will generate ideas based on your restaurant data.",
     contentCreated: "Content Created",
     newContentHasBeenCreatedAsADraft: "New content has been created as a draft",
     yourComprehensiveSEOAuditIsRunning: "Your comprehensive SEO audit is running.",
     reviewsCurrentlySharedAcrossPlatformsForAISearchVisibility: "Reviews currently shared across platforms for AI and search visibility",

     // Subscription & Billing
     subscriptionBilling: "Subscription & Billing",
     currentSubscription: "Current Subscription",
     billingManagement: "Billing Management",
     manageBilling: "Manage Billing",
     upgradeToBasic: "Upgrade to Basic",
     upgradeToPremium: "Upgrade to Premium",
     getPremium: "Get Premium",
     freeTrialActive: "Free Trial Active",
     daysRemaining: "Days Remaining",
     trialEndsIn: "Trial Ends In",
     days: "days",
     subscribeNow: "Subscribe Now",
     continueUsingFeatures: "to continue using all features after your trial ends.",
     nextBillingDate: "Next Billing Date",
     customerID: "Customer ID",
     planFeatures: "Plan Features",
     restaurant: "Restaurant",
     restaurants: "Restaurants",
     menuItem: "Menu Item",
     unlockAIPoweredFeatures: "Unlock AI-Powered Features and Unlimited Access",
     securelyManageSubscription: "Securely manage your subscription through Stripe",
     aiDynamicPricing: "AI Dynamic Pricing",
     advancedAnalytics: "Advanced Analytics",
     unlimitedEverything: "Unlimited Everything",
     posIntegration: "POS Integration",
     freeTrial: "Free Trial",
     basic: "Basic",
     premium: "Premium",
     currentPlan: "Current Plan",
     tryThisPremiumFeature: "Try this premium feature during your trial!",
  },
  es: {
    // Navigation
    dashboard: "Panel Principal",
    menuManagement: "Gestión de Menú",
    menusManagement: "Gestión de Menús",
    manageMenuItems: "Administrar Elementos del Menú",
    tableManagement: "Gestión de Mesas",
    orderManagement: "Gestión de Pedidos",
    analytics: "Análisis",
    settings: "Configuración",
    signOut: "Cerrar Sesión",
    login: "Iniciar Sesión",

    // Traffic Analytics
    trafficAnalytics: "Análisis de Tráfico",
    salesAnalytics: "Análisis de Ventas",
    menuAnalytics: "Análisis de Menú",
    totalSales: "Ventas Totales",
    orderCount: "Número de Órdenes",
    averageOrderValue: "Valor Medio de Pedido",
    salesByHour: "Ventas por Hora",
    salesByCategory: "Ventas por Categoría",
    salesByDayOfWeek: "Ventas por Día de la Semana",
    popularItems: "Artículos Populares",
    bestSellingItems: "Artículos más vendidos del menú",
    priceRecommendations: "Recomendaciones de Precio",
    smartPricingSuggestions: "Sugerencias de precios inteligentes basadas en popularidad y tráfico",
    categoryPerformance: "Rendimiento por Categoría",
    last24Hours: "Últimas 24 horas",
    last7Days: "Últimos 7 días",
    last30Days: "Últimos 30 días",
    day: "Día",
    week: "Semana",
    month: "Mes",
    orders: "pedidos",
    sales: "ventas",
    applyToMenu: "Aplicar al Menú",
    noPricingRecommendations: "No hay recomendaciones de precios disponibles",
    noCategoryData: "No hay datos de categoría disponibles",
    refreshData: "Actualizar Datos",
    currentTraffic: "Tráfico Actual",
    tableOccupancy: "Ocupación de Mesas",
    pricingStatus: "Estado de Precios",
    dataConfidence: "Confianza de Datos",
    high: "Alto",
    medium: "Medio",
    low: "Bajo",
    normal: "Normal",
    lastUpdated: "Última actualización",
    tables: "mesas",
    occupancy: "ocupación",
    currentDynamicPricing: "Precio dinámico actual",
    highConfidence: "Alta confianza",
    mediumConfidence: "Confianza media",
    lowConfidence: "Baja confianza",
    trafficTrend: "Tendencia de Tráfico",
    todayTraffic: "Tráfico por hora de hoy",
    tableStatus: "Estado de Mesas",
    currentOccupancy: "Ocupación actual",
    occupiedTable: "Ocupadas",
    availableTable: "Disponibles",
    time: "Hora",
    noTrafficData: "No hay datos de tráfico disponibles para hoy",
    noTableData: "No hay datos de mesas disponibles",
    weeklyTrafficPattern: "Patrón de Tráfico Semanal",
    weeklyLast7Days: "Últimos 7 días",
    averageOccupancy: "Ocupación promedio",
    weeklyDay: "Día",
    noWeeklyData: "No hay datos semanales disponibles",
    pricingRules: "Reglas de Precios",
    currentDynamicPricingRules: "Reglas actuales de precios dinámicos",
    editRules: "Editar Reglas",
    priceAdjustment: "ajuste de precio",
    noPricingRules: "No hay reglas de precios disponibles",
    noSalesData: "No hay datos de ventas disponibles",
    noPopularItems: "No hay datos de elementos populares disponibles",
    reviewAnalytics: "Revise los análisis de rendimiento de su restaurante",

    // Common
    welcome: "¡Bienvenido de nuevo!",
    todaysOverview: "Esto es lo que está sucediendo con su restaurante hoy.",
    cookieConsentMessage: "Utilizamos cookies para mejorar su experiencia y recordar su historial de pedidos. Al utilizar nuestro servicio, acepta nuestro uso de cookies.",
    accept: "Aceptar",
    decline: "Rechazar",
    todaysOrders: "Pedidos de Hoy",
    currentTrafficOverview: "Tráfico Actual",
    dynamicPricing: "Precios Dinámicos",
    recentOrders: "Pedidos Recientes",
    viewAllOrders: "Ver Todos los Pedidos",
    trafficHeatmap: "Mapa de Calor de Tráfico",
    quickMenuManagement: "Gestión Rápida de Menú",
    addItem: "Añadir Elemento",
    configureRules: "Configurar Reglas de Precios",
    optional: "opcional",

    // Hero Section
    welcomeToRestaurant: "Bienvenido a nuestro Restaurante",
    enjoyDiningExperience: "Disfruta de una experiencia gastronómica excepcional con nuestro servicio de menú digital",
    viewMenuAndOrder: "Ver Menú y Pedir",
    restaurantOwnerLogin: "Acceso para Propietarios",
    scanQrCode: "Escanea el Código QR",
    scanQrCodeDesc: "Escanea el código QR en tu mesa para acceder a nuestro menú digital",
    placeYourOrder: "Haz tu Pedido",
    placeYourOrderDesc: "Navega por nuestro menú y selecciona los platos para añadir a tu pedido",
    enjoyYourMeal: "Disfruta tu Comida",
    enjoyYourMealDesc: "Recibe actualizaciones sobre el estado de tu pedido y disfruta cuando llegue",

    // Menu Management
    menuItems: "Elementos del Menú",
    name: "Nombre",
    basePrice: "Precio Base",
    currentPrice: "Precio Actual",
    available: "Disponible",
    duration: "Duración",
    edit: "Editar",
    delete: "Eliminar",
    addMenuItem: "Añadir Elemento al Menú",
    editMenuItem: "Editar Elemento del Menú",
    save: "Guardar",
    cancel: "Cancelar",
    createMenuItem: "Crear un nuevo elemento de menú para su restaurante",
    updateMenuItem: "Actualizar los detalles de este elemento del menú",
    image: "Imagen",
    uploadImage: "Subir Imagen",
    imageUploadHint: "Sube una foto de tu elemento de menú (hasta 2MB)",
    description: "Descripción",
    menu: "Menú",
    availableDescription: "Si este elemento está actualmente disponible para ordenar",
    food: "Comida",
    drinks: "Bebidas",
    desserts: "Postres",
    starters: "Entrantes",
    mains: "Platos Principales",
    sides: "Guarniciones",
    specials: "Especialidades",
    selectMenu: "Seleccionar menú",
    startTime: "Hora de Inicio",
    endTime: "Hora de Fin",
    create: "Crear",
    addMenu: "Añadir Menú",

    // Table Management
    tablesList: "Mesas",
    tableNumber: "Número de Mesa",
    capacity: "Capacidad",
    status: "Estado",
    location: "Ubicación",
    occupied: "Ocupada",
    tableAvailable: "Disponible",
    addTable: "Añadir Mesa",
    editTable: "Editar Mesa",
    generateQR: "Generar Código QR",

    // Order Management
    ordersList: "Pedidos",
    orderId: "ID de Pedido",
    table: "Mesa",
    items: "Artículos",
    total: "Total",
    orderStatus: "Estado",
    orderTime: "Hora del Pedido",
    pending: "Pendiente",
    preparing: "Preparando",
    ready: "Listo",
    delivered: "Entregado",
    completed: "Completado",
    cancelled: "Cancelado",
    viewOrder: "Ver Pedido",
    updateStatus: "Actualizar Estado",

    // Analytics
    salesOverview: "Resumen de Ventas",
    analyticsPopularItems: "Artículos Populares",
    peakHours: "Horas Pico",
    revenue: "Ingresos",
    dailySales: "Ventas Diarias",
    monthlySales: "Ventas Mensuales",

    // Settings
    generalSettings: "Configuración General",
    restaurantProfile: "Perfil del Restaurante",
    settingsPricingRules: "Reglas de Precios",
    userProfile: "Perfil de Usuario",
    language: "Idioma",
    english: "Inglés",
    spanish: "Español",
    peakHoursAdjustment: "Ajuste de horas pico",
    quietHoursAdjustment: "Ajuste de horas tranquilas",
    restaurantName: "Nombre del Restaurante",
    logoUrl: "URL del Logotipo",
    saveProfile: "Guardar Perfil del Restaurante",
    saving: "Guardando...",
    profileUpdated: "Perfil Actualizado",
    profileUpdateSuccess: "El perfil de su restaurante ha sido actualizado con éxito",

    // Dynamic Pricing
    highTrafficAdjustment: "Ajuste de Tráfico Alto",
    mediumTrafficAdjustment: "Ajuste de Tráfico Medio",
    lowTrafficAdjustment: "Ajuste de Tráfico Bajo",
    applyDynamicPricingTo: "Aplicar Precios Dinámicos A",
    foodItems: "Productos Alimenticios",
    drinkItems: "Bebidas",
    aiConfidenceThreshold: "Umbral de Confianza de IA",

    // Dashboard specific
    dashboardOverview: "Resumen del Panel Principal",
    realTimeInsights: "Información en tiempo real para su negocio",
    refresh: "Actualizar",
    testLowTraffic: "Prueba de Tráfico Bajo",
    testHighTraffic: "Prueba de Tráfico Alto",
    todaysRevenue: "Ingresos de Hoy",
    vsYesterday: "vs ayer",
    highTraffic: "TRÁFICO ALTO",
    mediumTraffic: "TRÁFICO MEDIO",
    lowTraffic: "TRÁFICO BAJO",
    normalTraffic: "TRÁFICO NORMAL",
    on: "ENCENDIDO",
    off: "APAGADO",
    loading: "Cargando",
    confidence: "Confianza",
    analyzingTraffic: "Analizando tráfico",
    enableAiPricing: "Habilitar Precio AI",
    noOrdersToday: "Aún no hay pedidos hoy",
    loadingPopularItems: "Cargando elementos populares",
    loadingOrders: "Cargando pedidos",
    anonymous: "Anónimo",
    noRecentOrders: "No hay pedidos recientes",
    viewReports: "Ver Informes",
    customerExperience: "Experiencia del Cliente",
    manageTables: "Administrar Mesas",
    manageItems: "Administrar Elementos",
    previewMenu: "Vista Previa del Menú",

    // Error handling
    errorLoadingDashboard: "Error cargando el panel principal",
    failedToLoadDashboardData: "Error al cargar los datos del panel principal",
    tryAgain: "Intentar de Nuevo",

    // Notifications
    newOrderReceived: "¡Nuevo pedido recibido!",
    order: "Pedido",
    hasBeenPlaced: "ha sido realizado.",
    savePricingRules: "Guardar Reglas de Precios",
    pricingRulesUpdated: "Reglas de Precios Actualizadas",
    pricingRulesUpdateSuccess: "Las reglas de precios dinámicos se han guardado correctamente",
    highTrafficExplanation: "Durante períodos de tráfico alto (80% o más de capacidad), los precios aumentarán en este porcentaje.",
    mediumTrafficExplanation: "Durante períodos de tráfico medio (50-80% de capacidad), los precios aumentarán en este porcentaje.",
    lowTrafficExplanation: "Durante períodos de tráfico bajo (menos del 30% de capacidad), los precios pueden reducirse para atraer más clientes.",
    confidenceExplanation: "Solo aplicar cambios de precio cuando la IA tenga al menos este nivel de confianza en sus recomendaciones.",

    // Feedback Form
    shareFeedback: "Compartir Comentarios",
    foodQuality: "Calidad de la Comida",
    serviceQuality: "Calidad del Servicio",
    appExperience: "Experiencia de la Aplicación",
    additionalComments: "Comentarios Adicionales",
    tellUsMoreAboutExperience: "Cuéntanos más sobre tu experiencia",
    shareEmailForUpdates: "Compartir correo electrónico para actualizaciones",
    submitFeedback: "Enviar Comentarios",
    skipFeedback: "Omitir Comentarios",
    thankYouForFeedback: "Gracias por tus comentarios",
    errorSubmittingFeedback: "Error al enviar comentarios",
    pleaseTryAgainLater: "Por favor, inténtalo de nuevo más tarde",

    // Notifications Extended
    notifications: "Notificaciones",
    notificationsBellTitle: "Notificaciones",
    unreadNotifications: "notificaciones sin leer",
    allCaughtUp: "¡Todo al día!",
    loadingNotifications: "Cargando notificaciones...",
    unread: "sin leer",
    allNotifications: "Todas las notificaciones",
    markAllRead: "Marcar Todas como Leídas",
    all: "Todas",
    read: "Leídas",
    noNotifications: "Sin notificaciones",
    youreAllCaughtUp: "¡Estás al día! No hay notificaciones sin leer.",
    noReadNotifications: "No hay notificaciones leídas aún.",
    noNotificationsToDisplay: "No hay notificaciones para mostrar.",
    viewAllNotifications: "Ver Todas las Notificaciones",

    // Staff Requests
    staffRequests: "Solicitudes del Personal",
    manageCustomerAssistanceRequests: "Gestionar solicitudes de asistencia de clientes",
    generalAssistance: "Asistencia General",
    tableService: "Servicio de Mesa",
    orderIssue: "Problema con Pedido",
    other: "Otro",
    newStaffRequestReceived: "¡Nueva solicitud del personal recibida!",
    markAsInProgress: "Marcar como En Progreso",
    markAsCompleted: "Marcar como Completado",
    requestMarkedAs: "Solicitud marcada como",
    failedToUpdateRequestStatus: "Error al actualizar el estado de la solicitud",
    noRequestsAvailable: "No hay solicitudes disponibles",
    allRequestsComplete: "¡Todas las solicitudes están completadas!",

    // Customer Sentiment
    customerSentiment: "Sentimiento del Cliente",
    sentimentAnalysis: "Análisis de Sentimiento",
    overallRating: "Calificación General",
    totalFeedback: "Comentarios Totales",
    positive: "Positivo",
    neutral: "Neutral",
    negative: "Negativo",
    recentFeedback: "Comentarios Recientes",
    trends: "Tendencias",
    foodRating: "Calificación de Comida",
    serviceRating: "Calificación de Servicio",
    appRating: "Calificación de Aplicación",
    noFeedbackData: "No hay datos de comentarios disponibles",

    // AI Training Sync
    aiTrainingSync: "Sincronización de Entrenamiento de IA",
    feedbackSyncDashboard: "Panel de Sincronización de Comentarios para Entrenamiento de IA",
    manageFeedbackSync: "Administrar sincronización de comentarios de clientes a base de datos centralizada para entrenamiento de IA",
    connectionStatus: "Estado de Conexión",
    connectedToCentralizedDatabase: "Conectado a base de datos centralizada",
    cannotConnect: "No se puede conectar a la base de datos centralizada",
    testingConnection: "Probando conexión...",
    syncStatus: "Estado de Sincronización",
    localFeedback: "Comentarios Locales",
    needsMigration: "Necesita Migración",
    yes: "Sí",
    no: "No",
    checkStatus: "Verificar Estado",
    testSync: "Probar Sincronización",
    migrateData: "Migrar Datos",
    setupAutoSync: "Configurar Sincronización Automática",
    results: "Resultados",
    migrationResults: "Resultados de Migración",
    migrated: "Migrados",
    failed: "Fallidos",
    details: "Detalles",
    aboutAiTrainingSync: "Acerca de la Sincronización de Entrenamiento de IA",
    startingFeedbackMigration: "Iniciando migración de comentarios...",
    migrationSuccessful: "¡Migración exitosa!",
    recordsSyncedToCentralized: "registros sincronizados a la base de datos centralizada para entrenamiento de IA.",
    migrationFailed: "Migración falló:",
    migrationError: "Error de migración:",
    unknownError: "Error desconocido",
    autoSyncConfigured: "¡Sincronización automática configurada! Los nuevos comentarios se sincronizarán automáticamente a la base de datos centralizada.",
    autoSyncSetupFailed: "Configuración de sincronización automática falló:",
    setupError: "Error de configuración:",
    testingSampleFeedbackSync: "Probando sincronización de comentarios de muestra...",
    sampleSyncSuccessful: "¡Sincronización de muestra exitosa! Los datos de prueba están ahora disponibles para entrenamiento de IA.",
    sampleSyncFailed: "Sincronización de muestra falló:",
    testSyncError: "Error de prueba de sincronización:",

    // Settings - Pricing Rules
    pricingRulesSection: "Reglas de Precios",
    currentDynamicPricingRulesSettings: "Configuración actual de reglas de precios dinámicos",
    enableDynamicPricing: "Habilitar Precios Dinámicos",
    dynamicPricingEnabled: "Los precios dinámicos están habilitados",
    dynamicPricingDisabled: "Los precios dinámicos están deshabilitados",
    foodPricing: "Precios de comida",
    drinksPricing: "Precios de bebidas",
    enabled: "HABILITADO",
    disabled: "DESHABILITADO",
    loadingSettings: "Cargando configuración...",
    noSettingsFound: "No se encontró configuración",
    failedToLoadSettings: "Error al cargar configuración",

     // AdminLayout Descriptions
     manageMenuItemsDescription: "Administrar los elementos del menú de su restaurante",
     manageTablesAndQR: "Administrar las mesas y códigos QR de su restaurante",
     viewManageOrders: "Ver y administrar todos los pedidos de clientes",
     manageRestaurantMenus: "Administrar los menús de su restaurante",
     configureRestaurantSettings: "Configurar las preferencias y ajustes de su restaurante",

     // QR Code Management
     qrCodeManagement: "Gestión de Códigos QR",
     backToDashboard: "Volver al Panel Principal",
     existingTables: "Mesas Existentes",
     newTable: "Nueva Mesa",
     generateQRCodesForTables: "Generar Códigos QR para Mesas Existentes",
     selectTableToGenerate: "Selecciona una mesa para generar o regenerar su código QR",
     loadingTables: "Cargando mesas...",
     noTablesFound: "No se encontraron mesas.",
     createYourFirstTable: "Crear Tu Primera Mesa",
     qrCode: "Código QR",
     download: "Descargar",
     createNewTable: "Crear Nueva Mesa",
     addNewTableAndQR: "Agregar una nueva mesa y generar su código QR",
     tableNumberRequired: "Número de Mesa*",
     numberOfSeats: "Número de Asientos",
     zoneOptional: "Zona (Opcional)",
     creating: "Creando...",
     createTableAndQR: "Crear Mesa y Generar Código QR",
     qrCodeDownload: "Descarga de Código QR",
     qrCodeBeingDownloaded: "Tu código QR se está descargando.",
     downloadFailed: "Descarga Fallida",
     couldNotDownloadQR: "No se pudo descargar el código QR. Por favor, inténtalo de nuevo.",

     // Menu Pages
     allMenu: "Todo el Menú",
     allMenuPage: "Página de Todo el Menú",
     loadingMenuItems: "Cargando elementos del menú...",
     noDrinksAvailable: "No hay bebidas disponibles",
     noFoodItemsAvailable: "No hay elementos de comida disponibles",
     checkBackLater: "Por favor, vuelve más tarde o contacta al restaurante.",
     noMenuItemsAvailable: "No hay elementos de menú disponibles",
     addedToOrder: "Agregado a tu pedido",

     // Enhanced Menu Interface - Recommendations
     chefsPickLabel: "Selección del Chef",
     popularLabel: "Popular",
     recommendedLabel: "Recomendado",
     addToCartButton: "Añadir al Carrito",
     orderedTimes: "pedido {count} veces",
     recommendationsTitle: "Recomendaciones",
     featuredItems: "Elementos Destacados",

     // Enhanced Menu Interface - Cart
     yourOrder: "Tu Pedido",
     yourCartIsEmpty: "Tu carrito está vacío",
     addItemsToStart: "Agrega elementos del menú para comenzar tu pedido",
     specialInstructions: "Instrucciones Especiales",
     specialInstructionsPlaceholder: "ej., \"sin queso\", \"carne bien cocida\", \"extra picante\"...",
     specialInstructionsHint: "Cuéntanos sobre tus preferencias alimentarias",
     subtotal: "Subtotal",
     placeOrder: "Realizar Pedido",
     removeItem: "Eliminar elemento",
     increaseQuantity: "Aumentar cantidad",
     decreaseQuantity: "Disminuir cantidad",

     // Order History
     yourOrders: "Tus Pedidos",
     noOrders: "Aún no hay pedidos",
     noOrdersMessage: "Aún no has realizado ningún pedido. Comienza navegando nuestro delicioso menú!",
     browseMenu: "Navegar Menú",
     activeOrders: "Pedidos Activos",
     pastOrders: "Pedidos Pasados",
     active: "activo",

     // Enhanced Menu Interface - Menu Status
     menuCurrentlyUnavailable: "Menú Actualmente No Disponible",
     chefsPreparingSomething: "Nuestros chefs están preparando algo especial. Por favor vuelve durante nuestro horario regular.",
     liveMenu: "Menú en vivo",
     menuAvailable: "Menú Disponible",

     // Enhanced Menu Interface - Restaurant Status
     dineInAvailable: "Comer en el Local Disponible",
     takeoutAvailable: "Para Llevar Disponible",
     deliveryAvailable: "Entrega a Domicilio Disponible",
     openNow: "Abierto Ahora",
     closedNow: "Cerrado Ahora",
     opensAt: "Abre a las {time}",
     closesAt: "Cierra a las {time}",

     // Dietary Options
     dietaryOptions: "Opciones Dietéticas",
     allergyInformation: "Información de Alergias",
     isVegan: "Vegano",
     isVegetarian: "Vegetariano",
     isGlutenFree: "Sin Gluten",
     isDairyFree: "Sin Lácteos",
     isNutFree: "Sin Frutos Secos",
     spiceLevel: "Nivel de Picante",
     dietaryNotes: "Notas Dietéticas",
     allergens: "Alérgenos",
     mild: "Suave",
     mediumSpice: "Medio",
     spicy: "Picante",
     verySpicy: "Muy Picante",
     extraSpicy: "Extra Picante",
     noSpice: "Sin Picante",
     contains: "Contiene",
     mayContain: "Puede Contener",
     additionalDietaryInfo: "Información dietética adicional",
     selectSpiceLevel: "Seleccionar nivel de picante",
     enterAllergens: "Ingrese alérgenos (separados por comas)",
     allergensHint: "Liste cualquier alérgeno que contenga este artículo, separado por comas (ej: nueces, lácteos, gluten)",

     // Settings Extended
     restaurantLogo: "Logo del Restaurante",
     restaurantAddress: "Dirección del Restaurante",
     enterOpeningHours: "Ingresa información de horarios de apertura",
     briefRestaurantDescription: "Una breve descripción de tu restaurante",
     languageChanged: "Idioma Cambiado",
     languageSetToEnglish: "Language set to English",
     languageSetToSpanish: "Idioma establecido en Español",
     error: "Error",
     mustBeLoggedIn: "Debes iniciar sesión para actualizar el perfil de tu restaurante",
     failedToUpdateProfile: "Error al actualizar el perfil del restaurante",
     mustBeLoggedInPricing: "Debes iniciar sesión para actualizar las reglas de precios",
     pricingRulesUpdatedSuccess: "Las reglas de precios dinámicos se han guardado exitosamente",
     failedToUpdatePricingRules: "Error al actualizar las reglas de precios",

     // Enhanced Visibility Dashboard
     enhancedVisibility: "Visibilidad Mejorada",
     chefRecommendations: "Recomendaciones del Chef",
     enhancedVisibilityDashboard: "Panel de Visibilidad Mejorado",
     manageRestaurantOnlinePresenceDescription: "Gestione la presencia en línea de su restaurante y la visibilidad en plataformas de reseñas, motores de búsqueda y sistemas de IA",
     loadingRestaurantData: "Cargando datos del restaurante...",

     // Chef Recommendations Admin Interface
     manageChefRecommendations: "Gestionar Recomendaciones del Chef",
     manageFeaturedMenuItems: "Gestionar Elementos Destacados del Menú",
     featuredItemsCount: "Elementos Destacados ({count})",
     availableMenuItems: "Elementos de Menú Disponibles",
     priority: "Prioridad",
     category: "Categoría",
     price: "Precio",
     chefNotes: "Notas del Chef",
     actions: "Acciones",
     addChefRecommendation: "Añadir Recomendación del Chef",
     addFirstRecommendation: "Añadir Primera Recomendación",
     selectMenuItem: "Seleccionar un elemento del menú",
     selectMenuItemPlaceholder: "Seleccionar un elemento del menú",
     priorityLabel: "Prioridad (1 = más alta)",
     chefNotesLabel: "Notas del Chef (opcional)",
     chefNotesPlaceholder: "Añadir notas especiales sobre este plato...",
     recommendationType: "Tipo de Recomendación",
     signature: "Especialidad",
     popular: "Popular",
     seasonal: "Estacional",
     special: "Especial",
     loadingChefRecommendations: "Cargando recomendaciones del chef...",
     failedToLoadChefRecommendations: "Error al cargar recomendaciones del chef. Por favor, inténtalo de nuevo.",
     noRecommendationsYet: "Aún no hay recomendaciones",
     startByAddingFeaturedItems: "Comienza añadiendo los elementos destacados de tu chef",
     manageFeaturedItemsDescription: "Gestionar elementos destacados y recomendaciones especiales del chef",
     selectMenuItemAndConfigure: "Selecciona un elemento del menú y configura los ajustes de recomendación",
     unavailable: "No disponible",
     priorityNumber: "Prioridad: {number}",
     featuredVisibleToCustomers: "Destacado (visible para clientes)",
     updateRecommendation: "Actualizar Recomendación",
     addRecommendation: "Añadir Recomendación",
     addChefNotes: "Añadir notas del chef...",
     saveNotes: "Guardar Notas",
     customerFeedback: "Comentarios de Clientes",
     publicReviews: "Reseñas Públicas",
     platformStatus: "Estado de Plataforma",
     boostOnlinePresence: "Impulse la presencia en línea de su restaurante con SME Analytica",
     runSEOAudit: "Ejecutar Auditoría SEO",
     publishedReviews: "Reseñas Publicadas",
     averageRating: "Calificación Promedio",
     seoScore: "Puntuación SEO",
     aiVisibility: "Visibilidad IA",
     fromLastMonth: "desde el mes pasado",
     thisMonth: "este mes",
     aiMentions: "menciones de IA",
     overview: "Resumen",
     customerReviews: "Reseñas de Clientes",
     seoTools: "Herramientas SEO",
     contentHub: "Centro de Contenido",
     platformIntegrations: "Integraciones de Plataforma",
     yourVisibilityAcrossPlatforms: "Su visibilidad en todas las plataformas",
     performanceTrends: "Tendencias de Rendimiento",
     yourVisibilityGrowthOverTime: "Su crecimiento de visibilidad a lo largo del tiempo",
     searchVisibility: "Visibilidad de Búsqueda",
     reviewEngagement: "Participación en Reseñas",
     noPlatformIntegrationsConfiguredYet: "Aún no hay integraciones de plataforma configuradas",
     failedToLoadDashboard: "Error al cargar datos del panel",
     loadingDashboardData: "Cargando datos del panel...",
     retry: "Reintentar",
     seoAuditStarted: "Auditoría SEO Iniciada",
     quickActions: "Acciones Rápidas",
     boostYourVisibilityWithTheseRecommendedActions: "Impulse su visibilidad con estas acciones recomendadas",
     autoPublishReviews: "Auto-Publicar Reseñas",
     shareCustomerFeedbackWithAIAndGoogle: "Compartir comentarios de clientes con IA y Google",
     seoHealthCheck: "Verificación de Salud SEO",
     analyzeAndImproveSearchRankings: "Analizar y mejorar clasificaciones de búsqueda",
     createContent: "Crear Contenido",
     generateAIoptimizedContent: "Generar contenido optimizado para IA",
     reviewSharingSettings: "Configuración de Compartir Reseñas",
     configureHowYourReviewsAreSharedAcrossPlatforms: "Configure cómo se comparten sus reseñas en todas las plataformas",
     autoPublishThreshold: "Umbral de Auto-Publicación",
     moderation: "Moderación",
     required: "Requerida",
     automatic: "Automática",
     reviewApproval: "aprobación de reseñas",
     customerFeedbackReadyToPublish: "Comentarios de Clientes Listos para Publicar",
     recentCustomerFeedbackThatCanBeSharedToBoostYourVisibility: "Comentarios recientes de clientes que se pueden compartir para impulsar su visibilidad",
     autoPublishAll: "Auto-Publicar Todos",
     noEligibleCustomerFeedbackFound: "No se encontraron comentarios de clientes elegibles.",
     customerReviewsWillAppearHereOnceReceived: "Las reseñas de clientes aparecerán aquí una vez recibidas.",
     publish: "Publicar",
     shareWithAI: "Compartir con IA",
     shareWithGoogle: "Compartir con Google",
     noPublishedReviewsYet: "Aún no hay reseñas publicadas.",
     publishedReviewsWillAppearHereForPlatformSharing: "Las reseñas publicadas aparecerán aquí para compartir en plataformas.",
     featured: "Destacado",
     google: "Google",
     social: "Social",
     aiReady: "Listo para IA",
     reviewPublished: "Reseña Publicada",
     customerReviewHasBeenPublishedSuccessfully: "La reseña del cliente se ha publicado exitosamente",
     success: "Éxito",
     seoPerformance: "Rendimiento SEO",
     yourSearchEngineOptimizationStatus: "Su estado de optimización para motores de búsqueda",
     runFullAudit: "Ejecutar Auditoría Completa",
     overallSEOScore: "Puntuación SEO General",
     technicalSEO: "SEO Técnico",
     localSEO: "SEO Local",
     seoRecommendations: "Recomendaciones SEO",
     actionsToImproveYourSearchRanking: "Acciones para mejorar su clasificación de búsqueda",
     runAnSEOAuditToGetPersonalizedRecommendations: "Ejecute una auditoría SEO para obtener recomendaciones personalizadas",
     startSEOAudit: "Iniciar Auditoría SEO",
     structuredDataAndSchema: "Datos Estructurados y Esquema",
     helpSearchEnginesUnderstandYourRestaurant: "Ayude a los motores de búsqueda a entender su restaurante",
     restaurantSchema: "Esquema de Restaurante",
     reviewSchema: "Esquema de Reseñas",
     menuSchema: "Esquema de Menú",
     updateSchemaMarkup: "Actualizar Marcado de Esquema",
     schemaMarkupHasBeenUpdated: "El marcado de esquema se ha actualizado",
     publishedContent: "Contenido Publicado",
     yourLatestContentForAISearchVisibility: "Su último contenido para visibilidad de IA y búsqueda",
     createNew: "Crear Nuevo",
     noPublishedContentYet: "Aún no hay contenido publicado.",
     createContentToImproveYourOnlinePresence: "Cree contenido para mejorar su presencia en línea.",
     engagement: "Participación",
     aiGeneratedContentIdeas: "Ideas de Contenido Generadas por IA",
     smartContentSuggestionsToBoostYourVisibility: "Sugerencias inteligentes de contenido para impulsar su visibilidad",
     noContentIdeasAvailable: "No hay ideas de contenido disponibles.",
     aiWillGenerateIdeasBasedOnYourRestaurantData: "La IA generará ideas basadas en los datos de su restaurante.",
     contentCreated: "Contenido Creado",
     newContentHasBeenCreatedAsADraft: "Se ha creado nuevo contenido como borrador",
     yourComprehensiveSEOAuditIsRunning: "Su auditoría SEO completa está en curso.",
     reviewsCurrentlySharedAcrossPlatformsForAISearchVisibility: "Reseñas actualmente compartidas en plataformas para visibilidad de IA y búsqueda",

     // Subscription & Billing
     subscriptionBilling: "Suscripción y Facturación",
     currentSubscription: "Suscripción Actual",
     billingManagement: "Gestión de Facturación",
     manageBilling: "Gestionar Facturación",
     upgradeToBasic: "Actualizar a Básico",
     upgradeToPremium: "Actualizar a Premium",
     getPremium: "Obtener Premium",
     freeTrialActive: "Prueba Gratuita Activa",
     daysRemaining: "Días Restantes",
     trialEndsIn: "La prueba termina en",
     days: "días",
     subscribeNow: "Suscríbete Ahora",
     continueUsingFeatures: "para continuar usando todas las funciones después de que termine tu prueba.",
     nextBillingDate: "Próxima Fecha de Facturación",
     customerID: "ID de Cliente",
     planFeatures: "Características del Plan",
     restaurant: "Restaurante",
     restaurants: "Restaurantes",
     menuItem: "Elemento del Menú",
     unlockAIPoweredFeatures: "Desbloquea funciones impulsadas por IA y acceso ilimitado",
     securelyManageSubscription: "Gestiona de forma segura tu suscripción a través de Stripe",
     aiDynamicPricing: "Precios Dinámicos IA",
     advancedAnalytics: "Análisis Avanzados",
     unlimitedEverything: "Todo Ilimitado",
     posIntegration: "Integración POS",
     freeTrial: "Prueba Gratuita",
     basic: "Básico",
     premium: "Premium",
     currentPlan: "Plan Actual",
     tryThisPremiumFeature: "¡Prueba esta función premium durante tu prueba!",
  }
};

// Create language context
interface LanguageContextType {
  language: Language;
  setLanguage: (language: Language) => void;
  autoDetectedLanguage: Language | null;
  isLanguageDetected: boolean;
  t: (key: string, params?: Record<string, string | number>) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Create language provider
export const LanguageProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [language, setLanguage] = useState<Language>('en');
  const [autoDetectedLanguage, setAutoDetectedLanguage] = useState<Language | null>(null);
  const [isLanguageDetected, setIsLanguageDetected] = useState(false);

  // Auto-detect language on first load
  useEffect(() => {
    const initializeLanguage = async () => {
      try {
        // Use enhanced language detection service
        const { detectLanguage, storeLanguageAnalytics } = await import('@/services/languageDetectionService');

        const detectionResult = await detectLanguage();

        setLanguage(detectionResult.language);
        setAutoDetectedLanguage(detectionResult.language);
        setIsLanguageDetected(true);

        // Store analytics
        storeLanguageAnalytics(detectionResult);

        // Store the detected language preference
        storeLanguage(detectionResult.language);

      } catch (error) {
        console.error('Language detection failed, using fallback:', error);

        // Fallback to simple browser detection
        const browserLanguage = detectBrowserLanguage();
        setLanguage(browserLanguage);
        setAutoDetectedLanguage(browserLanguage);
        setIsLanguageDetected(true);
        storeLanguage(browserLanguage);
      }
    };

    initializeLanguage();
  }, []);

  // Enhanced setLanguage function that persists the choice
  const handleSetLanguage = (newLanguage: Language) => {
    setLanguage(newLanguage);
    storeLanguage(newLanguage);

    // Log language change for analytics
    console.log('🔄 Language Changed:', {
      from: language,
      to: newLanguage,
      wasAutoDetected: autoDetectedLanguage === language,
      timestamp: new Date().toISOString()
    });
  };

  // Translation function
  const t = (key: string, params?: Record<string, string | number>): string => {
    let translation = translations[language][key] || key;

    // Handle interpolation if params are provided
    if (params) {
      Object.entries(params).forEach(([paramKey, value]) => {
        translation = translation.replace(`{{${paramKey}}}`, String(value));
      });
    }

    return translation;
  };

  return (
    <LanguageContext.Provider value={{
      language,
      setLanguage: handleSetLanguage,
      autoDetectedLanguage,
      isLanguageDetected,
      t
    }}>
      {children}
    </LanguageContext.Provider>
  );
};

// Create hook for using language context
export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
