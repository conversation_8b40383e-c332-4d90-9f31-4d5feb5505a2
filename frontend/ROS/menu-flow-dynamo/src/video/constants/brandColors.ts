// MenuFlow Dynamo & SME Analytica Brand Colors
export const BRAND_COLORS = {
  // Primary brand colors from tailwind config
  primary: '#5f7790',        // SME Analytica buttons
  secondary: '#11a5e8',      // SME Analytica links/accent
  accent: '#11a5e8',         // Links and highlights
  
  // Background and surface colors
  background: '#171f31',     // Main background
  cardBackground: '#202940', // Card backgrounds (slightly lighter)
  
  // Text colors
  textPrimary: '#d5dce2',    // Primary text
  textMuted: '#a4acb6',      // Muted/secondary text
  
  // Status colors
  success: '#10b981',        // Green for success states
  warning: '#f59e0b',        // Orange for warnings
  error: '#ef4444',          // Red for errors
  
  // Gradient combinations
  gradients: {
    primary: `linear-gradient(135deg, #5f7790 0%, #11a5e8 100%)`,
    secondary: `linear-gradient(135deg, #11a5e8 0%, #5f7790 100%)`,
    background: `linear-gradient(135deg, #171f31 0%, #202940 50%, #171f31 100%)`,
    accent: `linear-gradient(135deg, #11a5e8 0%, #5f7790 100%)`,
  },
  
  // Component-specific colors
  qrCode: '#11a5e8',         // QR code accent
  collaborative: '#5f7790',   // Collaborative features
  ai: '#11a5e8',             // AI features highlight
  dashboard: '#202940',      // Dashboard backgrounds
};

// Logo colors (extracted from favicon.svg)
export const LOGO_COLORS = {
  background: '#0ea5e9',     // Close to our #11a5e8 brand secondary
  foreground: '#ffffff',     // White elements
};

// Animation colors
export const ANIMATION_COLORS = {
  pulse: 'rgba(17, 165, 232, 0.3)',    // #11a5e8 with opacity
  glow: 'rgba(95, 119, 144, 0.4)',     // #5f7790 with opacity
  particle: '#11a5e8',                  // Particle effects
  sync: '#5f7790',                      // Sync animations
};