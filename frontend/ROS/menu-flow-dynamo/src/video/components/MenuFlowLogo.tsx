import React from 'react';
import { BRAND_COLORS } from '../constants/brandColors';

interface MenuFlowLogoProps {
  size?: number;
  showText?: boolean;
  animated?: boolean;
}

export const MenuFlowLogo: React.FC<MenuFlowLogoProps> = ({ 
  size = 120, 
  showText = true, 
  animated = false 
}) => {
  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        gap: showText ? '20px' : '0',
      }}
    >
      {/* Logo Icon - Based on favicon.svg but adapted for MenuFlow */}
      <div
        style={{
          width: `${size}px`,
          height: `${size}px`,
          background: BRAND_COLORS.gradients.primary,
          borderRadius: '20px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          boxShadow: `0 ${size/6}px ${size/3}px rgba(17, 165, 232, 0.3)`,
          transform: animated ? 'scale(1.05)' : 'scale(1)',
          transition: 'all 0.3s ease',
        }}
      >
        <svg 
          width={size * 0.6} 
          height={size * 0.6} 
          viewBox="0 0 32 32" 
          fill="none"
        >
          {/* Plate */}
          <circle 
            cx="16" 
            cy="18" 
            r="10" 
            fill="none" 
            stroke="#ffffff" 
            strokeWidth="1.5"
          />
          
          {/* Fork */}
          <path 
            d="M8 8 L8 14 M6 8 L6 12 M10 8 L10 12" 
            stroke="#ffffff" 
            strokeWidth="1.2" 
            strokeLinecap="round"
          />
          
          {/* Knife */}
          <path 
            d="M24 8 L24 16 M23 8 L25 8" 
            stroke="#ffffff" 
            strokeWidth="1.2" 
            strokeLinecap="round"
          />
          
          {/* Food items on plate - Enhanced for MenuFlow */}
          <circle cx="14" cy="17" r="1.5" fill="#ffffff"/>
          <circle cx="18" cy="17" r="1.5" fill="#ffffff"/>
          <circle cx="16" cy="20" r="1" fill="#ffffff"/>
          
          {/* Digital enhancement - QR code pattern */}
          <rect x="12" y="6" width="8" height="1" fill="#ffffff" opacity="0.7"/>
          <rect x="12" y="8" width="8" height="1" fill="#ffffff" opacity="0.5"/>
          <rect x="12" y="10" width="8" height="1" fill="#ffffff" opacity="0.3"/>
        </svg>
      </div>

      {/* Brand Text */}
      {showText && (
        <div style={{ display: 'flex', flexDirection: 'column' }}>
          <div
            style={{
              fontSize: `${size * 0.4}px`,
              fontWeight: 'bold',
              background: BRAND_COLORS.gradients.primary,
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              lineHeight: 1,
              marginBottom: '5px',
            }}
          >
            MenuFlow
          </div>
          <div
            style={{
              fontSize: `${size * 0.15}px`,
              color: BRAND_COLORS.textMuted,
              fontWeight: '600',
              letterSpacing: '2px',
            }}
          >
            DYNAMO
          </div>
        </div>
      )}
    </div>
  );
};