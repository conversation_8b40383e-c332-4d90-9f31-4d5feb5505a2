import React from 'react';
import {
  AbsoluteFill,
  Audio,
  Sequence,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  Easing,
} from 'remotion';
import { BRAND_COLORS } from '../constants/brandColors';
import { OpeningHook } from '../scenes/OpeningHook';
import { ProblemStatement } from '../scenes/ProblemStatement';
import { SolutionIntroduction } from '../scenes/SolutionIntroduction';
import { QRCodeDemo } from '../scenes/QRCodeDemo';
import { CollaborativeCart } from '../scenes/CollaborativeCart';
import { DynamicPricing } from '../scenes/DynamicPricing';
import { AIRecommendations } from '../scenes/AIRecommendations';
import { StaffOperations } from '../scenes/StaffOperations';
import { AdvancedAnalytics } from '../scenes/AdvancedAnalytics';
import { AdminDashboard } from '../scenes/AdminDashboard';
import { CustomerExperience } from '../scenes/CustomerExperience';
import { CallToAction } from '../scenes/CallToAction';

export const MenuFlowExplainer: React.FC = () => {
  const frame = useCurrentFrame();
  const { fps, durationInFrames } = useVideoConfig();

  // Scene durations in seconds and frames
  const scenes = [
    { name: 'Opening Hook', duration: 10, component: OpeningHook },
    { name: 'Problem Statement', duration: 15, component: ProblemStatement },
    { name: 'Solution Introduction', duration: 15, component: SolutionIntroduction },
    { name: 'QR Code Demo', duration: 10, component: QRCodeDemo },
    { name: 'Collaborative Cart', duration: 15, component: CollaborativeCart },
    { name: 'Dynamic Pricing', duration: 20, component: DynamicPricing },
    { name: 'AI Recommendations', duration: 10, component: AIRecommendations },
    { name: 'Staff Operations', duration: 20, component: StaffOperations },
    { name: 'Advanced Analytics', duration: 20, component: AdvancedAnalytics },
    { name: 'Admin Dashboard', duration: 30, component: AdminDashboard },
    { name: 'Customer Experience', duration: 30, component: CustomerExperience },
    { name: 'Call to Action', duration: 75, component: CallToAction },
  ];

  let currentFrame = 0;
  const sequences = scenes.map((scene, index) => {
    const startFrame = currentFrame;
    const durationInFrames = scene.duration * fps;
    currentFrame += durationInFrames;

    return {
      ...scene,
      startFrame,
      durationInFrames,
    };
  });

  // Background gradient animation
  const backgroundOpacity = interpolate(
    frame,
    [0, 30, durationInFrames - 30, durationInFrames],
    [0, 1, 1, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
      easing: Easing.ease,
    }
  );

  return (
    <AbsoluteFill>
      {/* Animated background */}
      <AbsoluteFill
        style={{
          background: `linear-gradient(135deg, 
            ${BRAND_COLORS.background}cc 0%, 
            ${BRAND_COLORS.cardBackground}88 50%, 
            ${BRAND_COLORS.background}cc 100%)`,
          opacity: backgroundOpacity,
        }}
      />

      {/* Render all scenes in sequence */}
      {sequences.map((scene, index) => (
        <Sequence
          key={scene.name}
          from={scene.startFrame}
          durationInFrames={scene.durationInFrames}
        >
          <scene.component />
        </Sequence>
      ))}
    </AbsoluteFill>
  );
};