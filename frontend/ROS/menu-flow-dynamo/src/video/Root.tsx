import React from 'react';
import { Composition } from 'remotion';
import { MenuFlowExplainer } from './compositions/MenuFlowExplainer';

export const RemotionRoot: React.FC = () => {
  return (
    <>
      <Composition
        id="MenuFlowExplainer"
        component={MenuFlowExplainer}
        durationInFrames={7500} // 4 minutes 10 seconds at 30fps
        fps={30}
        width={1920}
        height={1080}
        defaultProps={{}}
      />
    </>
  );
};