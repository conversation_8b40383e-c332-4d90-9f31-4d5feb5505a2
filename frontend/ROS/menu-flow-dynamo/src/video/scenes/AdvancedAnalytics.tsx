import React from 'react';
import {
  AbsoluteFill,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  Easing,
  spring,
} from 'remotion';
import { BRAND_COLORS } from '../constants/brandColors';

export const AdvancedAnalytics: React.FC = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  // Analytics features
  const analyticsFeatures = [
    {
      title: 'Customer Feedback System',
      icon: '⭐',
      description: 'Real-time customer satisfaction tracking',
      delay: 60,
      metric: '4.8/5',
      color: BRAND_COLORS.success
    },
    {
      title: 'Performance Analytics',
      icon: '📊',
      description: 'Automated performance monitoring and alerts',
      delay: 120,
      metric: '+35%',
      color: BRAND_COLORS.secondary
    },
    {
      title: 'POS Integration',
      icon: '💳',
      description: 'Seamless integration with existing systems',
      delay: 180,
      metric: '99.9%',
      color: BRAND_COLORS.primary
    },
    {
      title: 'Enhanced Visibility',
      icon: '👁️',
      description: 'Complete operational transparency',
      delay: 240,
      metric: 'Real-time',
      color: BRAND_COLORS.accent
    },
  ];

  // Chart data animation
  const chartProgress = interpolate(
    frame,
    [300, 480],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
      easing: Easing.ease,
    }
  );

  return (
    <AbsoluteFill
      style={{
        background: BRAND_COLORS.gradients.background,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        color: BRAND_COLORS.textPrimary,
        position: 'relative',
        overflow: 'hidden',
        padding: '60px',
      }}
    >
      {/* Background elements */}
      <div
        style={{
          position: 'absolute',
          inset: 0,
          backgroundImage: `
            radial-gradient(circle at 20% 20%, rgba(95, 119, 144, 0.15) 0%, transparent 50%),
            radial-gradient(circle at 80% 80%, rgba(17, 165, 232, 0.15) 0%, transparent 50%)
          `,
        }}
      />

      {/* Title */}
      <div
        style={{
          fontSize: '48px',
          fontWeight: 'bold',
          marginBottom: '60px',
          textAlign: 'center',
          background: BRAND_COLORS.gradients.primary,
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
        }}
      >
        Advanced Business Intelligence
      </div>

      {/* Main dashboard mockup */}
      <div
        style={{
          width: '100%',
          maxWidth: '1400px',
          background: BRAND_COLORS.cardBackground,
          borderRadius: '20px',
          border: `2px solid ${BRAND_COLORS.primary}`,
          padding: '40px',
          marginBottom: '60px',
          boxShadow: `0 25px 50px rgba(17, 165, 232, 0.2)`,
        }}
      >
        {/* Dashboard header */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'between',
            marginBottom: '40px',
          }}
        >
          <div>
            <div style={{ fontSize: '28px', fontWeight: 'bold', color: BRAND_COLORS.textPrimary }}>
              Business Intelligence Dashboard
            </div>
            <div style={{ fontSize: '16px', color: BRAND_COLORS.textMuted }}>
              Powered by SME Analytica Platform
            </div>
          </div>
          
          <div
            style={{
              background: BRAND_COLORS.success,
              color: 'white',
              padding: '10px 20px',
              borderRadius: '20px',
              fontSize: '14px',
              fontWeight: '600',
            }}
          >
            🟢 LIVE DATA
          </div>
        </div>

        {/* Analytics grid */}
        <div
          style={{
            display: 'grid',
            gridTemplate: '1fr 1fr / 1fr 1fr',
            gap: '30px',
            marginBottom: '40px',
          }}
        >
          {analyticsFeatures.map((feature, index) => {
            const cardOpacity = interpolate(
              frame,
              [feature.delay, feature.delay + 30],
              [0, 1],
              {
                extrapolateLeft: 'clamp',
                extrapolateRight: 'clamp',
                easing: Easing.ease,
              }
            );

            const cardScale = spring({
              frame: frame - feature.delay,
              fps,
              config: {
                damping: 200,
                stiffness: 100,
              },
            });

            return (
              <div
                key={index}
                style={{
                  opacity: cardOpacity,
                  transform: `scale(${cardScale})`,
                  background: BRAND_COLORS.background,
                  borderRadius: '16px',
                  border: `2px solid ${feature.color}`,
                  padding: '25px',
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '15px',
                }}
              >
                {/* Feature header */}
                <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
                  <div
                    style={{
                      width: '50px',
                      height: '50px',
                      background: feature.color,
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '24px',
                    }}
                  >
                    {feature.icon}
                  </div>
                  <div>
                    <div style={{ fontSize: '18px', fontWeight: 'bold', color: BRAND_COLORS.textPrimary }}>
                      {feature.title}
                    </div>
                    <div style={{ fontSize: '14px', color: BRAND_COLORS.textMuted }}>
                      {feature.description}
                    </div>
                  </div>
                </div>

                {/* Metric display */}
                <div
                  style={{
                    fontSize: '32px',
                    fontWeight: 'bold',
                    color: feature.color,
                    textAlign: 'center',
                    padding: '20px',
                    background: `rgba(17, 165, 232, 0.1)`,
                    borderRadius: '12px',
                  }}
                >
                  {feature.metric}
                </div>

                {/* Mini chart representation */}
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'end',
                    gap: '4px',
                    height: '40px',
                  }}
                >
                  {[...Array(8)].map((_, i) => (
                    <div
                      key={i}
                      style={{
                        flex: 1,
                        height: `${30 + Math.random() * 40}%`,
                        background: `linear-gradient(to top, ${feature.color}, transparent)`,
                        borderRadius: '2px',
                        opacity: cardOpacity,
                      }}
                    />
                  ))}
                </div>
              </div>
            );
          })}
        </div>

        {/* Revenue impact chart */}
        <div
          style={{
            background: BRAND_COLORS.background,
            borderRadius: '16px',
            border: `2px solid ${BRAND_COLORS.secondary}`,
            padding: '30px',
            opacity: interpolate(
              frame,
              [300, 360],
              [0, 1],
              {
                extrapolateLeft: 'clamp',
                extrapolateRight: 'clamp',
                easing: Easing.ease,
              }
            ),
          }}
        >
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'between',
              marginBottom: '30px',
            }}
          >
            <div>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: BRAND_COLORS.textPrimary }}>
                Revenue Impact Analysis
              </div>
              <div style={{ fontSize: '16px', color: BRAND_COLORS.textMuted }}>
                Monthly performance comparison
              </div>
            </div>
            
            <div
              style={{
                background: BRAND_COLORS.success,
                color: 'white',
                padding: '15px 25px',
                borderRadius: '25px',
                fontSize: '20px',
                fontWeight: 'bold',
              }}
            >
              +47.3% Revenue Growth
            </div>
          </div>

          {/* Chart bars */}
          <div
            style={{
              display: 'flex',
              alignItems: 'end',
              gap: '12px',
              height: '120px',
              padding: '20px 0',
            }}
          >
            {/* Before MenuFlow */}
            <div style={{ flex: 1, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <div
                style={{
                  width: '100%',
                  height: `${60 * chartProgress}%`,
                  background: 'linear-gradient(to top, #6b7280, #9ca3af)',
                  borderRadius: '8px 8px 0 0',
                  marginBottom: '10px',
                }}
              />
              <div style={{ fontSize: '14px', color: BRAND_COLORS.textMuted, textAlign: 'center' }}>
                Before<br />MenuFlow
              </div>
            </div>

            {/* After MenuFlow */}
            <div style={{ flex: 1, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <div
                style={{
                  width: '100%',
                  height: `${100 * chartProgress}%`,
                  background: BRAND_COLORS.gradients.primary,
                  borderRadius: '8px 8px 0 0',
                  marginBottom: '10px',
                  boxShadow: `0 0 20px rgba(17, 165, 232, 0.4)`,
                }}
              />
              <div style={{ fontSize: '14px', color: BRAND_COLORS.textPrimary, textAlign: 'center', fontWeight: 'bold' }}>
                With<br />MenuFlow
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Feature highlights */}
      <div
        style={{
          display: 'flex',
          gap: '60px',
          justifyContent: 'center',
          fontSize: '18px',
          fontWeight: '600',
          color: BRAND_COLORS.textMuted,
          opacity: interpolate(
            frame,
            [420, 480],
            [0, 1],
            {
              extrapolateLeft: 'clamp',
              extrapolateRight: 'clamp',
              easing: Easing.ease,
            }
          ),
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
          <div style={{ color: BRAND_COLORS.success, fontSize: '20px' }}>📈</div>
          Advanced Analytics
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
          <div style={{ color: BRAND_COLORS.secondary, fontSize: '20px' }}>🔄</div>
          Real-time Sync
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
          <div style={{ color: BRAND_COLORS.primary, fontSize: '20px' }}>🎯</div>
          Predictive Insights
        </div>
      </div>
    </AbsoluteFill>
  );
};