import React from 'react';
import {
  AbsoluteFill,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  Easing,
  spring,
} from 'remotion';

export const CustomerExperience: React.FC = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  // Customer journey steps
  const steps = [
    { title: 'Scan QR Code', icon: '📱', delay: 60, description: 'Instant access to digital menu' },
    { title: 'Browse & Order', icon: '🍽️', delay: 180, description: 'Collaborative ordering with friends' },
    { title: 'Track Status', icon: '⏱️', delay: 300, description: 'Real-time order updates' },
    { title: 'Enjoy & Pay', icon: '😊', delay: 420, description: 'Seamless payment experience' },
  ];

  // Customer satisfaction metrics
  const metrics = [
    { label: 'Faster Service', value: '40%', delay: 540 },
    { label: 'Order Accuracy', value: '99.2%', delay: 600 },
    { label: 'Customer Satisfaction', value: '95%', delay: 660 },
  ];

  return (
    <AbsoluteFill
      style={{
        background: 'linear-gradient(135deg, #1e293b 0%, #0f172a 100%)',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white',
        position: 'relative',
        overflow: 'hidden',
        padding: '80px',
      }}
    >
      {/* Background elements */}
      <div
        style={{
          position: 'absolute',
          inset: 0,
          backgroundImage: `
            radial-gradient(circle at 25% 25%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(59, 130, 246, 0.1) 0%, transparent 50%)
          `,
        }}
      />

      {/* Title */}
      <div
        style={{
          fontSize: '56px',
          fontWeight: 'bold',
          marginBottom: '80px',
          textAlign: 'center',
          background: 'linear-gradient(135deg, #10b981 0%, #3b82f6 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
        }}
      >
        Pure Magic for Customers
      </div>

      {/* Customer journey */}
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '60px',
          marginBottom: '100px',
          position: 'relative',
        }}
      >
        {steps.map((step, index) => {
          const stepOpacity = interpolate(
            frame,
            [step.delay, step.delay + 30],
            [0, 1],
            {
              extrapolateLeft: 'clamp',
              extrapolateRight: 'clamp',
              easing: Easing.ease,
            }
          );

          const stepScale = spring({
            frame: frame - step.delay,
            fps,
            config: {
              damping: 200,
              stiffness: 100,
            },
          });

          return (
            <React.Fragment key={index}>
              {/* Step */}
              <div
                style={{
                  opacity: stepOpacity,
                  transform: `scale(${stepScale})`,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  gap: '20px',
                  maxWidth: '200px',
                }}
              >
                {/* Step number */}
                <div
                  style={{
                    width: '60px',
                    height: '60px',
                    background: 'linear-gradient(135deg, #10b981 0%, #3b82f6 100%)',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '24px',
                    fontWeight: 'bold',
                    color: 'white',
                    marginBottom: '10px',
                  }}
                >
                  {index + 1}
                </div>

                {/* Icon */}
                <div style={{ fontSize: '80px', marginBottom: '10px' }}>
                  {step.icon}
                </div>

                {/* Title */}
                <div
                  style={{
                    fontSize: '24px',
                    fontWeight: 'bold',
                    textAlign: 'center',
                    marginBottom: '10px',
                  }}
                >
                  {step.title}
                </div>

                {/* Description */}
                <div
                  style={{
                    fontSize: '16px',
                    color: '#94a3b8',
                    textAlign: 'center',
                    lineHeight: 1.4,
                  }}
                >
                  {step.description}
                </div>
              </div>

              {/* Arrow between steps */}
              {index < steps.length - 1 && (
                <div
                  style={{
                    fontSize: '40px',
                    color: '#10b981',
                    opacity: interpolate(
                      frame,
                      [step.delay + 60, step.delay + 90],
                      [0, 1],
                      {
                        extrapolateLeft: 'clamp',
                        extrapolateRight: 'clamp',
                        easing: Easing.ease,
                      }
                    ),
                  }}
                >
                  →
                </div>
              )}
            </React.Fragment>
          );
        })}
      </div>

      {/* Results metrics */}
      <div
        style={{
          background: 'rgba(30, 41, 59, 0.8)',
          borderRadius: '20px',
          border: '2px solid rgba(59, 130, 246, 0.3)',
          backdropFilter: 'blur(10px)',
          padding: '60px',
          width: '100%',
          maxWidth: '1000px',
        }}
      >
        <div
          style={{
            fontSize: '36px',
            fontWeight: 'bold',
            textAlign: 'center',
            marginBottom: '50px',
            color: '#3b82f6',
          }}
        >
          The Results Speak for Themselves
        </div>

        <div
          style={{
            display: 'flex',
            justifyContent: 'around',
            alignItems: 'center',
          }}
        >
          {metrics.map((metric, index) => {
            const metricOpacity = interpolate(
              frame,
              [metric.delay, metric.delay + 30],
              [0, 1],
              {
                extrapolateLeft: 'clamp',
                extrapolateRight: 'clamp',
                easing: Easing.ease,
              }
            );

            const metricScale = spring({
              frame: frame - metric.delay,
              fps,
              config: {
                damping: 200,
                stiffness: 100,
              },
            });

            const numberAnimation = interpolate(
              frame,
              [metric.delay + 30, metric.delay + 90],
              [0, 1],
              {
                extrapolateLeft: 'clamp',
                extrapolateRight: 'clamp',
                easing: Easing.ease,
              }
            );

            return (
              <div
                key={index}
                style={{
                  opacity: metricOpacity,
                  transform: `scale(${metricScale})`,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  gap: '15px',
                  textAlign: 'center',
                }}
              >
                {/* Metric value */}
                <div
                  style={{
                    fontSize: '72px',
                    fontWeight: 'bold',
                    background: 'linear-gradient(135deg, #10b981 0%, #3b82f6 100%)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    opacity: numberAnimation,
                  }}
                >
                  {metric.value}
                </div>

                {/* Metric label */}
                <div
                  style={{
                    fontSize: '20px',
                    fontWeight: '600',
                    color: '#94a3b8',
                    maxWidth: '200px',
                  }}
                >
                  {metric.label}
                </div>

                {/* Decorative element */}
                <div
                  style={{
                    width: '60px',
                    height: '4px',
                    background: 'linear-gradient(90deg, #10b981 0%, #3b82f6 100%)',
                    borderRadius: '2px',
                    opacity: numberAnimation,
                  }}
                />
              </div>
            );
          })}
        </div>
      </div>

      {/* Bottom tagline */}
      <div
        style={{
          position: 'absolute',
          bottom: '40px',
          fontSize: '28px',
          fontWeight: '600',
          color: '#10b981',
          textAlign: 'center',
          opacity: interpolate(
            frame,
            [720, 780],
            [0, 1],
            {
              extrapolateLeft: 'clamp',
              extrapolateRight: 'clamp',
              easing: Easing.ease,
            }
          ),
        }}
      >
        Faster Service • Accurate Orders • Happy Customers
      </div>
    </AbsoluteFill>
  );
};