import React from 'react';
import {
  AbsoluteFill,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  Easing,
  spring,
} from 'remotion';

export const ProblemStatement: React.FC = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  // Problem icons animation
  const problems = [
    { icon: '⏰', text: 'Long Wait Times', delay: 0 },
    { icon: '❌', text: 'Order Mistakes', delay: 60 },
    { icon: '🌐', text: 'Language Barriers', delay: 120 },
    { icon: '😰', text: 'Overwhelmed Staff', delay: 180 },
  ];

  const titleOpacity = interpolate(
    frame,
    [0, 30],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
      easing: Easing.ease,
    }
  );

  return (
    <AbsoluteFill
      style={{
        background: 'linear-gradient(135deg, #1f2937 0%, #111827 100%)',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white',
        padding: '100px',
      }}
    >
      {/* Title */}
      <div
        style={{
          opacity: titleOpacity,
          fontSize: '72px',
          fontWeight: 'bold',
          marginBottom: '80px',
          textAlign: 'center',
          color: '#ef4444',
        }}
      >
        Traditional Ordering is Broken
      </div>

      {/* Problems grid */}
      <div
        style={{
          display: 'grid',
          gridTemplate: '2fr 2fr / 2fr 2fr',
          gap: '60px',
          width: '100%',
          maxWidth: '800px',
        }}
      >
        {problems.map((problem, index) => {
          const opacity = interpolate(
            frame,
            [problem.delay, problem.delay + 30],
            [0, 1],
            {
              extrapolateLeft: 'clamp',
              extrapolateRight: 'clamp',
              easing: Easing.ease,
            }
          );

          const scale = spring({
            frame: frame - problem.delay,
            fps,
            config: {
              damping: 200,
              stiffness: 100,
            },
          });

          return (
            <div
              key={index}
              style={{
                opacity,
                transform: `scale(${scale})`,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                padding: '40px',
                background: 'rgba(239, 68, 68, 0.1)',
                borderRadius: '20px',
                border: '2px solid rgba(239, 68, 68, 0.3)',
                textAlign: 'center',
              }}
            >
              <div style={{ fontSize: '80px', marginBottom: '20px' }}>
                {problem.icon}
              </div>
              <div style={{ fontSize: '28px', fontWeight: '600' }}>
                {problem.text}
              </div>
            </div>
          );
        })}
      </div>

      {/* Bottom text */}
      <div
        style={{
          opacity: interpolate(
            frame,
            [240, 300],
            [0, 1],
            {
              extrapolateLeft: 'clamp',
              extrapolateRight: 'clamp',
              easing: Easing.ease,
            }
          ),
          fontSize: '36px',
          textAlign: 'center',
          marginTop: '80px',
          color: '#10b981',
          fontWeight: '600',
        }}
      >
        But what if there was a better way?
      </div>

      {/* Animated dots */}
      <div
        style={{
          position: 'absolute',
          top: '20%',
          right: '10%',
          display: 'flex',
          gap: '10px',
        }}
      >
        {[...Array(3)].map((_, i) => (
          <div
            key={i}
            style={{
              width: '12px',
              height: '12px',
              backgroundColor: '#3b82f6',
              borderRadius: '50%',
              opacity: interpolate(
                frame,
                [i * 20, i * 20 + 60, i * 20 + 120],
                [0.3, 1, 0.3],
                {
                  extrapolateLeft: 'clamp',
                  extrapolateRight: 'clamp',
                }
              ),
            }}
          />
        ))}
      </div>
    </AbsoluteFill>
  );
};