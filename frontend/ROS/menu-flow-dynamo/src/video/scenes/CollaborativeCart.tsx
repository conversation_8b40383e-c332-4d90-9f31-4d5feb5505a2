import React from 'react';
import {
  AbsoluteFill,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  Easing,
  spring,
} from 'remotion';

export const CollaborativeCart: React.FC = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  // Multiple phones animation
  const phones = [
    { id: 'phone1', delay: 0, name: '<PERSON>', color: '#ef4444' },
    { id: 'phone2', delay: 60, name: '<PERSON>', color: '#10b981' },
    { id: 'phone3', delay: 120, name: '<PERSON>', color: '#3b82f6' },
  ];

  const syncAnimation = interpolate(
    frame,
    [180, 240, 300, 360],
    [0, 1, 0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
      easing: Easing.ease,
    }
  );

  return (
    <AbsoluteFill
      style={{
        background: 'linear-gradient(135deg, #1e293b 0%, #0f172a 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Background animation */}
      <div
        style={{
          position: 'absolute',
          inset: 0,
          background: `radial-gradient(circle at 50% 50%, rgba(59, 130, 246, ${syncAnimation * 0.1}) 0%, transparent 70%)`,
        }}
      />

      {/* Title */}
      <div
        style={{
          position: 'absolute',
          top: '80px',
          fontSize: '48px',
          fontWeight: 'bold',
          color: '#3b82f6',
          textAlign: 'center',
        }}
      >
        Smart Collaborative Ordering
      </div>

      {/* Phones arrangement */}
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '60px',
          position: 'relative',
        }}
      >
        {phones.map((phone, index) => {
          const phoneOpacity = interpolate(
            frame,
            [phone.delay, phone.delay + 30],
            [0, 1],
            {
              extrapolateLeft: 'clamp',
              extrapolateRight: 'clamp',
              easing: Easing.ease,
            }
          );

          const phoneScale = spring({
            frame: frame - phone.delay,
            fps,
            config: {
              damping: 200,
              stiffness: 100,
            },
          });

          return (
            <div
              key={phone.id}
              style={{
                opacity: phoneOpacity,
                transform: `scale(${phoneScale})`,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: '20px',
              }}
            >
              {/* User name */}
              <div
                style={{
                  background: phone.color,
                  color: 'white',
                  padding: '10px 20px',
                  borderRadius: '20px',
                  fontSize: '18px',
                  fontWeight: '600',
                }}
              >
                {phone.name}
              </div>

              {/* Phone */}
              <div
                style={{
                  width: '250px',
                  height: '500px',
                  background: '#1f2937',
                  borderRadius: '30px',
                  padding: '15px',
                  border: '6px solid #374151',
                  boxShadow: `0 20px 40px rgba(0, 0, 0, 0.3), 0 0 0 2px ${phone.color}`,
                }}
              >
                <div
                  style={{
                    width: '100%',
                    height: '100%',
                    background: 'linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%)',
                    borderRadius: '22px',
                    display: 'flex',
                    flexDirection: 'column',
                    padding: '20px 15px',
                    position: 'relative',
                    overflow: 'hidden',
                  }}
                >
                  {/* Header */}
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'between',
                      marginBottom: '20px',
                    }}
                  >
                    <div
                      style={{
                        fontSize: '20px',
                        fontWeight: 'bold',
                        color: '#1f2937',
                      }}
                    >
                      Shared Cart
                    </div>
                    <div
                      style={{
                        background: phone.color,
                        color: 'white',
                        width: '30px',
                        height: '30px',
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '12px',
                        fontWeight: 'bold',
                      }}
                    >
                      3
                    </div>
                  </div>

                  {/* Participants */}
                  <div
                    style={{
                      display: 'flex',
                      gap: '10px',
                      marginBottom: '20px',
                    }}
                  >
                    {phones.map((p, i) => (
                      <div
                        key={p.id}
                        style={{
                          width: '40px',
                          height: '40px',
                          background: p.color,
                          borderRadius: '50%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          color: 'white',
                          fontSize: '12px',
                          fontWeight: 'bold',
                          opacity: i <= index ? 1 : 0.3,
                          border: p.id === phone.id ? '2px solid #1f2937' : 'none',
                        }}
                      >
                        {p.name[0]}
                      </div>
                    ))}
                  </div>

                  {/* Cart items */}
                  <div style={{ flex: 1, overflow: 'hidden' }}>
                    {[
                      { name: 'Caesar Salad', price: 12.99, user: 'Sarah', color: '#ef4444', show: frame >= 60 },
                      { name: 'Burger & Fries', price: 16.99, user: 'Mike', color: '#10b981', show: frame >= 180 },
                      { name: 'Pasta Alfredo', price: 18.99, user: 'Emma', color: '#3b82f6', show: frame >= 300 },
                    ].map((item, i) => (
                      <div
                        key={i}
                        style={{
                          background: 'white',
                          borderRadius: '12px',
                          padding: '15px',
                          marginBottom: '10px',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '10px',
                          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
                          border: `2px solid ${item.color}`,
                          opacity: item.show ? 1 : 0.3,
                          transform: item.show ? 'scale(1)' : 'scale(0.9)',
                          transition: 'all 0.3s ease',
                        }}
                      >
                        <div
                          style={{
                            width: '8px',
                            height: '40px',
                            background: item.color,
                            borderRadius: '4px',
                          }}
                        />
                        <div style={{ flex: 1 }}>
                          <div
                            style={{
                              fontSize: '14px',
                              fontWeight: '600',
                              color: '#1f2937',
                              marginBottom: '2px',
                            }}
                          >
                            {item.name}
                          </div>
                          <div
                            style={{
                              fontSize: '12px',
                              color: '#64748b',
                            }}
                          >
                            Added by {item.user}
                          </div>
                        </div>
                        <div
                          style={{
                            fontSize: '16px',
                            fontWeight: 'bold',
                            color: '#10b981',
                          }}
                        >
                          ${item.price}
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Total */}
                  <div
                    style={{
                      background: '#1f2937',
                      color: 'white',
                      borderRadius: '12px',
                      padding: '15px',
                      display: 'flex',
                      justifyContent: 'between',
                      alignItems: 'center',
                    }}
                  >
                    <div style={{ fontSize: '16px', fontWeight: '600' }}>
                      Total
                    </div>
                    <div style={{ fontSize: '18px', fontWeight: 'bold' }}>
                      $48.97
                    </div>
                  </div>

                  {/* Sync indicator */}
                  {syncAnimation > 0 && (
                    <div
                      style={{
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        background: 'rgba(59, 130, 246, 0.95)',
                        color: 'white',
                        borderRadius: '50%',
                        width: '80px',
                        height: '80px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '30px',
                        opacity: syncAnimation,
                        animation: 'pulse 1s ease-in-out infinite',
                      }}
                    >
                      🔄
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}

        {/* Sync lines */}
        <svg
          style={{
            position: 'absolute',
            inset: 0,
            pointerEvents: 'none',
            zIndex: -1,
          }}
          width="100%"
          height="100%"
        >
          {/* Connection lines between phones */}
          <defs>
            <linearGradient id="syncGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#3b82f6" stopOpacity={syncAnimation} />
              <stop offset="50%" stopColor="#10b981" stopOpacity={syncAnimation} />
              <stop offset="100%" stopColor="#ef4444" stopOpacity={syncAnimation} />
            </linearGradient>
          </defs>
          
          <line
            x1="30%"
            y1="60%"
            x2="50%"
            y2="60%"
            stroke="url(#syncGradient)"
            strokeWidth="4"
            strokeDasharray="10,5"
            opacity={frame >= 180 ? 1 : 0}
          />
          <line
            x1="50%"
            y1="60%"
            x2="70%"
            y2="60%"
            stroke="url(#syncGradient)"
            strokeWidth="4"
            strokeDasharray="10,5"
            opacity={frame >= 300 ? 1 : 0}
          />
        </svg>
      </div>

      {/* Bottom features */}
      <div
        style={{
          position: 'absolute',
          bottom: '60px',
          display: 'flex',
          gap: '60px',
          fontSize: '20px',
          color: '#94a3b8',
          fontWeight: '600',
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
          <div style={{ color: '#10b981', fontSize: '24px' }}>⚡</div>
          Real-time Sync
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
          <div style={{ color: '#3b82f6', fontSize: '24px' }}>👥</div>
          Multiple Users
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
          <div style={{ color: '#f59e0b', fontSize: '24px' }}>💰</div>
          Smart Bill Split
        </div>
      </div>
    </AbsoluteFill>
  );
};