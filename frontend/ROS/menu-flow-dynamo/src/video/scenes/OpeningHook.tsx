import React from 'react';
import {
  AbsoluteFill,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  Easing,
  spring,
} from 'remotion';
import { BRAND_COLORS } from '../constants/brandColors';

export const OpeningHook: React.FC = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  // Split screen animation
  const splitProgress = spring({
    frame,
    fps,
    config: {
      damping: 200,
      stiffness: 100,
      mass: 1,
    },
  });

  // Statistics animation
  const statsOpacity = interpolate(
    frame,
    [120, 180],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
      easing: Easing.ease,
    }
  );

  const statsScale = spring({
    frame: frame - 120,
    fps,
    config: {
      damping: 200,
      stiffness: 100,
    },
  });

  return (
    <AbsoluteFill>
      {/* Split screen container */}
      <div
        style={{
          display: 'flex',
          width: '100%',
          height: '100%',
          transform: `scale(${splitProgress})`,
        }}
      >
        {/* Left side - Frustrated customers */}
        <div
          style={{
            flex: 1,
            background: `linear-gradient(135deg, ${BRAND_COLORS.error} 0%, #dc2626 100%)`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            position: 'relative',
            overflow: 'hidden',
          }}
        >
          {/* Animated queue of people */}
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: '20px',
            }}
          >
            {[...Array(4)].map((_, i) => (
              <div
                key={i}
                style={{
                  width: '60px',
                  height: '60px',
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '24px',
                  transform: `translateY(${interpolate(
                    frame,
                    [0, 60, 120, 180],
                    [0, -5, 0, -5],
                    {
                      extrapolateLeft: 'clamp',
                      extrapolateRight: 'clamp',
                    }
                  )}px)`,
                  animation: `bounce ${1 + i * 0.2}s ease-in-out infinite`,
                }}
              >
                😤
              </div>
            ))}
          </div>
          
          {/* Overlay text */}
          <div
            style={{
              position: 'absolute',
              bottom: '50px',
              left: '50%',
              transform: 'translateX(-50%)',
              color: 'white',
              fontSize: '32px',
              fontWeight: 'bold',
              textAlign: 'center',
              textShadow: '2px 2px 4px rgba(0,0,0,0.5)',
            }}
          >
            Long Wait Times
          </div>
        </div>

        {/* Right side - Happy customers with QR */}
        <div
          style={{
            flex: 1,
            background: BRAND_COLORS.gradients.primary,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            position: 'relative',
            overflow: 'hidden',
          }}
        >
          {/* Phone with QR code */}
          <div
            style={{
              width: '200px',
              height: '350px',
              backgroundColor: BRAND_COLORS.background,
              borderRadius: '20px',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '20px',
              border: `4px solid ${BRAND_COLORS.cardBackground}`,
              transform: `scale(${interpolate(
                frame,
                [60, 120],
                [0.8, 1],
                {
                  extrapolateLeft: 'clamp',
                  extrapolateRight: 'clamp',
                  easing: Easing.ease,
                }
              )})`,
            }}
          >
            {/* QR Code simulation */}
            <div
              style={{
                width: '120px',
                height: '120px',
                backgroundColor: 'white',
                display: 'grid',
                gridTemplate: 'repeat(8, 1fr) / repeat(8, 1fr)',
                gap: '2px',
                padding: '10px',
                borderRadius: '8px',
              }}
            >
              {[...Array(64)].map((_, i) => (
                <div
                  key={i}
                  style={{
                    backgroundColor: Math.random() > 0.5 ? '#000' : '#fff',
                    borderRadius: '1px',
                  }}
                />
              ))}
            </div>
            
            {/* Happy emoji */}
            <div style={{ fontSize: '40px' }}>😊</div>
          </div>

          {/* Overlay text */}
          <div
            style={{
              position: 'absolute',
              bottom: '50px',
              left: '50%',
              transform: 'translateX(-50%)',
              color: 'white',
              fontSize: '32px',
              fontWeight: 'bold',
              textAlign: 'center',
              textShadow: '2px 2px 4px rgba(0,0,0,0.5)',
            }}
          >
            Instant QR Ordering
          </div>
        </div>
      </div>

      {/* Statistics overlay */}
      <div
        style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: `translate(-50%, -50%) scale(${statsScale})`,
          opacity: statsOpacity,
          background: `rgba(23, 31, 49, 0.9)`,
          padding: '40px 60px',
          borderRadius: '20px',
          border: `2px solid ${BRAND_COLORS.secondary}`,
          textAlign: 'center',
        }}
      >
        <div
          style={{
            color: BRAND_COLORS.secondary,
            fontSize: '64px',
            fontWeight: 'bold',
            marginBottom: '10px',
          }}
        >
          73%
        </div>
        <div
          style={{
            color: BRAND_COLORS.textPrimary,
            fontSize: '24px',
            marginBottom: '20px',
          }}
        >
          of customers prefer phone ordering
        </div>
        <div
          style={{
            color: BRAND_COLORS.primary,
            fontSize: '64px',
            fontWeight: 'bold',
            marginBottom: '10px',
          }}
        >
          40%
        </div>
        <div
          style={{
            color: BRAND_COLORS.textPrimary,
            fontSize: '24px',
          }}
        >
          faster table turnover
        </div>
      </div>
    </AbsoluteFill>
  );
};