import React from 'react';
import {
  AbsoluteFill,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  Easing,
  spring,
} from 'remotion';

export const AdminDashboard: React.FC = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  // Dashboard sections animation
  const sections = [
    { title: 'Real-time Orders', delay: 60, icon: '📋' },
    { title: 'Revenue Analytics', delay: 120, icon: '💰' },
    { title: 'Table Management', delay: 180, icon: '🪑' },
    { title: 'Menu Controls', delay: 240, icon: '📄' },
  ];

  return (
    <AbsoluteFill
      style={{
        background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%)',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        color: 'white',
        position: 'relative',
        overflow: 'hidden',
        padding: '60px',
      }}
    >
      {/* Background grid */}
      <div
        style={{
          position: 'absolute',
          inset: 0,
          backgroundImage: `
            linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '40px 40px',
          opacity: 0.3,
        }}
      />

      {/* Title */}
      <div
        style={{
          fontSize: '48px',
          fontWeight: 'bold',
          marginBottom: '60px',
          textAlign: 'center',
          background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
        }}
      >
        Powerful Restaurant Command Center
      </div>

      {/* Dashboard mockup */}
      <div
        style={{
          width: '100%',
          maxWidth: '1400px',
          height: '700px',
          background: 'rgba(30, 41, 59, 0.8)',
          borderRadius: '20px',
          border: '2px solid rgba(59, 130, 246, 0.3)',
          backdropFilter: 'blur(10px)',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
        }}
      >
        {/* Dashboard header */}
        <div
          style={{
            background: 'rgba(15, 23, 42, 0.8)',
            padding: '20px 40px',
            borderBottom: '2px solid rgba(59, 130, 246, 0.2)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'between',
          }}
        >
          <div>
            <div style={{ fontSize: '24px', fontWeight: 'bold' }}>
              MenuFlow Admin
            </div>
            <div style={{ fontSize: '14px', color: '#94a3b8' }}>
              Bella Vista Restaurant • Live Dashboard
            </div>
          </div>
          
          <div style={{ display: 'flex', gap: '20px', alignItems: 'center' }}>
            <div
              style={{
                background: '#10b981',
                color: 'white',
                padding: '8px 16px',
                borderRadius: '20px',
                fontSize: '14px',
                fontWeight: '600',
              }}
            >
              🟢 LIVE
            </div>
            <div style={{ fontSize: '16px', color: '#94a3b8' }}>
              {new Date().toLocaleTimeString()}
            </div>
          </div>
        </div>

        {/* Dashboard content */}
        <div
          style={{
            flex: 1,
            display: 'grid',
            gridTemplate: '1fr 1fr / 1fr 1fr',
            gap: '30px',
            padding: '30px',
          }}
        >
          {sections.map((section, index) => {
            const sectionOpacity = interpolate(
              frame,
              [section.delay, section.delay + 30],
              [0, 1],
              {
                extrapolateLeft: 'clamp',
                extrapolateRight: 'clamp',
                easing: Easing.ease,
              }
            );

            const sectionScale = spring({
              frame: frame - section.delay,
              fps,
              config: {
                damping: 200,
                stiffness: 100,
              },
            });

            return (
              <div
                key={index}
                style={{
                  opacity: sectionOpacity,
                  transform: `scale(${sectionScale})`,
                  background: 'rgba(15, 23, 42, 0.6)',
                  borderRadius: '16px',
                  border: '2px solid rgba(59, 130, 246, 0.2)',
                  padding: '30px',
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '20px',
                }}
              >
                {/* Section header */}
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '15px',
                    marginBottom: '10px',
                  }}
                >
                  <div style={{ fontSize: '32px' }}>{section.icon}</div>
                  <div>
                    <div style={{ fontSize: '20px', fontWeight: 'bold' }}>
                      {section.title}
                    </div>
                  </div>
                </div>

                {/* Section content based on type */}
                {index === 0 && ( // Real-time Orders
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
                    {[
                      { table: 'Table 5', items: '3 items', status: 'Preparing', time: '2 min' },
                      { table: 'Table 2', items: '2 items', status: 'Ready', time: '5 min' },
                      { table: 'Table 8', items: '4 items', status: 'Ordered', time: 'Just now' },
                    ].map((order, i) => (
                      <div
                        key={i}
                        style={{
                          background: 'rgba(59, 130, 246, 0.1)',
                          borderRadius: '8px',
                          padding: '15px',
                          display: 'flex',
                          justifyContent: 'between',
                          alignItems: 'center',
                        }}
                      >
                        <div>
                          <div style={{ fontSize: '16px', fontWeight: '600' }}>
                            {order.table}
                          </div>
                          <div style={{ fontSize: '14px', color: '#94a3b8' }}>
                            {order.items}
                          </div>
                        </div>
                        <div style={{ textAlign: 'right' }}>
                          <div
                            style={{
                              background: order.status === 'Ready' ? '#10b981' : 
                                         order.status === 'Preparing' ? '#f59e0b' : '#3b82f6',
                              color: 'white',
                              padding: '4px 12px',
                              borderRadius: '12px',
                              fontSize: '12px',
                              fontWeight: '600',
                              marginBottom: '5px',
                            }}
                          >
                            {order.status}
                          </div>
                          <div style={{ fontSize: '12px', color: '#94a3b8' }}>
                            {order.time}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {index === 1 && ( // Revenue Analytics
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'between',
                        alignItems: 'center',
                      }}
                    >
                      <div>
                        <div style={{ fontSize: '32px', fontWeight: 'bold', color: '#10b981' }}>
                          $2,847
                        </div>
                        <div style={{ fontSize: '14px', color: '#94a3b8' }}>
                          Today's Revenue
                        </div>
                      </div>
                      <div
                        style={{
                          background: '#10b981',
                          color: 'white',
                          padding: '8px 16px',
                          borderRadius: '12px',
                          fontSize: '14px',
                          fontWeight: '600',
                        }}
                      >
                        +28.5% ↗
                      </div>
                    </div>
                    
                    {/* Simple chart representation */}
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'end',
                        gap: '8px',
                        height: '80px',
                      }}
                    >
                      {[0.3, 0.6, 0.4, 0.8, 1.0, 0.7, 0.9].map((height, i) => (
                        <div
                          key={i}
                          style={{
                            flex: 1,
                            height: `${height * 100}%`,
                            background: 'linear-gradient(to top, #10b981, #3b82f6)',
                            borderRadius: '4px 4px 0 0',
                          }}
                        />
                      ))}
                    </div>
                  </div>
                )}

                {index === 2 && ( // Table Management
                  <div
                    style={{
                      display: 'grid',
                      gridTemplate: '1fr 1fr 1fr / 1fr 1fr 1fr',
                      gap: '10px',
                      height: '150px',
                    }}
                  >
                    {[...Array(9)].map((_, i) => (
                      <div
                        key={i}
                        style={{
                          background: i % 3 === 0 ? '#10b981' : 
                                     i % 3 === 1 ? '#f59e0b' : '#64748b',
                          borderRadius: '8px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          color: 'white',
                          fontSize: '14px',
                          fontWeight: 'bold',
                        }}
                      >
                        T{i + 1}
                      </div>
                    ))}
                  </div>
                )}

                {index === 3 && ( // Menu Controls
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
                    {[
                      { item: 'Signature Burger', available: true, price: '$18.99' },
                      { item: 'Daily Special', available: false, price: '$22.99' },
                      { item: 'Caesar Salad', available: true, price: '$12.99' },
                    ].map((menuItem, i) => (
                      <div
                        key={i}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'between',
                          padding: '10px 15px',
                          background: 'rgba(59, 130, 246, 0.1)',
                          borderRadius: '8px',
                        }}
                      >
                        <div>
                          <div style={{ fontSize: '16px', fontWeight: '600' }}>
                            {menuItem.item}
                          </div>
                          <div style={{ fontSize: '14px', color: '#94a3b8' }}>
                            {menuItem.price}
                          </div>
                        </div>
                        <div
                          style={{
                            width: '60px',
                            height: '30px',
                            background: menuItem.available ? '#10b981' : '#64748b',
                            borderRadius: '15px',
                            position: 'relative',
                            cursor: 'pointer',
                          }}
                        >
                          <div
                            style={{
                              position: 'absolute',
                              top: '3px',
                              left: menuItem.available ? '33px' : '3px',
                              width: '24px',
                              height: '24px',
                              background: 'white',
                              borderRadius: '50%',
                              transition: 'all 0.3s ease',
                            }}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Bottom features */}
      <div
        style={{
          position: 'absolute',
          bottom: '40px',
          display: 'flex',
          gap: '80px',
          fontSize: '18px',
          fontWeight: '600',
          color: '#94a3b8',
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
          <div style={{ color: '#10b981', fontSize: '20px' }}>⚡</div>
          Real-time Updates
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
          <div style={{ color: '#3b82f6', fontSize: '20px' }}>📊</div>
          Advanced Analytics
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
          <div style={{ color: '#f59e0b', fontSize: '20px' }}>🎛️</div>
          Complete Control
        </div>
      </div>
    </AbsoluteFill>
  );
};