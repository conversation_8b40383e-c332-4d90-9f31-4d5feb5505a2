import React from 'react';
import {
  AbsoluteFill,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  Easing,
  spring,
} from 'remotion';

export const CallToAction: React.FC = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  // Logo animation
  const logoScale = spring({
    frame: frame - 60,
    fps,
    config: {
      damping: 200,
      stiffness: 100,
    },
  });

  const logoOpacity = interpolate(
    frame,
    [60, 120],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
      easing: Easing.ease,
    }
  );

  // Text animations
  const titleOpacity = interpolate(
    frame,
    [180, 240],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
      easing: Easing.ease,
    }
  );

  const ctaOpacity = interpolate(
    frame,
    [300, 360],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
      easing: Easing.ease,
    }
  );

  const urlOpacity = interpolate(
    frame,
    [420, 480],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
      easing: Easing.ease,
    }
  );

  // Features showcase
  const features = [
    '📱 QR Code Ordering',
    '🤝 Collaborative Carts',
    '🧠 AI Dynamic Pricing',
    '📊 Real-time Analytics',
    '🌍 Multi-language Support',
    '💰 Revenue Optimization',
  ];

  const featureOpacity = interpolate(
    frame,
    [540, 600],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
      easing: Easing.ease,
    }
  );

  // Final message animation
  const finalMessageOpacity = interpolate(
    frame,
    [1800, 1860], // Near the end
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
      easing: Easing.ease,
    }
  );

  // Particle effects
  const particles = [...Array(50)].map((_, i) => ({
    id: i,
    x: Math.random() * 100,
    y: Math.random() * 100,
    delay: Math.random() * 600,
    duration: 300 + Math.random() * 300,
  }));

  return (
    <AbsoluteFill
      style={{
        background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #8b5cf6 50%, #3b82f6 75%, #0f172a 100%)',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Animated particles */}
      {particles.map((particle) => (
        <div
          key={particle.id}
          style={{
            position: 'absolute',
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: '4px',
            height: '4px',
            background: '#3b82f6',
            borderRadius: '50%',
            opacity: interpolate(
              frame,
              [particle.delay, particle.delay + 60, particle.delay + particle.duration - 60, particle.delay + particle.duration],
              [0, 1, 1, 0],
              {
                extrapolateLeft: 'clamp',
                extrapolateRight: 'clamp',
              }
            ),
            transform: `scale(${interpolate(
              frame,
              [particle.delay, particle.delay + particle.duration],
              [0.5, 2],
              {
                extrapolateLeft: 'clamp',
                extrapolateRight: 'clamp',
              }
            )})`,
          }}
        />
      ))}

      {/* Logo section */}
      <div
        style={{
          opacity: logoOpacity,
          transform: `scale(${logoScale})`,
          display: 'flex',
          alignItems: 'center',
          gap: '30px',
          marginBottom: '80px',
        }}
      >
        {/* MenuFlow logo */}
        <div
          style={{
            width: '150px',
            height: '150px',
            background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
            borderRadius: '25px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '80px',
            boxShadow: '0 30px 60px rgba(59, 130, 246, 0.4)',
          }}
        >
          🚀
        </div>
        
        {/* Brand text */}
        <div>
          <div
            style={{
              fontSize: '72px',
              fontWeight: 'bold',
              background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              marginBottom: '10px',
            }}
          >
            MenuFlow
          </div>
          <div
            style={{
              fontSize: '28px',
              color: '#94a3b8',
              fontWeight: '600',
            }}
          >
            Dynamo
          </div>
        </div>
      </div>

      {/* Main title */}
      <div
        style={{
          opacity: titleOpacity,
          fontSize: '64px',
          fontWeight: 'bold',
          textAlign: 'center',
          marginBottom: '60px',
          maxWidth: '1000px',
          lineHeight: 1.2,
        }}
      >
        Ready to Transform Your Restaurant?
      </div>

      {/* Features grid */}
      <div
        style={{
          opacity: featureOpacity,
          display: 'grid',
          gridTemplate: '1fr 1fr / 1fr 1fr 1fr',
          gap: '30px',
          marginBottom: '80px',
          maxWidth: '900px',
        }}
      >
        {features.map((feature, index) => (
          <div
            key={index}
            style={{
              background: 'rgba(59, 130, 246, 0.1)',
              border: '2px solid rgba(59, 130, 246, 0.3)',
              borderRadius: '16px',
              padding: '25px',
              textAlign: 'center',
              fontSize: '18px',
              fontWeight: '600',
              transform: `scale(${interpolate(
                frame,
                [540 + index * 20, 600 + index * 20],
                [0.8, 1],
                {
                  extrapolateLeft: 'clamp',
                  extrapolateRight: 'clamp',
                  easing: Easing.ease,
                }
              )})`,
              backdropFilter: 'blur(10px)',
            }}
          >
            {feature}
          </div>
        ))}
      </div>

      {/* CTA Button */}
      <div
        style={{
          opacity: ctaOpacity,
          marginBottom: '60px',
        }}
      >
        <div
          style={{
            background: 'linear-gradient(135deg, #10b981 0%, #3b82f6 100%)',
            color: 'white',
            padding: '25px 60px',
            borderRadius: '50px',
            fontSize: '32px',
            fontWeight: 'bold',
            textAlign: 'center',
            boxShadow: '0 20px 40px rgba(16, 185, 129, 0.3)',
            cursor: 'pointer',
            transform: `scale(${spring({
              frame: frame - 300,
              fps,
              config: {
                damping: 200,
                stiffness: 100,
              },
            })})`,
            position: 'relative',
            overflow: 'hidden',
          }}
        >
          {/* Button shine effect */}
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: `-100%`,
              width: '100%',
              height: '100%',
              background: 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent)',
              transform: `translateX(${interpolate(
                frame,
                [360, 420],
                [0, 200],
                {
                  extrapolateLeft: 'clamp',
                  extrapolateRight: 'clamp',
                }
              )}%)`,
            }}
          />
          Book Your Demo Today
        </div>
      </div>

      {/* Website URL */}
      <div
        style={{
          opacity: urlOpacity,
          marginBottom: '80px',
        }}
      >
        <div
          style={{
            fontSize: '48px',
            fontWeight: 'bold',
            color: '#3b82f6',
            textAlign: 'center',
            background: 'rgba(59, 130, 246, 0.1)',
            border: '2px solid rgba(59, 130, 246, 0.3)',
            borderRadius: '20px',
            padding: '20px 40px',
            backdropFilter: 'blur(10px)',
          }}
        >
          SMEAnalytica.dev
        </div>
      </div>

      {/* SME Analytica badge */}
      <div
        style={{
          opacity: urlOpacity,
          background: 'rgba(139, 92, 246, 0.2)',
          border: '2px solid rgba(139, 92, 246, 0.4)',
          borderRadius: '50px',
          padding: '20px 40px',
          fontSize: '24px',
          fontWeight: '600',
          color: '#8b5cf6',
          marginBottom: '60px',
        }}
      >
        Powered by SME Analytica Platform
      </div>

      {/* Final tagline */}
      <div
        style={{
          opacity: finalMessageOpacity,
          fontSize: '36px',
          fontWeight: '600',
          textAlign: 'center',
          color: '#94a3b8',
          position: 'absolute',
          bottom: '100px',
        }}
      >
        Where Technology Meets Hospitality
      </div>

      {/* Bottom decorative line */}
      <div
        style={{
          position: 'absolute',
          bottom: '60px',
          width: '300px',
          height: '4px',
          background: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 50%, #10b981 100%)',
          borderRadius: '2px',
          opacity: finalMessageOpacity,
        }}
      />

      {/* Competitive advantage reminder */}
      <div
        style={{
          position: 'absolute',
          top: '60px',
          right: '60px',
          background: 'rgba(239, 68, 68, 0.1)',
          border: '2px solid rgba(239, 68, 68, 0.3)',
          borderRadius: '16px',
          padding: '20px',
          fontSize: '16px',
          fontWeight: '600',
          color: '#ef4444',
          maxWidth: '300px',
          textAlign: 'center',
          opacity: interpolate(
            frame,
            [1200, 1260],
            [0, 1],
            {
              extrapolateLeft: 'clamp',
              extrapolateRight: 'clamp',
              easing: Easing.ease,
            }
          ),
        }}
      >
        🔥 Not Just Ordering<br />
        Complete Restaurant Transformation<br />
        with AI Dynamic Pricing
      </div>
    </AbsoluteFill>
  );
};