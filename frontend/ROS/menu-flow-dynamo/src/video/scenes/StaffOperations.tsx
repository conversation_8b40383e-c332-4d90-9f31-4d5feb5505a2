import React from 'react';
import {
  AbsoluteFill,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  Easing,
  spring,
} from 'remotion';
import { BRAND_COLORS } from '../constants/brandColors';

export const StaffOperations: React.FC = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  // Staff features to showcase
  const staffFeatures = [
    { 
      icon: '🔔', 
      title: 'Real-time Notifications', 
      description: 'Instant alerts for new orders and requests',
      delay: 60,
      color: BRAND_COLORS.secondary
    },
    { 
      icon: '🙋‍♂️', 
      title: 'Staff Request System', 
      description: 'Customers can call for assistance with one tap',
      delay: 120,
      color: BRAND_COLORS.primary
    },
    { 
      icon: '🪑', 
      title: 'Smart Table Management', 
      description: 'Real-time table status and occupancy tracking',
      delay: 180,
      color: BRAND_COLORS.accent
    },
    { 
      icon: '📱', 
      title: 'Mobile Staff App', 
      description: 'Dedicated interface for restaurant staff',
      delay: 240,
      color: BRAND_COLORS.secondary
    },
  ];

  // Notification animation
  const notificationPulse = interpolate(
    frame,
    [0, 30, 60, 90],
    [1, 1.1, 1, 1.1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
      easing: Easing.ease,
    }
  );

  return (
    <AbsoluteFill
      style={{
        background: BRAND_COLORS.gradients.background,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        color: BRAND_COLORS.textPrimary,
        position: 'relative',
        overflow: 'hidden',
        padding: '80px',
      }}
    >
      {/* Background pattern */}
      <div
        style={{
          position: 'absolute',
          inset: 0,
          backgroundImage: `
            radial-gradient(circle at 25% 25%, rgba(17, 165, 232, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(95, 119, 144, 0.1) 0%, transparent 50%)
          `,
        }}
      />

      {/* Title */}
      <div
        style={{
          fontSize: '56px',
          fontWeight: 'bold',
          marginBottom: '80px',
          textAlign: 'center',
          background: BRAND_COLORS.gradients.primary,
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
        }}
      >
        Empowering Your Staff
      </div>

      {/* Main content area */}
      <div
        style={{
          display: 'flex',
          width: '100%',
          maxWidth: '1400px',
          gap: '80px',
          alignItems: 'center',
        }}
      >
        {/* Left side - Staff dashboard mockup */}
        <div
          style={{
            flex: 1,
            background: BRAND_COLORS.cardBackground,
            borderRadius: '20px',
            padding: '40px',
            border: `2px solid ${BRAND_COLORS.primary}`,
            boxShadow: `0 20px 40px rgba(17, 165, 232, 0.2)`,
          }}
        >
          {/* Dashboard header */}
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'between',
              marginBottom: '30px',
            }}
          >
            <div>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: BRAND_COLORS.textPrimary }}>
                Staff Dashboard
              </div>
              <div style={{ fontSize: '14px', color: BRAND_COLORS.textMuted }}>
                Live Operations Center
              </div>
            </div>
            
            {/* Notification bell */}
            <div
              style={{
                position: 'relative',
                transform: `scale(${notificationPulse})`,
              }}
            >
              <div
                style={{
                  width: '50px',
                  height: '50px',
                  background: BRAND_COLORS.secondary,
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '24px',
                  boxShadow: `0 0 20px rgba(17, 165, 232, 0.5)`,
                }}
              >
                🔔
              </div>
              
              {/* Notification badge */}
              <div
                style={{
                  position: 'absolute',
                  top: '-5px',
                  right: '-5px',
                  width: '20px',
                  height: '20px',
                  background: BRAND_COLORS.error,
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '12px',
                  fontWeight: 'bold',
                  color: 'white',
                }}
              >
                3
              </div>
            </div>
          </div>

          {/* Active requests */}
          <div style={{ marginBottom: '30px' }}>
            <div style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '15px', color: BRAND_COLORS.textPrimary }}>
              Active Requests
            </div>
            
            {[
              { table: 'Table 5', request: 'Assistance needed', time: '2 min ago', urgent: true },
              { table: 'Table 12', request: 'Extra napkins', time: '5 min ago', urgent: false },
              { table: 'Table 3', request: 'Check please', time: '1 min ago', urgent: true },
            ].map((request, i) => (
              <div
                key={i}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'between',
                  padding: '15px',
                  background: request.urgent ? 'rgba(239, 68, 68, 0.1)' : BRAND_COLORS.background,
                  borderRadius: '8px',
                  marginBottom: '10px',
                  border: request.urgent ? `2px solid ${BRAND_COLORS.error}` : `1px solid ${BRAND_COLORS.primary}`,
                  opacity: interpolate(
                    frame,
                    [60 + i * 30, 90 + i * 30],
                    [0, 1],
                    {
                      extrapolateLeft: 'clamp',
                      extrapolateRight: 'clamp',
                      easing: Easing.ease,
                    }
                  ),
                }}
              >
                <div>
                  <div style={{ fontSize: '16px', fontWeight: '600', color: BRAND_COLORS.textPrimary }}>
                    {request.table}
                  </div>
                  <div style={{ fontSize: '14px', color: BRAND_COLORS.textMuted }}>
                    {request.request}
                  </div>
                </div>
                <div style={{ textAlign: 'right' }}>
                  <div
                    style={{
                      background: request.urgent ? BRAND_COLORS.error : BRAND_COLORS.success,
                      color: 'white',
                      padding: '6px 12px',
                      borderRadius: '12px',
                      fontSize: '12px',
                      fontWeight: '600',
                      marginBottom: '5px',
                    }}
                  >
                    {request.urgent ? 'URGENT' : 'NORMAL'}
                  </div>
                  <div style={{ fontSize: '12px', color: BRAND_COLORS.textMuted }}>
                    {request.time}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Table status grid */}
          <div>
            <div style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '15px', color: BRAND_COLORS.textPrimary }}>
              Table Status
            </div>
            <div
              style={{
                display: 'grid',
                gridTemplate: '1fr 1fr 1fr / 1fr 1fr 1fr 1fr',
                gap: '8px',
              }}
            >
              {[...Array(12)].map((_, i) => (
                <div
                  key={i}
                  style={{
                    height: '40px',
                    background: i % 4 === 0 ? BRAND_COLORS.success : 
                               i % 4 === 1 ? BRAND_COLORS.warning : 
                               i % 4 === 2 ? BRAND_COLORS.error : BRAND_COLORS.cardBackground,
                    borderRadius: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: '14px',
                    fontWeight: 'bold',
                    border: `1px solid ${BRAND_COLORS.primary}`,
                  }}
                >
                  {i + 1}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right side - Features list */}
        <div
          style={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            gap: '40px',
          }}
        >
          {staffFeatures.map((feature, index) => {
            const featureOpacity = interpolate(
              frame,
              [feature.delay, feature.delay + 30],
              [0, 1],
              {
                extrapolateLeft: 'clamp',
                extrapolateRight: 'clamp',
                easing: Easing.ease,
              }
            );

            const featureScale = spring({
              frame: frame - feature.delay,
              fps,
              config: {
                damping: 200,
                stiffness: 100,
              },
            });

            return (
              <div
                key={index}
                style={{
                  opacity: featureOpacity,
                  transform: `scale(${featureScale})`,
                  background: `rgba(17, 165, 232, 0.1)`,
                  border: `2px solid ${feature.color}`,
                  borderRadius: '20px',
                  padding: '30px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '25px',
                  backdropFilter: 'blur(10px)',
                }}
              >
                {/* Feature icon */}
                <div
                  style={{
                    width: '80px',
                    height: '80px',
                    background: feature.color,
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '40px',
                    boxShadow: `0 10px 20px rgba(17, 165, 232, 0.3)`,
                  }}
                >
                  {feature.icon}
                </div>

                {/* Feature content */}
                <div>
                  <div
                    style={{
                      fontSize: '24px',
                      fontWeight: 'bold',
                      color: BRAND_COLORS.textPrimary,
                      marginBottom: '10px',
                    }}
                  >
                    {feature.title}
                  </div>
                  <div
                    style={{
                      fontSize: '16px',
                      color: BRAND_COLORS.textMuted,
                      lineHeight: 1.4,
                    }}
                  >
                    {feature.description}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Bottom tagline */}
      <div
        style={{
          position: 'absolute',
          bottom: '40px',
          fontSize: '28px',
          fontWeight: '600',
          color: BRAND_COLORS.secondary,
          textAlign: 'center',
          opacity: interpolate(
            frame,
            [300, 360],
            [0, 1],
            {
              extrapolateLeft: 'clamp',
              extrapolateRight: 'clamp',
              easing: Easing.ease,
            }
          ),
        }}
      >
        Streamlined Operations • Happy Staff • Better Service
      </div>
    </AbsoluteFill>
  );
};