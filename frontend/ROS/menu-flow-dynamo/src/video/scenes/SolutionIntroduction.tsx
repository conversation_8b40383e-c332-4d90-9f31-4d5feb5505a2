import React from 'react';
import {
  AbsoluteFill,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  Easing,
  spring,
} from 'remotion';
import { BRAND_COLORS } from '../constants/brandColors';
import { MenuFlowLogo } from '../components/MenuFlowLogo';

export const SolutionIntroduction: React.FC = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  // Logo animation
  const logoScale = spring({
    frame,
    fps,
    config: {
      damping: 200,
      stiffness: 100,
    },
  });

  const logoOpacity = interpolate(
    frame,
    [0, 30],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
      easing: Easing.ease,
    }
  );

  // Text animations
  const titleOpacity = interpolate(
    frame,
    [60, 90],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
      easing: Easing.ease,
    }
  );

  const subtitleOpacity = interpolate(
    frame,
    [120, 150],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
      easing: Easing.ease,
    }
  );

  // Interface mockup animation
  const interfaceOpacity = interpolate(
    frame,
    [180, 240],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
      easing: Easing.ease,
    }
  );

  const interfaceTranslate = interpolate(
    frame,
    [180, 240],
    [100, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
      easing: Easing.ease,
    }
  );

  return (
    <AbsoluteFill
      style={{
        background: BRAND_COLORS.gradients.background,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        color: BRAND_COLORS.textPrimary,
        padding: '100px',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Background pattern */}
      <div
        style={{
          position: 'absolute',
          inset: 0,
          backgroundImage: `radial-gradient(circle at 25% 25%, rgba(95, 119, 144, 0.1) 0%, transparent 50%),
                           radial-gradient(circle at 75% 75%, rgba(17, 165, 232, 0.1) 0%, transparent 50%)`,
        }}
      />

      {/* Logo */}
      <div
        style={{
          opacity: logoOpacity,
          transform: `scale(${logoScale})`,
          marginBottom: '60px',
        }}
      >
        <MenuFlowLogo size={120} showText={true} animated={true} />
      </div>

      {/* Main title */}
      <div
        style={{
          opacity: titleOpacity,
          fontSize: '64px',
          fontWeight: 'bold',
          textAlign: 'center',
          marginBottom: '40px',
          maxWidth: '1000px',
        }}
      >
        The AI-Powered Restaurant Operating System
      </div>

      {/* Subtitle */}
      <div
        style={{
          opacity: subtitleOpacity,
          fontSize: '32px',
          textAlign: 'center',
          color: BRAND_COLORS.textMuted,
          marginBottom: '80px',
          maxWidth: '800px',
          lineHeight: 1.4,
        }}
      >
        Complete digital transformation for your restaurant with intelligent features that delight customers and boost revenue
      </div>

      {/* Interface mockup */}
      <div
        style={{
          opacity: interfaceOpacity,
          transform: `translateY(${interfaceTranslate}px)`,
          display: 'flex',
          gap: '40px',
          alignItems: 'center',
        }}
      >
        {/* Phone mockup */}
        <div
          style={{
            width: '300px',
            height: '600px',
            background: BRAND_COLORS.cardBackground,
            borderRadius: '40px',
            padding: '20px',
            border: `8px solid ${BRAND_COLORS.primary}`,
            boxShadow: `0 25px 50px rgba(17, 165, 232, 0.3)`,
          }}
        >
          <div
            style={{
              width: '100%',
              height: '100%',
              background: `linear-gradient(180deg, ${BRAND_COLORS.textPrimary} 0%, #e2e8f0 100%)`,
              borderRadius: '28px',
              display: 'flex',
              flexDirection: 'column',
              padding: '30px 20px',
            }}
          >
            {/* Header */}
            <div
              style={{
                fontSize: '24px',
                fontWeight: 'bold',
                color: BRAND_COLORS.background,
                marginBottom: '30px',
                textAlign: 'center',
              }}
            >
              Restaurant Menu
            </div>

            {/* Menu items */}
            {[...Array(4)].map((_, i) => (
              <div
                key={i}
                style={{
                  background: 'white',
                  borderRadius: '12px',
                  padding: '20px',
                  marginBottom: '15px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '15px',
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                }}
              >
                <div
                  style={{
                    width: '60px',
                    height: '60px',
                    background: `linear-gradient(135deg, ${
                      ['#ef4444', '#10b981', '#3b82f6', '#f59e0b'][i]
                    } 0%, ${
                      ['#dc2626', '#059669', '#2563eb', '#d97706'][i]
                    } 100%)`,
                    borderRadius: '8px',
                  }}
                />
                <div style={{ flex: 1 }}>
                  <div
                    style={{
                      fontSize: '16px',
                      fontWeight: '600',
                      color: BRAND_COLORS.background,
                      marginBottom: '5px',
                    }}
                  >
                    Menu Item {i + 1}
                  </div>
                  <div style={{ fontSize: '14px', color: BRAND_COLORS.primary }}>
                    ${(15 + i * 3).toFixed(2)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Features list */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '30px' }}>
          {[
            '📱 QR Code Instant Access',
            '🤝 Collaborative Ordering',
            '🧠 AI Dynamic Pricing',
            '🌍 Multi-language Support',
            '📊 Real-time Analytics',
          ].map((feature, i) => (
            <div
              key={i}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '20px',
                fontSize: '24px',
                opacity: interpolate(
                  frame,
                  [240 + i * 20, 270 + i * 20],
                  [0, 1],
                  {
                    extrapolateLeft: 'clamp',
                    extrapolateRight: 'clamp',
                    easing: Easing.ease,
                  }
                ),
              }}
            >
              <div
                style={{
                  width: '50px',
                  height: '50px',
                  background: `rgba(17, 165, 232, 0.2)`,
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  border: `2px solid ${BRAND_COLORS.secondary}`,
                }}
              >
                ✓
              </div>
              <span>{feature}</span>
            </div>
          ))}
        </div>
      </div>

      {/* SME Analytica badge */}
      <div
        style={{
          position: 'absolute',
          bottom: '40px',
          right: '40px',
          background: `rgba(17, 165, 232, 0.1)`,
          border: `2px solid ${BRAND_COLORS.secondary}`,
          borderRadius: '50px',
          padding: '15px 30px',
          fontSize: '18px',
          color: BRAND_COLORS.secondary,
          fontWeight: '600',
        }}
      >
        Powered by SME Analytica
      </div>
    </AbsoluteFill>
  );
};