import React from 'react';
import {
  AbsoluteFill,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  Easing,
  spring,
} from 'remotion';

export const QRCodeDemo: React.FC = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  // Phone scanning animation
  const phoneScale = spring({
    frame,
    fps,
    config: {
      damping: 200,
      stiffness: 100,
    },
  });

  const scanLineProgress = interpolate(
    frame,
    [60, 180],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
      easing: Easing.ease,
    }
  );

  // Menu loading animation
  const menuOpacity = interpolate(
    frame,
    [180, 240],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
      easing: Easing.ease,
    }
  );

  return (
    <AbsoluteFill
      style={{
        background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Background pattern */}
      <div
        style={{
          position: 'absolute',
          inset: 0,
          backgroundImage: `
            radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%)
          `,
        }}
      />

      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '100px',
        }}
      >
        {/* QR Code */}
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '40px',
          }}
        >
          {/* Table number */}
          <div
            style={{
              background: 'rgba(59, 130, 246, 0.2)',
              border: '2px solid #3b82f6',
              borderRadius: '12px',
              padding: '20px 40px',
              fontSize: '32px',
              fontWeight: 'bold',
              color: '#3b82f6',
            }}
          >
            TABLE 7
          </div>

          {/* QR Code */}
          <div
            style={{
              width: '300px',
              height: '300px',
              backgroundColor: 'white',
              borderRadius: '20px',
              padding: '20px',
              display: 'grid',
              gridTemplate: 'repeat(15, 1fr) / repeat(15, 1fr)',
              gap: '2px',
              position: 'relative',
              boxShadow: '0 25px 50px rgba(0, 0, 0, 0.3)',
            }}
          >
            {/* QR pattern */}
            {[...Array(225)].map((_, i) => (
              <div
                key={i}
                style={{
                  backgroundColor: Math.random() > 0.6 ? '#000' : '#fff',
                  borderRadius: '1px',
                }}
              />
            ))}

            {/* Scan line */}
            <div
              style={{
                position: 'absolute',
                top: `${scanLineProgress * 100}%`,
                left: 0,
                right: 0,
                height: '4px',
                background: 'linear-gradient(90deg, transparent 0%, #10b981 50%, transparent 100%)',
                boxShadow: '0 0 20px #10b981',
                opacity: scanLineProgress < 1 ? 1 : 0,
              }}
            />

            {/* Success indicator */}
            {scanLineProgress >= 1 && (
              <div
                style={{
                  position: 'absolute',
                  inset: 0,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  background: 'rgba(16, 185, 129, 0.9)',
                  borderRadius: '16px',
                  fontSize: '80px',
                }}
              >
                ✓
              </div>
            )}
          </div>

          <div
            style={{
              fontSize: '24px',
              color: '#94a3b8',
              textAlign: 'center',
            }}
          >
            Scan to Order
          </div>
        </div>

        {/* Arrow */}
        <div
          style={{
            fontSize: '80px',
            color: '#10b981',
            transform: `scale(${phoneScale})`,
          }}
        >
          →
        </div>

        {/* Phone with menu */}
        <div
          style={{
            width: '350px',
            height: '700px',
            background: '#1f2937',
            borderRadius: '40px',
            padding: '20px',
            border: '8px solid #374151',
            boxShadow: '0 25px 50px rgba(0, 0, 0, 0.5)',
            transform: `scale(${phoneScale})`,
          }}
        >
          <div
            style={{
              width: '100%',
              height: '100%',
              background: 'linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%)',
              borderRadius: '28px',
              display: 'flex',
              flexDirection: 'column',
              overflow: 'hidden',
              position: 'relative',
            }}
          >
            {/* Loading state */}
            {menuOpacity < 1 && (
              <div
                style={{
                  position: 'absolute',
                  inset: 0,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  background: '#f8fafc',
                  flexDirection: 'column',
                  gap: '20px',
                }}
              >
                <div
                  style={{
                    width: '60px',
                    height: '60px',
                    border: '6px solid #e2e8f0',
                    borderTop: '6px solid #3b82f6',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite',
                  }}
                />
                <div style={{ fontSize: '18px', color: '#64748b' }}>
                  Loading Menu...
                </div>
              </div>
            )}

            {/* Menu content */}
            <div
              style={{
                opacity: menuOpacity,
                padding: '30px 20px',
                display: 'flex',
                flexDirection: 'column',
                height: '100%',
              }}
            >
              {/* Header */}
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'between',
                  marginBottom: '30px',
                }}
              >
                <div>
                  <div
                    style={{
                      fontSize: '28px',
                      fontWeight: 'bold',
                      color: '#1f2937',
                      marginBottom: '5px',
                    }}
                  >
                    Bella Vista
                  </div>
                  <div style={{ fontSize: '16px', color: '#64748b' }}>
                    Table 7 • 2 guests
                  </div>
                </div>
                
                {/* Language selector */}
                <div
                  style={{
                    background: '#3b82f6',
                    color: 'white',
                    padding: '8px 16px',
                    borderRadius: '20px',
                    fontSize: '14px',
                    fontWeight: '600',
                  }}
                >
                  EN | ES
                </div>
              </div>

              {/* Menu categories */}
              <div
                style={{
                  display: 'flex',
                  gap: '10px',
                  marginBottom: '30px',
                  flexWrap: 'wrap',
                }}
              >
                {['Appetizers', 'Mains', 'Desserts', 'Drinks'].map((category, i) => (
                  <div
                    key={category}
                    style={{
                      background: i === 0 ? '#3b82f6' : '#e2e8f0',
                      color: i === 0 ? 'white' : '#64748b',
                      padding: '10px 20px',
                      borderRadius: '25px',
                      fontSize: '14px',
                      fontWeight: '600',
                    }}
                  >
                    {category}
                  </div>
                ))}
              </div>

              {/* Menu items */}
              <div style={{ flex: 1, overflow: 'hidden' }}>
                {[...Array(3)].map((_, i) => (
                  <div
                    key={i}
                    style={{
                      background: 'white',
                      borderRadius: '16px',
                      padding: '20px',
                      marginBottom: '15px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '15px',
                      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
                      border: '1px solid #e2e8f0',
                    }}
                  >
                    <div
                      style={{
                        width: '70px',
                        height: '70px',
                        background: `linear-gradient(135deg, ${
                          ['#ef4444', '#10b981', '#f59e0b'][i]
                        } 0%, ${
                          ['#dc2626', '#059669', '#d97706'][i]
                        } 100%)`,
                        borderRadius: '12px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '30px',
                      }}
                    >
                      {['🥗', '🍝', '🍰'][i]}
                    </div>
                    <div style={{ flex: 1 }}>
                      <div
                        style={{
                          fontSize: '18px',
                          fontWeight: '600',
                          color: '#1f2937',
                          marginBottom: '5px',
                        }}
                      >
                        {['Caesar Salad', 'Pasta Carbonara', 'Tiramisu'][i]}
                      </div>
                      <div
                        style={{
                          fontSize: '14px',
                          color: '#64748b',
                          marginBottom: '10px',
                        }}
                      >
                        {['Fresh romaine, parmesan...', 'Creamy pasta with bacon...', 'Classic Italian dessert...'][i]}
                      </div>
                      <div
                        style={{
                          fontSize: '20px',
                          fontWeight: 'bold',
                          color: '#10b981',
                        }}
                      >
                        ${[12.99, 18.99, 8.99][i]}
                      </div>
                    </div>
                    <div
                      style={{
                        width: '40px',
                        height: '40px',
                        background: '#3b82f6',
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        fontSize: '20px',
                        fontWeight: 'bold',
                      }}
                    >
                      +
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom text */}
      <div
        style={{
          position: 'absolute',
          bottom: '50px',
          left: '50%',
          transform: 'translateX(-50%)',
          fontSize: '32px',
          fontWeight: '600',
          color: '#10b981',
          textAlign: 'center',
        }}
      >
        No App Downloads • Instant Access • Multilingual
      </div>
    </AbsoluteFill>
  );
};