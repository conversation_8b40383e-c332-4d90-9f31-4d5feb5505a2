import React from 'react';
import {
  AbsoluteFill,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  Easing,
  spring,
} from 'remotion';

export const DynamicPricing: React.FC = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  // AI brain animation
  const brainPulse = interpolate(
    frame,
    [0, 30, 60],
    [1, 1.1, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
      easing: Easing.ease,
    }
  );

  // Chart animation
  const chartProgress = interpolate(
    frame,
    [120, 300],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
      easing: Easing.ease,
    }
  );

  // Price changes
  const priceChangeDelay = [180, 240, 300, 360, 420];
  const prices = [
    { item: 'Signature Burger', base: 18.99, peak: 22.99, low: 15.99 },
    { item: 'Caesar Salad', base: 12.99, peak: 14.99, low: 9.99 },
    { item: 'Pasta Alfredo', base: 16.99, peak: 19.99, low: 13.99 },
  ];

  // Revenue comparison
  const revenueOpacity = interpolate(
    frame,
    [480, 540],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
      easing: Easing.ease,
    }
  );

  return (
    <AbsoluteFill
      style={{
        background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%)',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Animated background */}
      <div
        style={{
          position: 'absolute',
          inset: 0,
          background: `
            radial-gradient(circle at 20% 20%, rgba(147, 51, 234, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%)
          `,
        }}
      />

      {/* Title */}
      <div
        style={{
          fontSize: '56px',
          fontWeight: 'bold',
          marginBottom: '60px',
          textAlign: 'center',
          background: 'linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
        }}
      >
        Revolutionary Dynamic Pricing AI
      </div>

      <div
        style={{
          display: 'flex',
          width: '100%',
          maxWidth: '1400px',
          gap: '80px',
          alignItems: 'center',
        }}
      >
        {/* Left side - AI Brain */}
        <div
          style={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '40px',
          }}
        >
          {/* AI Brain */}
          <div
            style={{
              width: '300px',
              height: '300px',
              background: 'linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%)',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '120px',
              transform: `scale(${brainPulse})`,
              boxShadow: `0 0 100px rgba(139, 92, 246, ${brainPulse - 0.5})`,
              position: 'relative',
            }}
          >
            🧠
            
            {/* Pulse rings */}
            <div
              style={{
                position: 'absolute',
                inset: '-20px',
                border: '2px solid rgba(139, 92, 246, 0.3)',
                borderRadius: '50%',
                animation: 'pulse 2s ease-in-out infinite',
              }}
            />
            <div
              style={{
                position: 'absolute',
                inset: '-40px',
                border: '2px solid rgba(59, 130, 246, 0.2)',
                borderRadius: '50%',
                animation: 'pulse 2s ease-in-out infinite 0.5s',
              }}
            />
          </div>

          {/* AI Analysis Points */}
          <div
            style={{
              display: 'grid',
              gridTemplate: '1fr 1fr / 1fr 1fr',
              gap: '20px',
              width: '100%',
              maxWidth: '400px',
            }}
          >
            {[
              { icon: '📊', text: 'Traffic Analysis', delay: 60 },
              { icon: '⏰', text: 'Peak Hours', delay: 120 },
              { icon: '🎯', text: 'Demand Patterns', delay: 180 },
              { icon: '💰', text: 'Revenue Optimization', delay: 240 },
            ].map((item, i) => (
              <div
                key={i}
                style={{
                  background: 'rgba(59, 130, 246, 0.1)',
                  border: '2px solid rgba(59, 130, 246, 0.3)',
                  borderRadius: '12px',
                  padding: '20px',
                  textAlign: 'center',
                  opacity: interpolate(
                    frame,
                    [item.delay, item.delay + 30],
                    [0, 1],
                    {
                      extrapolateLeft: 'clamp',
                      extrapolateRight: 'clamp',
                      easing: Easing.ease,
                    }
                  ),
                  transform: `scale(${spring({
                    frame: frame - item.delay,
                    fps,
                    config: { damping: 200, stiffness: 100 },
                  })})`,
                }}
              >
                <div style={{ fontSize: '24px', marginBottom: '8px' }}>
                  {item.icon}
                </div>
                <div style={{ fontSize: '14px', fontWeight: '600' }}>
                  {item.text}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Right side - Dynamic Pricing Dashboard */}
        <div
          style={{
            flex: 1.5,
            background: 'rgba(30, 41, 59, 0.8)',
            borderRadius: '20px',
            padding: '40px',
            border: '2px solid rgba(59, 130, 246, 0.3)',
            backdropFilter: 'blur(10px)',
          }}
        >
          {/* Dashboard Header */}
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'between',
              marginBottom: '40px',
            }}
          >
            <div>
              <div style={{ fontSize: '28px', fontWeight: 'bold', marginBottom: '5px' }}>
                Dynamic Pricing Engine
              </div>
              <div style={{ fontSize: '16px', color: '#94a3b8' }}>
                Real-time price optimization
              </div>
            </div>
            <div
              style={{
                background: '#10b981',
                color: 'white',
                padding: '10px 20px',
                borderRadius: '20px',
                fontSize: '14px',
                fontWeight: '600',
              }}
            >
              ACTIVE
            </div>
          </div>

          {/* Time periods */}
          <div
            style={{
              display: 'flex',
              gap: '20px',
              marginBottom: '40px',
            }}
          >
            {[
              { time: '2:00 PM', status: 'Low Traffic', color: '#10b981' },
              { time: '7:00 PM', status: 'Peak Hours', color: '#ef4444' },
              { time: '10:00 PM', status: 'Late Night', color: '#f59e0b' },
            ].map((period, i) => (
              <div
                key={i}
                style={{
                  flex: 1,
                  background: 'rgba(15, 23, 42, 0.5)',
                  borderRadius: '12px',
                  padding: '20px',
                  textAlign: 'center',
                  border: frame >= 300 + i * 60 ? `2px solid ${period.color}` : '2px solid transparent',
                  transition: 'all 0.3s ease',
                }}
              >
                <div style={{ fontSize: '20px', fontWeight: 'bold', marginBottom: '5px' }}>
                  {period.time}
                </div>
                <div
                  style={{
                    fontSize: '14px',
                    color: period.color,
                    fontWeight: '600',
                  }}
                >
                  {period.status}
                </div>
              </div>
            ))}
          </div>

          {/* Price adjustments */}
          <div style={{ marginBottom: '40px' }}>
            <div style={{ fontSize: '20px', fontWeight: 'bold', marginBottom: '20px' }}>
              Live Price Adjustments
            </div>
            
            {prices.map((item, i) => {
              const currentPrice = frame >= 300 
                ? item.peak 
                : frame >= 180 
                ? item.base 
                : item.low;
              
              const priceColor = currentPrice > item.base 
                ? '#ef4444' 
                : currentPrice < item.base 
                ? '#10b981' 
                : '#94a3b8';

              return (
                <div
                  key={i}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'between',
                    padding: '15px 20px',
                    background: 'rgba(15, 23, 42, 0.3)',
                    borderRadius: '8px',
                    marginBottom: '10px',
                    border: `2px solid ${priceColor}20`,
                  }}
                >
                  <div style={{ fontSize: '16px', fontWeight: '600' }}>
                    {item.item}
                  </div>
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '15px',
                    }}
                  >
                    {currentPrice !== item.base && (
                      <div
                        style={{
                          fontSize: '14px',
                          color: '#64748b',
                          textDecoration: 'line-through',
                        }}
                      >
                        ${item.base}
                      </div>
                    )}
                    <div
                      style={{
                        fontSize: '20px',
                        fontWeight: 'bold',
                        color: priceColor,
                      }}
                    >
                      ${currentPrice}
                    </div>
                    {currentPrice > item.base && (
                      <div
                        style={{
                          background: '#ef4444',
                          color: 'white',
                          padding: '4px 8px',
                          borderRadius: '12px',
                          fontSize: '12px',
                          fontWeight: '600',
                        }}
                      >
                        +{Math.round(((currentPrice - item.base) / item.base) * 100)}%
                      </div>
                    )}
                    {currentPrice < item.base && (
                      <div
                        style={{
                          background: '#10b981',
                          color: 'white',
                          padding: '4px 8px',
                          borderRadius: '12px',
                          fontSize: '12px',
                          fontWeight: '600',
                        }}
                      >
                        -{Math.round(((item.base - currentPrice) / item.base) * 100)}%
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Revenue impact */}
          <div
            style={{
              opacity: revenueOpacity,
              background: 'rgba(16, 185, 129, 0.1)',
              border: '2px solid rgba(16, 185, 129, 0.3)',
              borderRadius: '12px',
              padding: '20px',
              textAlign: 'center',
            }}
          >
            <div style={{ fontSize: '16px', color: '#94a3b8', marginBottom: '10px' }}>
              Revenue Increase This Hour
            </div>
            <div
              style={{
                fontSize: '36px',
                fontWeight: 'bold',
                color: '#10b981',
                marginBottom: '5px',
              }}
            >
              +28.5%
            </div>
            <div style={{ fontSize: '14px', color: '#10b981' }}>
              vs. Static Pricing
            </div>
          </div>
        </div>
      </div>

      {/* Bottom tagline */}
      <div
        style={{
          position: 'absolute',
          bottom: '40px',
          fontSize: '28px',
          fontWeight: '600',
          color: '#8b5cf6',
          textAlign: 'center',
        }}
      >
        Smart Pricing That Maximizes Revenue 24/7
      </div>
    </AbsoluteFill>
  );
};