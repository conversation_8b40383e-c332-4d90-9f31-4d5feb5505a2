import React from 'react';
import {
  AbsoluteFill,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  Easing,
  spring,
} from 'remotion';

export const AIRecommendations: React.FC = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  const recommendations = [
    { name: 'Chef\'s Special Ribeye', icon: '🥩', popularity: 95, delay: 60 },
    { name: 'Signature Lobster Bisque', icon: '🦞', popularity: 88, delay: 120 },
    { name: 'Truffle Mushroom Risotto', icon: '🍄', popularity: 92, delay: 180 },
  ];

  return (
    <AbsoluteFill
      style={{
        background: 'linear-gradient(135deg, #1e293b 0%, #0f172a 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Background sparkles */}
      {[...Array(20)].map((_, i) => (
        <div
          key={i}
          style={{
            position: 'absolute',
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
            width: '4px',
            height: '4px',
            background: '#f59e0b',
            borderRadius: '50%',
            opacity: interpolate(
              frame,
              [i * 10, i * 10 + 30, i * 10 + 60],
              [0, 1, 0],
              {
                extrapolateLeft: 'clamp',
                extrapolateRight: 'clamp',
              }
            ),
          }}
        />
      ))}

      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: '60px',
          maxWidth: '1200px',
          width: '100%',
        }}
      >
        {/* Title */}
        <div
          style={{
            fontSize: '48px',
            fontWeight: 'bold',
            textAlign: 'center',
            background: 'linear-gradient(135deg, #f59e0b 0%, #ef4444 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
          }}
        >
          AI-Powered Chef Recommendations
        </div>

        {/* Recommendations carousel */}
        <div
          style={{
            display: 'flex',
            gap: '40px',
            alignItems: 'center',
          }}
        >
          {recommendations.map((item, index) => {
            const itemOpacity = interpolate(
              frame,
              [item.delay, item.delay + 30],
              [0, 1],
              {
                extrapolateLeft: 'clamp',
                extrapolateRight: 'clamp',
                easing: Easing.ease,
              }
            );

            const itemScale = spring({
              frame: frame - item.delay,
              fps,
              config: {
                damping: 200,
                stiffness: 100,
              },
            });

            return (
              <div
                key={index}
                style={{
                  opacity: itemOpacity,
                  transform: `scale(${itemScale})`,
                  background: 'rgba(245, 158, 11, 0.1)',
                  border: '2px solid rgba(245, 158, 11, 0.3)',
                  borderRadius: '20px',
                  padding: '40px',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  gap: '20px',
                  minWidth: '280px',
                  boxShadow: '0 20px 40px rgba(245, 158, 11, 0.1)',
                  position: 'relative',
                }}
              >
                {/* Popularity badge */}
                <div
                  style={{
                    position: 'absolute',
                    top: '-15px',
                    right: '20px',
                    background: '#f59e0b',
                    color: 'white',
                    padding: '8px 16px',
                    borderRadius: '20px',
                    fontSize: '14px',
                    fontWeight: 'bold',
                  }}
                >
                  {item.popularity}% Popular
                </div>

                {/* Food icon */}
                <div
                  style={{
                    fontSize: '80px',
                    marginBottom: '10px',
                  }}
                >
                  {item.icon}
                </div>

                {/* Item name */}
                <div
                  style={{
                    fontSize: '24px',
                    fontWeight: 'bold',
                    textAlign: 'center',
                    lineHeight: 1.2,
                  }}
                >
                  {item.name}
                </div>

                {/* AI badge */}
                <div
                  style={{
                    background: 'linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%)',
                    color: 'white',
                    padding: '10px 20px',
                    borderRadius: '25px',
                    fontSize: '14px',
                    fontWeight: '600',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                  }}
                >
                  🤖 AI Recommended
                </div>

                {/* Popularity bar */}
                <div
                  style={{
                    width: '100%',
                    height: '8px',
                    background: 'rgba(245, 158, 11, 0.2)',
                    borderRadius: '4px',
                    overflow: 'hidden',
                  }}
                >
                  <div
                    style={{
                      width: `${item.popularity}%`,
                      height: '100%',
                      background: 'linear-gradient(90deg, #f59e0b 0%, #ef4444 100%)',
                      borderRadius: '4px',
                      transform: `scaleX(${interpolate(
                        frame,
                        [item.delay + 30, item.delay + 90],
                        [0, 1],
                        {
                          extrapolateLeft: 'clamp',
                          extrapolateRight: 'clamp',
                          easing: Easing.ease,
                        }
                      )})`,
                      transformOrigin: 'left',
                    }}
                  />
                </div>
              </div>
            );
          })}
        </div>

        {/* Features */}
        <div
          style={{
            display: 'flex',
            gap: '60px',
            justifyContent: 'center',
            fontSize: '20px',
            fontWeight: '600',
            color: '#94a3b8',
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
            <div style={{ color: '#3b82f6', fontSize: '24px' }}>📊</div>
            Real Order Data
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
            <div style={{ color: '#10b981', fontSize: '24px' }}>👨‍🍳</div>
            Chef Curated
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
            <div style={{ color: '#f59e0b', fontSize: '24px' }}>🎯</div>
            Personalized
          </div>
        </div>
      </div>
    </AbsoluteFill>
  );
};