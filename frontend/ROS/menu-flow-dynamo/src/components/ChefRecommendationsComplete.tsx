/**
 * Complete Chef Recommendations System
 * 
 * Comprehensive implementation for both admin management and customer display
 * of chef's featured recommendations with real-time sync
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  ChefHat, 
  Star, 
  TrendingUp, 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  EyeOff,
  Users,
  DollarSign,
  Clock,
  Award,
  Flame,
  Heart,
  Sparkles,
  ArrowUp,
  ArrowDown,
  Save,
  X,
  Check,
  AlertCircle,
  RefreshCw
} from 'lucide-react';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useRestaurant } from '@/contexts/RestaurantContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { motion, AnimatePresence } from 'framer-motion';

interface ChefRecommendation {
  id: string;
  menu_item_id: string;
  restaurant_id: string;
  is_featured: boolean;
  featured_priority: number;
  chef_notes?: string;
  recommendation_type: 'signature' | 'popular' | 'seasonal' | 'special';
  is_available: boolean;
  created_at: string;
  updated_at: string;
  // Menu item details
  menu_item: {
    id: string;
    name: string;
    description?: string;
    category: string;
    current_price: number;
    base_price: number;
    image_url?: string;
    is_available: boolean;
  };
  // Analytics
  total_orders?: number;
  recent_orders?: number;
  rating_average?: number;
}

interface MenuItemOption {
  id: string;
  name: string;
  category: string;
  current_price: number;
  is_available: boolean;
  image_url?: string;
}

interface ChefRecommendationsProps {
  mode: 'admin' | 'customer';
  onAddToCart?: (menuItemId: string) => void;
  className?: string;
}

// Chef Recommendations Management (Admin View)
const ChefRecommendationsAdmin: React.FC<{ className?: string }> = ({ className }) => {
  const { restaurantInfo } = useRestaurant();
  const { t } = useLanguage();
  const queryClient = useQueryClient();
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [editingItem, setEditingItem] = useState<ChefRecommendation | null>(null);

  // Fetch current recommendations
  const { data: recommendations = [], isLoading, error } = useQuery({
    queryKey: ['chef-recommendations', restaurantInfo?.id],
    queryFn: async () => {
      if (!restaurantInfo?.id) return [];

      const { data, error } = await supabase
        .from('chef_recommendations')
        .select(`
          *,
          menu_item:menu_items!inner (
            id,
            name,
            description,
            category,
            current_price,
            base_price,
            image_url,
            is_available
          )
        `)
        .eq('restaurant_id', restaurantInfo.id)
        .order('featured_priority', { ascending: true });

      if (error) throw error;
      return data as ChefRecommendation[];
    },
    enabled: !!restaurantInfo?.id
  });

  // Fetch available menu items for adding new recommendations
  const { data: menuItems = [] } = useQuery({
    queryKey: ['menu-items-for-recommendations', restaurantInfo?.id],
    queryFn: async () => {
      if (!restaurantInfo?.id) return [];

      const { data, error } = await supabase
        .from('menu_items')
        .select('id, name, category, current_price, is_available, image_url')
        .eq('restaurant_id', restaurantInfo.id)
        .eq('is_available', true)
        .order('name');

      if (error) throw error;
      return data as MenuItemOption[];
    },
    enabled: !!restaurantInfo?.id
  });

  // Add/Update recommendation mutation
  const addRecommendationMutation = useMutation({
    mutationFn: async (recommendation: Partial<ChefRecommendation>) => {
      const { data, error } = await supabase
        .from('chef_recommendations')
        .insert([{
          ...recommendation,
          restaurant_id: restaurantInfo!.id
        }])
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['chef-recommendations'] });
      queryClient.invalidateQueries({ queryKey: ['recommendations'] });
      toast.success('Chef recommendation added successfully');
      setIsAddingNew(false);
    },
    onError: (error) => {
      console.error('Error adding recommendation:', error);
      toast.error('Failed to add recommendation');
    }
  });

  // Update recommendation mutation
  const updateRecommendationMutation = useMutation({
    mutationFn: async (recommendation: Partial<ChefRecommendation> & { id: string }) => {
      const { data, error } = await supabase
        .from('chef_recommendations')
        .update(recommendation)
        .eq('id', recommendation.id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['chef-recommendations'] });
      queryClient.invalidateQueries({ queryKey: ['recommendations'] });
      toast.success('Chef recommendation updated successfully');
      setEditingItem(null);
    },
    onError: (error) => {
      console.error('Error updating recommendation:', error);
      toast.error('Failed to update recommendation');
    }
  });

  // Delete recommendation mutation
  const deleteRecommendationMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('chef_recommendations')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['chef-recommendations'] });
      queryClient.invalidateQueries({ queryKey: ['recommendations'] });
      toast.success('Chef recommendation removed');
    },
    onError: (error) => {
      console.error('Error deleting recommendation:', error);
      toast.error('Failed to remove recommendation');
    }
  });

  // Toggle featured status
  const toggleFeatured = useCallback(async (id: string, currentStatus: boolean) => {
    await updateRecommendationMutation.mutateAsync({
      id,
      is_featured: !currentStatus
    });
  }, [updateRecommendationMutation]);

  // Update priority
  const updatePriority = useCallback(async (id: string, direction: 'up' | 'down') => {
    const currentItem = recommendations.find(r => r.id === id);
    if (!currentItem) return;

    const newPriority = direction === 'up' 
      ? Math.max(1, currentItem.featured_priority - 1)
      : currentItem.featured_priority + 1;

    await updateRecommendationMutation.mutateAsync({
      id,
      featured_priority: newPriority
    });
  }, [recommendations, updateRecommendationMutation]);

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="h-5 w-5 animate-spin mr-2" />
            <span>{t('loadingChefRecommendations')}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {t('failedToLoadChefRecommendations')}
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <ChefHat className="h-5 w-5" />
              {t('chefRecommendations')}
            </CardTitle>
            <CardDescription>
              {t('manageFeaturedItemsDescription')}
            </CardDescription>
          </div>
          <Dialog open={isAddingNew} onOpenChange={setIsAddingNew}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-1" />
                {t('addChefRecommendation')}
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>{t('addChefRecommendation')}</DialogTitle>
                <DialogDescription>
                  {t('selectMenuItemAndConfigure')}
                </DialogDescription>
              </DialogHeader>
              <RecommendationForm
                menuItems={menuItems}
                onSubmit={(data) => addRecommendationMutation.mutate(data)}
                onCancel={() => setIsAddingNew(false)}
              />
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>

      <CardContent>
        {recommendations.length === 0 ? (
          <div className="text-center py-8">
            <ChefHat className="h-12 w-12 mx-auto text-gray-400 mb-3" />
            <h3 className="text-lg font-medium text-gray-900 mb-1">{t('noRecommendationsYet')}</h3>
            <p className="text-gray-500 mb-4">{t('startByAddingFeaturedItems')}</p>
            <Button onClick={() => setIsAddingNew(true)}>
              <Plus className="h-4 w-4 mr-1" />
              {t('addFirstRecommendation')}
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {recommendations.map((recommendation) => (
              <motion.div
                key={recommendation.id}
                layout
                className="flex items-center justify-between p-4 border rounded-lg bg-white"
              >
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    {recommendation.menu_item.image_url ? (
                      <img
                        src={recommendation.menu_item.image_url}
                        alt={recommendation.menu_item.name}
                        className="w-16 h-16 object-cover rounded-lg"
                      />
                    ) : (
                      <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                        <ChefHat className="h-6 w-6 text-gray-400" />
                      </div>
                    )}
                    {recommendation.is_featured && (
                      <Badge className="absolute -top-1 -right-1 bg-yellow-500">
                        <Star className="h-3 w-3" />
                      </Badge>
                    )}
                  </div>

                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900">
                      {recommendation.menu_item.name}
                    </h4>
                    <p className="text-sm text-gray-600">
                      {recommendation.menu_item.category} • €{recommendation.menu_item.current_price.toFixed(2)}
                    </p>
                    {recommendation.chef_notes && (
                      <p className="text-sm text-blue-600 italic mt-1">
                        "{recommendation.chef_notes}"
                      </p>
                    )}
                    <div className="flex items-center space-x-2 mt-2">
                      <Badge variant={getRecommendationTypeVariant(recommendation.recommendation_type)}>
                        {recommendation.recommendation_type}
                      </Badge>
                      <Badge variant="outline">
                        {t('priorityNumber', { number: recommendation.featured_priority })}
                      </Badge>
                      {!recommendation.menu_item.is_available && (
                        <Badge variant="destructive">{t('unavailable')}</Badge>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => updatePriority(recommendation.id, 'up')}
                    disabled={updateRecommendationMutation.isPending}
                  >
                    <ArrowUp className="h-3 w-3" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => updatePriority(recommendation.id, 'down')}
                    disabled={updateRecommendationMutation.isPending}
                  >
                    <ArrowDown className="h-3 w-3" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => toggleFeatured(recommendation.id, recommendation.is_featured)}
                    disabled={updateRecommendationMutation.isPending}
                  >
                    {recommendation.is_featured ? (
                      <EyeOff className="h-3 w-3" />
                    ) : (
                      <Eye className="h-3 w-3" />
                    )}
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setEditingItem(recommendation)}
                  >
                    <Edit className="h-3 w-3" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => deleteRecommendationMutation.mutate(recommendation.id)}
                    disabled={deleteRecommendationMutation.isPending}
                    className="text-red-600 border-red-200 hover:bg-red-50"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </motion.div>
            ))}
          </div>
        )}

        {/* Edit Dialog */}
        <Dialog open={!!editingItem} onOpenChange={(open) => !open && setEditingItem(null)}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Edit Recommendation</DialogTitle>
              <DialogDescription>
                Update the recommendation settings for {editingItem?.menu_item.name}
              </DialogDescription>
            </DialogHeader>
            {editingItem && (
              <RecommendationForm
                initialData={editingItem}
                menuItems={menuItems}
                onSubmit={(data) => updateRecommendationMutation.mutate({ ...data, id: editingItem.id })}
                onCancel={() => setEditingItem(null)}
                isEditing
              />
            )}
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};

// Customer View Component
const ChefRecommendationsCustomer: React.FC<{ 
  onAddToCart?: (menuItemId: string) => void;
  className?: string;
}> = ({ onAddToCart, className }) => {
  const { restaurantInfo } = useRestaurant();
  const { t } = useLanguage();

  // Fetch featured recommendations for customers
  const { data: recommendations = [], isLoading } = useQuery({
    queryKey: ['featured-recommendations', restaurantInfo?.id],
    queryFn: async () => {
      if (!restaurantInfo?.id) return [];

      const { data, error } = await supabase
        .from('chef_recommendations')
        .select(`
          *,
          menu_item:menu_items!inner (
            id,
            name,
            description,
            category,
            current_price,
            base_price,
            image_url,
            is_available
          )
        `)
        .eq('restaurant_id', restaurantInfo.id)
        .eq('is_featured', true)
        .eq('menu_item.is_available', true)
        .order('featured_priority', { ascending: true });

      if (error) throw error;
      return data as ChefRecommendation[];
    },
    enabled: !!restaurantInfo?.id
  });

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="h-5 w-5 animate-spin mr-2" />
            <span>Loading chef's recommendations...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (recommendations.length === 0) {
    return null; // Don't show anything if no recommendations
  }

  return (
    <Card className={`bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200 ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-yellow-800">
          <ChefHat className="h-5 w-5" />
          Chef's Recommendations
        </CardTitle>
        <CardDescription className="text-yellow-700">
          Hand-picked favorites from our chef
        </CardDescription>
      </CardHeader>

      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {recommendations.map((recommendation) => (
            <motion.div
              key={recommendation.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-lg border border-yellow-200 p-4 hover:shadow-md transition-shadow"
            >
              <div className="relative mb-3">
                {recommendation.menu_item.image_url ? (
                  <img
                    src={recommendation.menu_item.image_url}
                    alt={recommendation.menu_item.name}
                    className="w-full h-32 object-cover rounded-lg"
                  />
                ) : (
                  <div className="w-full h-32 bg-gray-200 rounded-lg flex items-center justify-center">
                    <ChefHat className="h-8 w-8 text-gray-400" />
                  </div>
                )}
                <Badge className="absolute top-2 right-2 bg-yellow-500">
                  <Star className="h-3 w-3 mr-1" />
                  Chef's Choice
                </Badge>
              </div>

              <h4 className="font-semibold text-gray-900 mb-1">
                {recommendation.menu_item.name}
              </h4>
              
              {recommendation.menu_item.description && (
                <p className="text-sm text-gray-600 mb-2">
                  {recommendation.menu_item.description}
                </p>
              )}

              {recommendation.chef_notes && (
                <div className="bg-yellow-50 border border-yellow-200 rounded p-2 mb-3">
                  <p className="text-sm text-yellow-800 italic">
                    "{recommendation.chef_notes}"
                  </p>
                  <p className="text-xs text-yellow-600 mt-1">- Chef's Note</p>
                </div>
              )}

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className="text-lg font-bold text-gray-900">
                    €{recommendation.menu_item.current_price.toFixed(2)}
                  </span>
                  <Badge variant={getRecommendationTypeVariant(recommendation.recommendation_type)}>
                    {recommendation.recommendation_type}
                  </Badge>
                </div>

                {onAddToCart && (
                  <Button
                    size="sm"
                    onClick={() => onAddToCart(recommendation.menu_item.id)}
                    className="bg-yellow-600 hover:bg-yellow-700"
                  >
                    <Plus className="h-3 w-3 mr-1" />
                    Add
                  </Button>
                )}
              </div>
            </motion.div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

// Recommendation Form Component
const RecommendationForm: React.FC<{
  initialData?: ChefRecommendation;
  menuItems: MenuItemOption[];
  onSubmit: (data: Partial<ChefRecommendation>) => void;
  onCancel: () => void;
  isEditing?: boolean;
}> = ({ initialData, menuItems, onSubmit, onCancel, isEditing = false }) => {
  const { t } = useLanguage();
  const [formData, setFormData] = useState({
    menu_item_id: initialData?.menu_item_id || '',
    is_featured: initialData?.is_featured || true,
    featured_priority: initialData?.featured_priority || 1,
    chef_notes: initialData?.chef_notes || '',
    recommendation_type: initialData?.recommendation_type || 'signature' as const
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {!isEditing && (
        <div>
          <Label htmlFor="menu_item_id">{t('menuItem')}</Label>
          <Select
            value={formData.menu_item_id}
            onValueChange={(value) => setFormData(prev => ({ ...prev, menu_item_id: value }))}
          >
            <SelectTrigger>
              <SelectValue placeholder={t('selectMenuItemPlaceholder')} />
            </SelectTrigger>
            <SelectContent>
              {menuItems.map((item) => (
                <SelectItem key={item.id} value={item.id}>
                  {item.name} ({item.category}) - €{item.current_price.toFixed(2)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      <div>
        <Label htmlFor="recommendation_type">{t('recommendationType')}</Label>
        <Select
          value={formData.recommendation_type}
          onValueChange={(value: any) => setFormData(prev => ({ ...prev, recommendation_type: value }))}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="signature">{t('signature')}</SelectItem>
            <SelectItem value="popular">{t('popular')}</SelectItem>
            <SelectItem value="seasonal">{t('seasonal')}</SelectItem>
            <SelectItem value="special">{t('special')}</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="featured_priority">{t('priorityLabel')}</Label>
        <Input
          type="number"
          min="1"
          value={formData.featured_priority}
          onChange={(e) => setFormData(prev => ({ ...prev, featured_priority: parseInt(e.target.value) || 1 }))}
        />
      </div>

      <div>
        <Label htmlFor="chef_notes">{t('chefNotesLabel')}</Label>
        <Textarea
          placeholder={t('chefNotesPlaceholder')}
          value={formData.chef_notes}
          onChange={(e) => setFormData(prev => ({ ...prev, chef_notes: e.target.value }))}
        />
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="is_featured"
          checked={formData.is_featured}
          onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_featured: checked }))}
        />
        <Label htmlFor="is_featured">{t('featuredVisibleToCustomers')}</Label>
      </div>

      <DialogFooter>
        <Button type="button" variant="outline" onClick={onCancel}>
          {t('cancel')}
        </Button>
        <Button type="submit">
          {isEditing ? t('updateRecommendation') : t('addRecommendation')}
        </Button>
      </DialogFooter>
    </form>
  );
};

// Utility function for badge variants
const getRecommendationTypeVariant = (type: string) => {
  switch (type) {
    case 'signature': return 'default';
    case 'popular': return 'secondary';
    case 'seasonal': return 'outline';
    case 'special': return 'destructive';
    default: return 'default';
  }
};

// Main Component
export const ChefRecommendationsComplete: React.FC<ChefRecommendationsProps> = ({
  mode,
  onAddToCart,
  className
}) => {
  if (mode === 'admin') {
    return <ChefRecommendationsAdmin className={className} />;
  }

  return (
    <ChefRecommendationsCustomer
      onAddToCart={onAddToCart}
      className={className}
    />
  );
};

export default ChefRecommendationsComplete;