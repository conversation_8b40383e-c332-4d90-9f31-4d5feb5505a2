/**
 * <PERSON><PERSON>
 * Informs users about data storage and tracking for order history
 */

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/contexts/LanguageContext';
import { storeTableSession, isTableSessionValid, getCurrentTableId } from '../services/tableSessionService';
import { getRestaurantId } from '../services/restaurantDbService';

const CONSENT_KEY = 'menu_flow_cookie_consent';

export function CookieConsent() {
  const { t } = useLanguage();
  const [show, setShow] = useState(false);
  
  useEffect(() => {
    // Check if user has already given consent
    const hasConsent = localStorage.getItem(CONSENT_KEY) === 'true';
    
    // Only show banner if consent hasn't been given
    if (!hasConsent) {
      // Small delay to avoid showing immediately on page load
      const timer = setTimeout(() => {
        setShow(true);
      }, 1000);
      
      return () => clearTimeout(timer);
    }
  }, []);
  
  const updateTableSession = async (tableIdValue: string) => {
    try {
      // Only store if we have a valid table ID
      if (tableIdValue) {
        const restaurantId = await getRestaurantId();
        if (restaurantId) {
          storeTableSession(tableIdValue, restaurantId);
        }
      }
    } catch (error) {
      console.error('Error updating table session:', error);
    }
  };

  const acceptConsent = async () => {
    localStorage.setItem(CONSENT_KEY, 'true');
    const tableId = await getCurrentTableId();
    if (tableId) {
      await updateTableSession(tableId);
    }
    setShow(false);
  };
  
  if (!show) {
    return null;
  }
  
  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 p-4 shadow-lg border-t z-50 animate-fadeIn">
      <div className="container mx-auto max-w-4xl flex flex-col sm:flex-row justify-between items-center gap-4">
        <p className="text-sm text-gray-600 dark:text-gray-300">
          {t('cookieConsentMessage')}
        </p>
        
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={() => setShow(false)}
            size="sm"
          >
            {t('decline')}
          </Button>
          
          <Button 
            className="bg-restaurant-primary hover:bg-restaurant-primary/90"
            onClick={acceptConsent}
            size="sm"
          >
            {t('accept')}
          </Button>
        </div>
      </div>
    </div>
  );
}
