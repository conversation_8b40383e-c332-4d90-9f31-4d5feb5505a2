import React from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Lock, Crown, Zap } from 'lucide-react';
import { useROSFeatureAccess } from '@/hooks/useROSFeatureAccess';
import { useROSSubscription } from '@/hooks/useROSSubscription';
import { useLanguage } from '@/contexts/LanguageContext';
import { PREMIUM_PRICE_ID } from '@/lib/stripe';

interface FeatureGateProps {
  feature: keyof ReturnType<typeof useROSFeatureAccess>;
  fallback?: React.ReactNode;
  children: React.ReactNode;
  showUpgrade?: boolean;
  customMessage?: string;
}

export const FeatureGate: React.FC<FeatureGateProps> = ({
  feature,
  fallback,
  children,
  showUpgrade = true,
  customMessage
}) => {
  const featureAccess = useROSFeatureAccess();
  const { createCheckoutSession, isCreatingCheckout, subscription } = useROSSubscription();
  
  const hasAccess = featureAccess[feature] as boolean;
  const isTrialing = featureAccess.isTrialActive;
  const isTrialDynamicPricing = feature === 'canUseDynamicPricing' && isTrialing;
  
  if (hasAccess) {
    return <>{children}</>;
  }

  // If no access, show fallback or upgrade prompt
  if (fallback) {
    return <>{fallback}</>;
  }

  const getFeatureInfo = (featureName: string) => {
    const featureMap: Record<string, { name: string; requiredPlan: string; icon: React.ReactNode }> = {
      canUseDynamicPricing: {
        name: 'Dynamic Pricing',
        requiredPlan: 'Premium',
        icon: <Zap className="h-4 w-4" />
      },
      canAccessAdvancedAnalytics: {
        name: 'Advanced Analytics',
        requiredPlan: 'Premium', 
        icon: <Crown className="h-4 w-4" />
      },
      canManageMultipleRestaurants: {
        name: 'Multi-Restaurant Management',
        requiredPlan: 'Premium',
        icon: <Crown className="h-4 w-4" />
      },
      canIntegratePOS: {
        name: 'POS Integration',
        requiredPlan: 'Premium',
        icon: <Crown className="h-4 w-4" />
      },
      canExportData: {
        name: 'Data Export',
        requiredPlan: 'Basic',
        icon: <Lock className="h-4 w-4" />
      }
    };

    return featureMap[featureName] || {
      name: featureName,
      requiredPlan: 'Basic',
      icon: <Lock className="h-4 w-4" />
    };
  };

  const featureInfo = getFeatureInfo(feature);
  const currentPlan = featureAccess.subscriptionTier;

  const handleUpgrade = async () => {
    try {
      // Import the correct price IDs from lib/stripe
      const { BASIC_PRICE_ID, PREMIUM_PRICE_ID } = await import('@/lib/stripe');
      
      // For premium features, redirect to premium plan
      const priceId = featureInfo.requiredPlan === 'Premium' 
        ? PREMIUM_PRICE_ID // €69.99/month
        : BASIC_PRICE_ID; // €14.99/month
        
      await createCheckoutSession(priceId);
    } catch (error) {
      console.error('Error creating checkout session:', error);
    }
  };

  return (
    <Alert className={isTrialDynamicPricing ? "border-green-200 bg-green-50" : "border-amber-200 bg-amber-50"}>
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          {featureInfo.icon}
        </div>
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <h4 className={`font-semibold ${isTrialDynamicPricing ? 'text-green-800' : 'text-amber-800'}`}>
              {isTrialDynamicPricing ? `${featureInfo.name} - Trial Access` : `${featureInfo.name} - Premium Feature`}
            </h4>
            <Badge 
              variant="outline" 
              className={isTrialDynamicPricing ? "border-green-300 text-green-700" : "border-amber-300 text-amber-700"}
            >
              {isTrialDynamicPricing ? 'Free Trial' : `${featureInfo.requiredPlan} Plan Required`}
            </Badge>
          </div>
          
          <AlertDescription className={`${isTrialDynamicPricing ? 'text-green-700' : 'text-amber-700'} mb-3`}>
            {customMessage || (
              <>
                {isTrialDynamicPricing ? (
                  `🎉 You're experiencing ${featureInfo.name} during your free trial! This premium feature normally requires a Premium subscription (€69.99/month). See how it can boost your revenue, then upgrade to keep it after your trial ends.`
                ) : isTrialing ? (
                  `This feature requires a ${featureInfo.requiredPlan} subscription. Your trial includes basic features only.`
                ) : (
                  `Upgrade to ${featureInfo.requiredPlan} to unlock ${featureInfo.name} and other advanced features.`
                )}
              </>
            )}
          </AlertDescription>

          {showUpgrade && (
            <div className="flex items-center gap-3">
              <Button 
                onClick={handleUpgrade}
                disabled={isCreatingCheckout}
                size="sm"
                className={isTrialDynamicPricing ? "bg-green-600 hover:bg-green-700" : "bg-amber-600 hover:bg-amber-700"}
              >
                {isCreatingCheckout ? 'Loading...' : 
                 isTrialDynamicPricing ? 'Upgrade to Keep This Feature' : 
                 `Upgrade to ${featureInfo.requiredPlan}`}
              </Button>
              
              {subscription && (
                <div className="text-sm text-amber-600">
                  Current: {currentPlan === 'trial' ? 'Free Trial' : `${currentPlan} Plan`}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </Alert>
  );
};

// Convenience components for specific features
export const DynamicPricingGate: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <FeatureGate feature="canUseDynamicPricing">
    {children}
  </FeatureGate>
);

export const AdvancedAnalyticsGate: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <FeatureGate feature="canAccessAdvancedAnalytics">
    {children}
  </FeatureGate>
);

export const MultiRestaurantGate: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <FeatureGate feature="canManageMultipleRestaurants">
    {children}
  </FeatureGate>
);

export const POSIntegrationGate: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <FeatureGate feature="canIntegratePOS">
    {children}
  </FeatureGate>
);

export const DataExportGate: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <FeatureGate feature="canExportData">
    {children}
  </FeatureGate>
);

// Usage limit checker component with trial awareness
export const UsageLimitGate: React.FC<{
  currentCount: number;
  maxCount: number;
  featureName: string;
  children: React.ReactNode;
  showUpgrade?: boolean;
}> = ({ currentCount, maxCount, featureName, children, showUpgrade = true }) => {
  const { createCheckoutSession, isCreatingCheckout, subscription } = useROSSubscription();
  const { language } = useLanguage();
  
  const isTrialing = subscription?.status === 'trialing';
  const basicLimits = featureName === 'Tables' ? 8 : featureName === 'Menu Items' ? 12 : maxCount;
  
  // Show trial warning when approaching Basic limits during trial
  const showTrialWarning = isTrialing && currentCount >= basicLimits && currentCount < maxCount;
  
  // Translations for trial warnings
  const trialWarningTranslations = {
    en: {
      title: "⚠️ Trial Access Warning",
      warning: (feature: string, current: number, basic: number, excess: number) => 
        `You have ${current} ${feature.toLowerCase()}, but Basic plan only includes ${basic}. After your trial ends, ${excess} ${feature.toLowerCase()} will be hidden until you upgrade to Premium.`,
      trialInfo: (current: number, max: number, basic: number) => 
        `Trial: ${current} / ${max} • Basic: ${basic} limit`,
      secureButton: "Secure Premium Access",
      limitReached: `${featureName} Limit Reached`,
      limitMessage: (max: number, feature: string) => 
        `You've reached your limit of ${max} ${feature.toLowerCase()}. Upgrade to Premium (€69.99/month) for unlimited access.`,
      upgradeButton: "Upgrade to Premium",
      loading: "Loading...",
      current: "Current"
    },
    es: {
      title: "⚠️ Advertencia de Acceso de Prueba",
      warning: (feature: string, current: number, basic: number, excess: number) => {
        const featureSpanish = feature === 'Tables' ? 'mesas' : feature === 'Menu Items' ? 'elementos del menú' : feature.toLowerCase();
        return `Tienes ${current} ${featureSpanish}, pero el plan Básico solo incluye ${basic}. Después de que termine tu prueba, ${excess} ${featureSpanish} se ocultarán hasta que actualices a Premium.`;
      },
      trialInfo: (current: number, max: number, basic: number) => 
        `Prueba: ${current} / ${max} • Básico: límite de ${basic}`,
      secureButton: "Asegurar Acceso Premium",
      limitReached: `Límite de ${featureName === 'Tables' ? 'Mesas' : featureName === 'Menu Items' ? 'Elementos del Menú' : featureName} Alcanzado`,
      limitMessage: (max: number, feature: string) => {
        const featureSpanish = feature === 'Tables' ? 'mesas' : feature === 'Menu Items' ? 'elementos del menú' : feature.toLowerCase();
        return `Has alcanzado tu límite de ${max} ${featureSpanish}. Actualiza a Premium (€69.99/mes) para acceso ilimitado.`;
      },
      upgradeButton: "Actualizar a Premium",
      loading: "Cargando...",
      current: "Actual"
    }
  };
  
  const t = trialWarningTranslations[language] || trialWarningTranslations.en;
  
  // -1 means unlimited
  if (maxCount === -1 || currentCount < maxCount) {
    // Show warning during trial if they'll lose access to content after trial
    if (showTrialWarning) {
      return (
        <>
          {children}
          <div className="relative mt-4 overflow-hidden rounded-lg border border-amber-200/60 bg-gradient-to-r from-amber-50 to-yellow-50 shadow-sm">
            <div className="relative p-4">
              <div className="flex items-center space-x-3">
                {/* Icon */}
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-br from-amber-500 to-yellow-500 shadow-sm">
                  <Crown className="h-4 w-4 text-white" />
                </div>
                
                {/* Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-sm font-semibold text-amber-900 mb-1">
                        ⚠️ Trial Access Warning
                      </h4>
                      <p className="text-xs text-amber-800 leading-relaxed">
                        You have {currentCount} {featureName.toLowerCase()}, but Basic plan only includes {basicLimits}. After trial: {currentCount - basicLimits} will be hidden.
                      </p>
                    </div>
                    
                    {/* Stats & Action */}
                    <div className="flex items-center space-x-3">
                      <div className="text-center px-2">
                        <div className="text-xs text-amber-600">Usage</div>
                        <div className="text-sm font-bold text-amber-900">{currentCount}/{basicLimits}</div>
                      </div>
                      
                      {showUpgrade && (
                        <Button 
                          onClick={() => createCheckoutSession(PREMIUM_PRICE_ID)}
                          disabled={isCreatingCheckout}
                          size="sm"
                          className="bg-gradient-to-r from-amber-600 to-yellow-600 hover:from-amber-700 hover:to-yellow-700 text-white border-0 text-xs px-3 py-1.5"
                        >
                          {isCreatingCheckout ? (
                            <div className="flex items-center space-x-1">
                              <div className="h-3 w-3 animate-spin rounded-full border border-white/30 border-t-white" />
                              <span>Loading</span>
                            </div>
                          ) : (
                            <div className="flex items-center space-x-1">
                              <Crown className="h-3 w-3" />
                              <span>Upgrade €69.99/mo</span>
                            </div>
                          )}
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </>
      );
    }
    
    return <>{children}</>;
  }

  const handleUpgrade = async () => {
    try {
      const { PREMIUM_PRICE_ID } = await import('@/lib/stripe');
      await createCheckoutSession(PREMIUM_PRICE_ID); // Upgrade to premium for unlimited
    } catch (error) {
      console.error('Error creating checkout session:', error);
    }
  };

  return (
    <div className="rounded-xl border border-red-200/60 bg-gradient-to-r from-red-50/80 to-rose-50/80 shadow-sm overflow-hidden">
      {/* Subtle background pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-red-100/20 to-transparent" />
      
      <div className="relative px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Compact icon and title */}
          <div className="flex items-center space-x-3">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-red-500 to-rose-500 shadow-sm">
              <Lock className="h-4 w-4 text-white" />
            </div>
            <div className="flex-1">
              <h4 className="text-sm font-semibold text-red-900">
                {t.limitReached}
              </h4>
              <p className="text-xs text-red-700/80 mt-0.5">
                {t.limitMessage(maxCount, featureName)}
              </p>
            </div>
          </div>
          
          {/* Compact usage indicator */}
          <div className="flex items-center space-x-3">
            <div className="text-right">
              <div className="text-xs font-medium text-red-600">Usage</div>
              <div className="text-sm font-bold text-red-900">{currentCount}/{maxCount}</div>
            </div>
            
            {/* Status badge */}
            <div className="flex items-center space-x-1 px-2 py-1 bg-red-100/80 rounded-full">
              <div className="h-1.5 w-1.5 rounded-full bg-red-500" />
              <span className="text-xs font-medium text-red-700">Limit</span>
            </div>
          </div>
        </div>
        
        {/* Compact upgrade button */}
        {showUpgrade && (
          <div className="mt-3 pt-3 border-t border-red-200/40">
            <Button 
              onClick={handleUpgrade}
              disabled={isCreatingCheckout}
              size="sm"
              className="w-full bg-gradient-to-r from-red-600 to-rose-600 hover:from-red-700 hover:to-rose-700 text-white text-xs font-medium py-2 rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
            >
              {isCreatingCheckout ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="h-3 w-3 animate-spin rounded-full border border-white/30 border-t-white" />
                  <span>{t.loading}</span>
                </div>
              ) : (
                <div className="flex items-center justify-center space-x-2">
                  <Crown className="h-3 w-3" />
                  <span>{t.upgradeButton}</span>
                </div>
              )}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};