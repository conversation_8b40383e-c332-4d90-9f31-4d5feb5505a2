import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Plus, Minus, Leaf, Heart, Wheat, Droplets, Nut, Flame } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { ThemeAwareCard } from '@/components/theme/ThemeAwareCard';
import { ThemeAwareButton } from '@/components/theme/ThemeAwareButton';
import { useThemeStyles } from '@/hooks/useRestaurantTheme';

export interface MenuItemType {
  id: string;
  name: string;
  description: string;
  price: number;
  image?: string;
  category: string;
  allergies?: string[];
  calories?: number;
  // Dynamic pricing related fields
  base_price?: number;         // Original price before dynamic pricing
  price_factor?: number;       // Factor applied to the base price
  original_price?: number;     // Alias for base_price in some contexts
  isDynamicallyPriced?: boolean; // Whether this item's price is dynamically adjusted
  // Dietary options
  is_vegan?: boolean;
  is_vegetarian?: boolean;
  is_gluten_free?: boolean;
  is_dairy_free?: boolean;
  is_nut_free?: boolean;
  spice_level?: number;
  dietary_notes?: string;
}

interface MenuItemProps {
  item: MenuItemType;
  onAddToCart: (item: MenuItemType) => void;
  quantity?: number;
  onIncreaseQuantity?: () => void;
  onDecreaseQuantity?: () => void;
}

const MenuItem: React.FC<MenuItemProps> = ({
  item,
  onAddToCart,
  quantity,
  onIncreaseQuantity,
  onDecreaseQuantity
}) => {
  const { t } = useLanguage();
  const { getColorValue, getThemeTailwindClasses } = useThemeStyles();
  const { 
    name, 
    description, 
    price, 
    image, 
    allergies, 
    calories,
    base_price,
    price_factor,
    original_price,
    isDynamicallyPriced,
    is_vegan,
    is_vegetarian,
    is_gluten_free,
    is_dairy_free,
    is_nut_free,
    spice_level,
    dietary_notes
  } = item;

  const showPriceComparison = base_price && price !== base_price;
  const priceChangeIndicator = showPriceComparison ? (price < base_price ? t('menuItem.priceDecreased') : t('menuItem.priceIncreased')) : '';
  const priceChangeColor = showPriceComparison ? (price < base_price ? '#22c55e' : '#ef4444') : getColorValue('textSecondary');

  return (
    <ThemeAwareCard 
      variant="menu-item" 
      className="group relative overflow-hidden transition-all duration-300 hover:shadow-lg"
    >
      {/* Image Section */}
      {image && (
        <div className="relative h-48 overflow-hidden rounded-t-lg">
          <img 
            src={image} 
            alt={name}
            className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
          />
          {isDynamicallyPriced && (
            <div 
              className="absolute top-2 right-2 px-2 py-1 rounded-full text-xs font-medium text-white"
              style={{ backgroundColor: getColorValue('accent') }}
            >
              {t('menuItem.dynamicPrice')}
            </div>
          )}
        </div>
      )}

      {/* Content Section */}
      <div className="p-4 space-y-3">
        {/* Title and Price */}
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <h3 
              className="font-semibold text-lg leading-tight"
              style={{ color: getColorValue('textPrimary') }}
            >
              {name}
            </h3>
            {description && (
              <p 
                className="text-sm mt-1 leading-relaxed"
                style={{ color: getColorValue('textSecondary') }}
              >
                {description}
              </p>
            )}
          </div>
          
          <div className="ml-4 text-right">
            <div 
              className="text-xl font-bold"
              style={{ color: getColorValue('primary') }}
            >
              €{price.toFixed(2)}
            </div>
            {showPriceComparison && (
              <div className="text-xs space-y-1">
                <div 
                  className="line-through"
                  style={{ color: getColorValue('textSecondary') }}
                >
                  €{(original_price || base_price)?.toFixed(2)}
                </div>
                <div 
                  className="font-medium"
                  style={{ color: priceChangeColor }}
                >
                  {priceChangeIndicator}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Dietary Information */}
        {(is_vegan || is_vegetarian || is_gluten_free || is_dairy_free || is_nut_free || spice_level) && (
          <div className="flex flex-wrap gap-2">
            {is_vegan && (
              <span 
                className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium"
                style={{ 
                  backgroundColor: `${getColorValue('accent')}20`,
                  color: getColorValue('accent')
                }}
              >
                <Leaf className="w-3 h-3" />
                {t('menuItem.vegan')}
              </span>
            )}
            {is_vegetarian && !is_vegan && (
              <span 
                className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium"
                style={{ 
                  backgroundColor: `${getColorValue('secondary')}20`,
                  color: getColorValue('secondary')
                }}
              >
                <Heart className="w-3 h-3" />
                {t('menuItem.vegetarian')}
              </span>
            )}
            {is_gluten_free && (
              <span 
                className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium"
                style={{ 
                  backgroundColor: `${getColorValue('primary')}20`,
                  color: getColorValue('primary')
                }}
              >
                <Wheat className="w-3 h-3" />
                {t('menuItem.glutenFree')}
              </span>
            )}
            {is_dairy_free && (
              <span 
                className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium"
                style={{ 
                  backgroundColor: `${getColorValue('secondary')}20`,
                  color: getColorValue('secondary')
                }}
              >
                <Droplets className="w-3 h-3" />
                {t('menuItem.dairyFree')}
              </span>
            )}
            {is_nut_free && (
              <span 
                className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium"
                style={{ 
                  backgroundColor: `${getColorValue('primary')}20`,
                  color: getColorValue('primary')
                }}
              >
                <Nut className="w-3 h-3" />
                {t('menuItem.nutFree')}
              </span>
            )}
            {spice_level && spice_level > 0 && (
              <span 
                className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium"
                style={{ 
                  backgroundColor: `${getColorValue('accent')}20`,
                  color: getColorValue('accent')
                }}
              >
                <Flame className="w-3 h-3" />
                {'🌶️'.repeat(Math.min(spice_level, 3))}
              </span>
            )}
          </div>
        )}

        {/* Additional Info */}
        <div className="flex justify-between items-center text-sm">
          <div className="flex gap-4">
            {calories && (
              <span style={{ color: getColorValue('textSecondary') }}>
                {calories} {t('menuItem.cal')}
              </span>
            )}
            {allergies && allergies.length > 0 && (
              <span style={{ color: getColorValue('textSecondary') }}>
                {t('menuItem.allergies')}: {allergies.join(', ')}
              </span>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-between pt-2">
          {quantity && quantity > 0 ? (
            <div className="flex items-center gap-3">
              <ThemeAwareButton
                variant="outline"
                size="sm"
                onClick={onDecreaseQuantity}
                className="w-8 h-8 p-0 rounded-full"
              >
                <Minus className="w-4 h-4" />
              </ThemeAwareButton>
              
              <span 
                className="font-semibold text-lg min-w-[2rem] text-center"
                style={{ color: getColorValue('textPrimary') }}
              >
                {quantity}
              </span>
              
              <ThemeAwareButton
                variant="primary"
                size="sm"
                onClick={onIncreaseQuantity}
                className="w-8 h-8 p-0 rounded-full"
              >
                <Plus className="w-4 h-4" />
              </ThemeAwareButton>
            </div>
          ) : (
            <ThemeAwareButton
              variant="primary"
              onClick={() => onAddToCart(item)}
              className="flex items-center gap-2 flex-1"
            >
              <Plus className="w-4 h-4" />
              {t('menuItem.addToCart')}
            </ThemeAwareButton>
          )}
        </div>

        {/* Dynamic Pricing Info */}
        {isDynamicallyPriced && (
          <div 
            className="mt-3 p-2 rounded-md border-l-4 text-xs"
            style={{ 
              backgroundColor: `${getColorValue('primary')}10`,
              borderLeftColor: getColorValue('primary'),
              color: getColorValue('textSecondary')
            }}
          >
            <div className="font-medium" style={{ color: getColorValue('textPrimary') }}>
              {t('menuItem.dynamicPricingInfo')}
            </div>
            <div className="mt-1">
              {t('menuItem.priceAdjustedBy')} {((price_factor || 1) - 1) * 100 > 0 ? '+' : ''}{(((price_factor || 1) - 1) * 100).toFixed(0)}%
            </div>
          </div>
        )}

        {/* Dietary Notes */}
        {dietary_notes && (
          <div 
            className="mt-2 text-xs italic"
            style={{ color: getColorValue('textSecondary') }}
          >
            {dietary_notes}
          </div>
        )}
      </div>
    </ThemeAwareCard>
  );
};

export default MenuItem;
