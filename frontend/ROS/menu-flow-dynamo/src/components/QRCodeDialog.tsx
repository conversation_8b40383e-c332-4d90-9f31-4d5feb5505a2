import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { QRCodeSVG } from 'qrcode.react';
import { Download } from 'lucide-react';

interface QRCodeDialogProps {
  isOpen: boolean;
  onClose: () => void;
  table: any;
}

const QRCodeDialog: React.FC<QRCodeDialogProps> = ({ isOpen, onClose, table }) => {
  const { toast } = useToast();

  const downloadQRCode = () => {
    try {
      // Get the SVG element
      const svgElement = document.getElementById('table-qrcode');
      if (!svgElement) {
        console.error('QR code SVG element not found');
        return;
      }
      
      // Create a canvas element
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        console.error('Could not get canvas context');
        return;
      }
      
      // Set canvas dimensions to match the QR code size
      canvas.width = 200;
      canvas.height = 200;
      
      // Create an image from the SVG
      const img = new Image();
      const svgData = new XMLSerializer().serializeToString(svgElement);
      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
      const url = URL.createObjectURL(svgBlob);
      
      img.onload = () => {
        // Draw the image on the canvas
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
        
        // Convert canvas to data URL
        const pngUrl = canvas.toDataURL('image/png');
        
        // Create download link
        const downloadLink = document.createElement('a');
        downloadLink.href = pngUrl;
        downloadLink.download = `table-${table?.table_number}-qrcode.png`;
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
        
        // Clean up
        URL.revokeObjectURL(url);
      };
      
      img.src = url;
      
      toast({
        title: "QR Code Download",
        description: "Your QR code is being downloaded.",
      });
    } catch (error) {
      console.error('Error downloading QR code:', error);
      toast({
        variant: "destructive",
        title: "Download Failed",
        description: "Could not download the QR code. Please try again.",
      });
    }
  };

  if (!table) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            QR Code for Table {table.table_number}
          </DialogTitle>
        </DialogHeader>

        <div className="flex flex-col items-center space-y-4 py-4">
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <QRCodeSVG
              id="table-qrcode"
              value={`${window.location.origin}/menu?table=${table.id}`}
              size={200}
              level="H"
              includeMargin={true}
              bgColor="#FFFFFF"
              fgColor="#000000"
            />
          </div>
          <div className="text-center space-y-2">
            <p className="font-medium">Table {table.table_number}</p>
            <p className="text-sm text-muted-foreground">
              Scan this QR code to access the menu
            </p>
            <p className="text-xs text-muted-foreground break-all">
              URL: {window.location.origin}/menu?table={table.id}
            </p>
          </div>
          <div className="flex gap-3 mt-2">
            <Button onClick={downloadQRCode} className="mt-4">
              <Download className="h-4 w-4 mr-2" /> Download QR Code
            </Button>
            <Button onClick={onClose} variant="outline" className="mt-4">
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default QRCodeDialog;
