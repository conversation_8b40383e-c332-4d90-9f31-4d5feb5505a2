import React from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { AlertTriangle, Crown } from 'lucide-react';
import { useROSSubscription } from '@/hooks/useROSSubscription';
import { useLanguage } from '@/contexts/LanguageContext';
import { PREMIUM_PRICE_ID } from '@/lib/stripe';

interface TrialImpactAlertProps {
  currentMenuItems: number;
  currentTables: number;
  className?: string;
}

export const TrialImpactAlert: React.FC<TrialImpactAlertProps> = ({
  currentMenuItems,
  currentTables,
  className = ""
}) => {
  const { subscription, createCheckoutSession, isCreatingCheckout, daysRemainingInTrial } = useROSSubscription();
  const { language } = useLanguage();
  
  // Only show for trial users who have exceeded Basic limits
  const isTrialing = subscription?.status === 'trialing';
  const exceedsBasicLimits = currentMenuItems > 12 || currentTables > 8;
  
  if (!isTrialing || !exceedsBasicLimits) {
    return null;
  }
  
  const menuItemsToLose = Math.max(0, currentMenuItems - 12);
  const tablesToLose = Math.max(0, currentTables - 8);
  
  // Translations
  const translations = {
    en: {
      title: `🚨 Trial Ending in ${daysRemainingInTrial} Days - You'll Lose Access`,
      subtitle: "When your trial expires, Basic plan (€14.99/month) will hide:",
      menuItems: "menu items",
      tables: "tables",
      youHave: "you have",
      basicAllows: "Basic allows",
      dynamicPricing: "Dynamic Pricing",
      advancedAnalytics: "Advanced Analytics",
      premiumFeatureOnly: "Premium feature only",
      contentNote: "Your content won't be deleted, but customers won't see it until you upgrade.",
      buttonText: "Secure Premium Access - €69.99/mo",
      loading: "Loading...",
      keepAll: "Keep all"
    },
    es: {
      title: `🚨 Prueba termina en ${daysRemainingInTrial} días - Perderás el acceso`,
      subtitle: "Cuando expire tu prueba, el plan Básico (€14.99/mes) ocultará:",
      menuItems: "elementos del menú",
      tables: "mesas",
      youHave: "tienes",
      basicAllows: "Básico permite",
      dynamicPricing: "Precios Dinámicos",
      advancedAnalytics: "Análisis Avanzados",
      premiumFeatureOnly: "Solo función Premium",
      contentNote: "Tu contenido no se eliminará, pero los clientes no lo verán hasta que actualices.",
      buttonText: "Asegurar Acceso Premium - €69.99/mes",
      loading: "Cargando...",
      keepAll: "Mantener todos"
    }
  };
  
  const t = translations[language] || translations.en;
  
  return (
    <Alert className={`border-orange-200 bg-orange-50 ${className}`}>
      <div className="flex items-start space-x-3">
        <AlertTriangle className="h-5 w-5 text-orange-600 flex-shrink-0 mt-0.5" />
        <div className="flex-1">
          <h4 className="font-semibold text-orange-800 mb-2">
            {t.title}
          </h4>
          <AlertDescription className="text-orange-700 mb-4">
            <div className="space-y-2">
              <p className="font-medium">{t.subtitle}</p>
              <ul className="ml-4 space-y-1">
                {menuItemsToLose > 0 && (
                  <li>• <strong>{menuItemsToLose} {t.menuItems}</strong> ({t.youHave} {currentMenuItems}, {t.basicAllows} 12)</li>
                )}
                {tablesToLose > 0 && (
                  <li>• <strong>{tablesToLose} {t.tables}</strong> ({t.youHave} {currentTables}, {t.basicAllows} 8)</li>
                )}
                <li>• <strong>{t.dynamicPricing}</strong> ({t.premiumFeatureOnly})</li>
                <li>• <strong>{t.advancedAnalytics}</strong> ({t.premiumFeatureOnly})</li>
              </ul>
              <p className="mt-3 font-medium text-orange-800">
                {t.contentNote}
              </p>
            </div>
          </AlertDescription>
          
          <div className="flex items-center gap-3">
            <Button 
              onClick={() => createCheckoutSession(PREMIUM_PRICE_ID)}
              disabled={isCreatingCheckout}
              size="sm"
              className="bg-orange-600 hover:bg-orange-700"
            >
              <Crown className="h-4 w-4 mr-2" />
              {isCreatingCheckout ? t.loading : t.buttonText}
            </Button>
            
            <div className="text-xs text-orange-600">
              {t.keepAll} {currentMenuItems} {currentMenuItems === 1 ? t.menuItems.slice(0, -1) : t.menuItems} + {currentTables} {currentTables === 1 ? t.tables.slice(0, -1) : t.tables} + AI
            </div>
          </div>
        </div>
      </div>
    </Alert>
  );
};