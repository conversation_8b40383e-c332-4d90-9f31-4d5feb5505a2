import React, { useState, useEffect } from 'react';

interface ImageProxyProps {
  src: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
  fallbackSrc?: string;
}

/**
 * Component to handle images with MIME type issues
 * Converts images to base64 data URLs to bypass MIME type restrictions
 * Specifically designed to handle Supabase storage URLs
 */
const ImageProxy: React.FC<ImageProxyProps> = ({
  src,
  alt,
  className,
  width,
  height,
  fallbackSrc = 'https://placehold.co/200x200?text=Menu'
}) => {
  const [imageSrc, setImageSrc] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<boolean>(false);

  useEffect(() => {
    if (!src) {
      console.warn('ImageProxy: No source URL provided');
      setError(true);
      setIsLoading(false);
      return;
    }

    // Log the image URL we're trying to load
    console.log(`ImageProxy: Loading image from ${src}`);

    const fetchImage = async () => {
      try {
        // Add cache-busting parameter for Supabase URLs to avoid caching issues
        const urlWithCacheBusting = src.includes('supabase.co')
          ? `${src}?t=${new Date().getTime()}`
          : src;

        // Fetch the image directly as a blob

        const response = await fetch(urlWithCacheBusting, {
          // Add headers that might help with CORS issues
          headers: {
            'Accept': 'image/*',
          },
          // Include credentials if the URL is from the same origin
          credentials: 'same-origin',
          // Use no-cache to avoid browser cache issues
          cache: 'no-cache',
          // Add a timeout to avoid hanging requests
          signal: AbortSignal.timeout(10000) // 10 second timeout
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
        }

        // Get content type for debugging
        const contentType = response.headers.get('content-type');
        console.log(`ImageProxy: Image content type: ${contentType}`);

        // Convert to blob
        const imageBlob = await response.blob();
        console.log(`ImageProxy: Blob size: ${imageBlob.size} bytes, type: ${imageBlob.type}`);

        // If blob is empty or invalid, throw error
        if (imageBlob.size === 0) {
          throw new Error('Empty image blob received');
        }

        // Convert to base64 data URL
        const reader = new FileReader();
        reader.onloadend = () => {
          if (typeof reader.result === 'string') {
            console.log(`ImageProxy: Successfully loaded image, data URL length: ${reader.result.length}`);
            setImageSrc(reader.result);
            setIsLoading(false);
          }
        };
        reader.onerror = (e) => {
          console.error('ImageProxy: FileReader error:', e);
          setError(true);
          setIsLoading(false);
        };
        reader.readAsDataURL(imageBlob);
      } catch (error) {
        console.error(`ImageProxy: Error loading image from ${src}:`, error);
        setError(true);
        setIsLoading(false);

        // Try direct image loading as fallback
        const img = new Image();
        img.onload = () => {
          console.log('ImageProxy: Direct image loading succeeded as fallback');
          setError(false);
          setImageSrc(src);
          setIsLoading(false);
        };
        img.onerror = () => {
          console.error('ImageProxy: Both proxy and direct loading failed');
        };
        img.src = src;
      }
    };

    fetchImage();
  }, [src]);

  if (isLoading) {
    return (
      <div className={`${className || ''} flex items-center justify-center bg-gray-100`}>
        <span className="text-xs text-gray-500">Loading...</span>
      </div>
    );
  }

  if (error || !imageSrc) {
    // Try direct image as fallback
    return (
      <img
        src={fallbackSrc}
        alt={alt}
        className={className}
        width={width}
        height={height}
        onError={(e) => {
          console.error('ImageProxy: Fallback image also failed to load');
          e.currentTarget.style.display = 'none';
        }}
      />
    );
  }

  return (
    <img
      src={imageSrc}
      alt={alt}
      className={className}
      width={width}
      height={height}
      onError={(e) => {
        console.error('ImageProxy: Error displaying processed image');
        e.currentTarget.src = fallbackSrc;
      }}
    />
  );
};

export default ImageProxy;
