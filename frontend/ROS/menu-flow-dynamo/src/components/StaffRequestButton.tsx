import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { MessageSquare, Users, Utensils, HelpCircle } from 'lucide-react';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import { createServerNotification } from '@/services/notificationService';

interface StaffRequestButtonProps {
  tableId?: string;
  restaurantId: string;
  className?: string;
}

const StaffRequestButton: React.FC<StaffRequestButtonProps> = ({
  tableId,
  restaurantId,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [requestType, setRequestType] = useState<string>('');
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const requestTypes = [
    { value: 'assistance', label: 'General Assistance', icon: HelpCircle },
    { value: 'service', label: 'Table Service', icon: Users },
    { value: 'order', label: 'Order Issue', icon: Utensils },
    { value: 'other', label: 'Other', icon: MessageSquare }
  ];

  const handleSubmit = async () => {
    if (!requestType) {
      toast.error('Please select a request type');
      return;
    }

    setIsSubmitting(true);
    try {
      // Get table number for the notification
      let tableNumber = 'Unknown';
      if (tableId) {
        const { data: tableData } = await supabase
          .from('restaurant_tables')
          .select('table_number')
          .eq('id', tableId)
          .single();

        if (tableData) {
          tableNumber = tableData.table_number;
        }
      }

      // Check for existing pending requests first
      if (tableId) {
        const { data: existingRequests } = await supabase
          .from('staff_requests')
          .select('id')
          .eq('restaurant_id', restaurantId)
          .eq('table_id', tableId)
          .eq('status', 'pending')
          .limit(1);

        if (existingRequests && existingRequests.length > 0) {
          toast.error(`There's already a pending staff request for Table ${tableNumber}. Please wait for staff to respond.`);
          setIsOpen(false);
          return;
        }
      }

      // Create staff request in database
      const { data: staffRequestData, error: staffRequestError } = await supabase
        .from('staff_requests')
        .insert({
          restaurant_id: restaurantId,
          table_id: tableId,
          request_type: requestType,
          message: message,
          status: 'pending'
        })
        .select()
        .single();

      if (staffRequestError) {
        if (staffRequestError.code === '23505') { // Unique constraint violation
          toast.error(`There's already a pending staff request for Table ${tableNumber}. Please wait for staff to respond.`);
          setIsOpen(false);
          return;
        }
        console.error('Error creating staff request:', staffRequestError);
        throw new Error('Failed to create staff request');
      }

      // Create notification for admin
      try {
        await createServerNotification({
          type: 'staff_request',
          title: `Staff Request - Table ${tableNumber}`,
          message: message || `${requestTypes.find(t => t.value === requestType)?.label} requested at Table ${tableNumber}`,
          restaurant_id: restaurantId,
          action_label: 'View Request',
          action_url: `/admin/staff-requests`,
          priority: 'high',
          metadata: {
            staff_request_id: staffRequestData.id,
            table_id: tableId,
            table_number: tableNumber,
            request_type: requestType,
            customer_message: message
          }
        });

      } catch (notificationError) {
        console.error('Failed to create notification:', notificationError);
        // Don't fail the whole request if notification fails
      }

      toast.success('Staff request sent successfully! Our team will assist you shortly.');
      setIsOpen(false);
      setRequestType('');
      setMessage('');
    } catch (error) {
      console.error('Error sending staff request:', error);
      toast.error('Failed to send staff request. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Button
        variant="outline"
        className={`${className}`}
        onClick={() => setIsOpen(true)}
      >
        <MessageSquare className="h-4 w-4 mr-2" />
        Request Staff
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Request Staff Assistance</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="request-type">Type of Request</Label>
              <Select value={requestType} onValueChange={setRequestType}>
                <SelectTrigger>
                  <SelectValue placeholder="Select request type" />
                </SelectTrigger>
                <SelectContent>
                  {requestTypes.map((type) => {
                    const Icon = type.icon;
                    return (
                      <SelectItem key={type.value} value={type.value}>
                        <div className="flex items-center">
                          <Icon className="h-4 w-4 mr-2" />
                          {type.label}
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="message">Additional Message (Optional)</Label>
              <Textarea
                id="message"
                placeholder="Describe what you need help with..."
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                rows={3}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting || !requestType}
            >
              {isSubmitting ? 'Sending...' : 'Send Request'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default StaffRequestButton;
