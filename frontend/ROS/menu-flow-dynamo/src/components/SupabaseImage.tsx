import React, { useState, useEffect } from 'react';

interface SupabaseImageProps {
  src: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
  fallbackSrc?: string;
}

/**
 * A component specifically designed to handle Supabase storage images
 * It adds the download=true parameter to ensure correct content type
 */
const SupabaseImage: React.FC<SupabaseImageProps> = ({
  src,
  alt,
  className,
  width,
  height,
  fallbackSrc = 'https://placehold.co/200x200?text=Menu'
}) => {
  const [imageSrc, setImageSrc] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(false);

  useEffect(() => {
    if (!src) {
      setError(true);
      setIsLoading(false);
      return;
    }

    // For Supabase URLs, add download=true parameter to ensure correct content type
    let modifiedSrc = src;
    if (src.includes('supabase.co')) {
      // Add download=true parameter
      modifiedSrc = src.includes('?') 
        ? `${src}&download=true` 
        : `${src}?download=true`;
      
      // Add cache busting parameter
      modifiedSrc += `&t=${Date.now()}`;
      
      console.log(`SupabaseImage: Modified URL: ${modifiedSrc}`);
    }

    setImageSrc(modifiedSrc);
  }, [src]);

  const handleLoad = () => {
    console.log(`SupabaseImage: Successfully loaded image from ${imageSrc}`);
    setIsLoading(false);
  };

  const handleError = () => {
    console.error(`SupabaseImage: Failed to load image from ${imageSrc}`);
    setError(true);
    setIsLoading(false);
  };

  return (
    <>
      {isLoading && !error && (
        <div className={`${className || ''} flex items-center justify-center bg-gray-100`}>
          <span className="text-xs text-gray-500">Loading...</span>
        </div>
      )}
      
      {error ? (
        <img
          src={fallbackSrc}
          alt={alt}
          className={className}
          width={width}
          height={height}
        />
      ) : (
        <img
          src={imageSrc}
          alt={alt}
          className={className}
          width={width}
          height={height}
          onLoad={handleLoad}
          onError={handleError}
          style={{ display: isLoading ? 'none' : 'block' }}
        />
      )}
    </>
  );
};

export default SupabaseImage;
