import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/contexts/LanguageContext';
import {
  BarChart2,
  Settings,
  Utensils,
  QrCode,
  Clock,
  LogOut,
  PieChart,
  BookOpen,
  Coffee,
  Database,
  Bell,
  MessageSquare,
  RefreshCw,
  Brain,
  Target,
  Activity,
  ShoppingCart,
  ChefHat,
  Eye,
  Bot
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { getSubscriptionTranslation } from '@/contexts/SubscriptionTranslations';
import { useRestaurant } from '@/contexts/RestaurantContext';
import { useROSSubscription } from '@/hooks/useROSSubscription';
import { useROSFeatureAccess } from '@/hooks/useROSFeatureAccess';
import { BASIC_PRICE_ID, PREMIUM_PRICE_ID } from '@/lib/stripe';
import { Badge } from '@/components/ui/badge';
import { Crown, CreditCard } from 'lucide-react';

const AdminSidebar = () => {
  const { signOut, user } = useAuth();
  const { t, language } = useLanguage();
  const { restaurantInfo } = useRestaurant();
  const location = useLocation();
  const { subscription, createCheckoutSession, isCreatingCheckout, daysRemainingInTrial } = useROSSubscription();
  const featureAccess = useROSFeatureAccess();

  const handleSignOut = async () => {
    await signOut();
  };

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <aside className="w-64 bg-white border-r border-gray-200 h-screen flex flex-col sticky top-0">
      {/* Header - Fixed */}
      <div className="flex-shrink-0 p-4 border-b border-gray-200">
        <h1 className="text-xl font-semibold text-restaurant-primary">
          {restaurantInfo?.name || 'Restaurant'}
        </h1>
        <p className="text-xs text-gray-500">Owner Dashboard</p>
        {user && <p className="text-xs text-gray-500 mt-1 truncate">{user.email}</p>}
      </div>

      {/* Navigation - Scrollable */}
      <nav className="flex-1 overflow-y-auto">
        <div className="p-2">
          <div className="mb-2 px-2 py-1.5 text-xs font-semibold text-gray-500">
            MAIN
          </div>
          <div className="space-y-1">
            <Link to="/admin/dashboard">
              <Button
                variant="ghost"
                className={`w-full justify-start ${isActive('/admin/dashboard') ? 'bg-gray-100 text-restaurant-text' : 'text-restaurant-muted'}`}
              >
                <PieChart className="h-4 w-4 mr-3" />
                {t('dashboard')}
              </Button>
            </Link>
            <Link to="/admin/menus">
              <Button
                variant="ghost"
                className={`w-full justify-start ${isActive('/admin/menus') ? 'bg-gray-100 text-restaurant-text' : 'text-restaurant-muted'}`}
              >
                <BookOpen className="h-4 w-4 mr-3" />
                Menus
              </Button>
            </Link>
            <Link to="/admin/menu-items">
              <Button
                variant="ghost"
                className={`w-full justify-start ${isActive('/admin/menu-items') ? 'bg-gray-100 text-restaurant-text' : 'text-restaurant-muted'}`}
              >
                <Coffee className="h-4 w-4 mr-3" />
                Menu Items
              </Button>
            </Link>
            <Link to="/admin/tables">
              <Button
                variant="ghost"
                className={`w-full justify-start ${isActive('/admin/tables') ? 'bg-gray-100 text-restaurant-text' : 'text-restaurant-muted'}`}
              >
                <QrCode className="h-4 w-4 mr-3" />
                {t('tableManagement')}
              </Button>
            </Link>
            <Link to="/admin/orders">
              <Button
                variant="ghost"
                className={`w-full justify-start ${isActive('/admin/orders') ? 'bg-gray-100 text-restaurant-text' : 'text-restaurant-muted'}`}
              >
                <Clock className="h-4 w-4 mr-3" />
                {t('orderManagement')}
              </Button>
            </Link>
            <Link to="/admin/notifications">
              <Button
                variant="ghost"
                className={`w-full justify-start ${isActive('/admin/notifications') ? 'bg-gray-100 text-restaurant-text' : 'text-restaurant-muted'}`}
              >
                <Bell className="h-4 w-4 mr-3" />
                Notifications
              </Button>
            </Link>
            <Link to="/admin/staff-requests">
              <Button
                variant="ghost"
                className={`w-full justify-start ${isActive('/admin/staff-requests') ? 'bg-gray-100 text-restaurant-text' : 'text-restaurant-muted'}`}
              >
                <MessageSquare className="h-4 w-4 mr-3" />
                Staff Requests
              </Button>
            </Link>
            <Link to="/admin/analytics">
              <Button
                variant="ghost"
                className={`w-full justify-start ${isActive('/admin/analytics') ? 'bg-gray-100 text-restaurant-text' : 'text-restaurant-muted'}`}
              >
                <BarChart2 className="h-4 w-4 mr-3" />
                {t('analytics')}
              </Button>
            </Link>
            <Link to="/admin/settings">
              <Button
                variant="ghost"
                className={`w-full justify-start ${isActive('/admin/settings') ? 'bg-gray-100 text-restaurant-text' : 'text-restaurant-muted'}`}
              >
                <Settings className="h-4 w-4 mr-3" />
                {t('settings')}
              </Button>
            </Link>
          </div>

          {/* AI & Intelligence Section */}
          <div className="mb-2 px-2 py-1.5 text-xs font-semibold text-gray-500 mt-4">
            AI & INTELLIGENCE
          </div>
          <div className="space-y-1">
            <Link to="/admin/enhanced-visibility">
              <Button
                variant="ghost"
                className={`w-full justify-start ${isActive('/admin/enhanced-visibility') ? 'bg-gray-100 text-restaurant-text' : 'text-restaurant-muted'}`}
              >
                <Eye className="h-4 w-4 mr-3" />
                {t('enhancedVisibility')}
              </Button>
            </Link>
            <Link to="/admin/feedback-sync">
              <Button
                variant="ghost"
                className={`w-full justify-start ${isActive('/admin/feedback-sync') ? 'bg-gray-100 text-restaurant-text' : 'text-restaurant-muted'}`}
              >
                <RefreshCw className="h-4 w-4 mr-3" />
                {t('aiTrainingSync')}
              </Button>
            </Link>
            <Link to="/admin/chef-recommendations">
              <Button
                variant="ghost"
                className={`w-full justify-start ${isActive('/admin/chef-recommendations') ? 'bg-gray-100 text-restaurant-text' : 'text-restaurant-muted'}`}
              >
                <ChefHat className="h-4 w-4 mr-3" />
                {t('chefRecommendations')}
              </Button>
            </Link>
            {/* Test Data button removed */}
          </div>

          {/* Subscription Status Section */}
          <div className="px-2 py-4 border-t border-gray-200 mt-4">
            <div className="mb-2 px-2 py-1.5 text-xs font-semibold text-gray-500">
              {getSubscriptionTranslation('subscription', language).toUpperCase()}
            </div>
            
            {subscription ? (
              <div className="space-y-2">
                <div className="flex items-center justify-between px-2">
                  <span className="text-sm font-medium">
                    {subscription.plan_name || (subscription.plan_id === 'enterprise' ? getSubscriptionTranslation('premium', language) : getSubscriptionTranslation('basic', language))}
                  </span>
                  <Badge variant={subscription.status === 'active' ? 'default' : 
                                subscription.status === 'trialing' ? 'secondary' : 'destructive'}>
                    {subscription.status === 'trialing' ? getSubscriptionTranslation('freeTrial', language) : 
                     subscription.status === 'active' ? 'Active' : 'Expired'}
                  </Badge>
                </div>
                
                {/* Trial Warning */}
                {subscription.status === 'trialing' && daysRemainingInTrial <= 3 && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded p-2">
                    <p className="text-xs text-yellow-800 mb-2">
                      {getSubscriptionTranslation('trialEndsIn', language)} {daysRemainingInTrial} {daysRemainingInTrial === 1 ? getSubscriptionTranslation('day', language) : getSubscriptionTranslation('days', language)}!
                    </p>
                    <div className="space-y-1">
                      <Button 
                        size="sm" 
                        className="w-full text-xs h-7"
                        onClick={() => createCheckoutSession(PREMIUM_PRICE_ID)}
                        disabled={isCreatingCheckout}
                      >
                        <Crown className="h-3 w-3 mr-1" />
                        {getSubscriptionTranslation('premium', language)} €69.99
                      </Button>
                      <Button 
                        size="sm" 
                        variant="outline" 
                        className="w-full text-xs h-7"
                        onClick={() => createCheckoutSession(BASIC_PRICE_ID)}
                        disabled={isCreatingCheckout}
                      >
                        {getSubscriptionTranslation('basic', language)} €14.99
                      </Button>
                    </div>
                  </div>
                )}
                
                {/* Payment Required */}
                {subscription.status === 'past_due' && (
                  <div className="bg-red-50 border border-red-200 rounded p-2">
                    <p className="text-xs text-red-800 mb-2">
                      Payment failed - Update billing
                    </p>
                    <Button 
                      size="sm" 
                      variant="destructive" 
                      className="w-full text-xs h-7"
                      onClick={() => createCheckoutSession(subscription.plan_id === 'enterprise' ? PREMIUM_PRICE_ID : BASIC_PRICE_ID)}
                      disabled={isCreatingCheckout}
                    >
                      <CreditCard className="h-3 w-3 mr-1" />
                      Update Payment
                    </Button>
                  </div>
                )}
                
                {/* Upgrade to Premium */}
                {subscription.status === 'active' && subscription.plan_id === 'professional' && (
                  <Button 
                    size="sm" 
                    className="w-full text-xs h-7 bg-yellow-600 hover:bg-yellow-700"
                    onClick={() => createCheckoutSession(PREMIUM_PRICE_ID)}
                    disabled={isCreatingCheckout}
                  >
                    <Crown className="h-3 w-3 mr-1" />
                    {getSubscriptionTranslation('upgradeToPremium', language)}
                  </Button>
                )}
              </div>
            ) : (
              <div className="space-y-2">
                <p className="text-xs text-gray-600 px-2">No active subscription</p>
                <div className="space-y-1">
                  <Button 
                    size="sm" 
                    className="w-full text-xs h-7"
                    onClick={() => createCheckoutSession(PREMIUM_PRICE_ID)}
                    disabled={isCreatingCheckout}
                  >
                    <Crown className="h-3 w-3 mr-1" />
                    Premium €69.99
                  </Button>
                  <Button 
                    size="sm" 
                    variant="outline" 
                    className="w-full text-xs h-7"
                    onClick={() => createCheckoutSession(BASIC_PRICE_ID)}
                    disabled={isCreatingCheckout}
                  >
                    Basic €14.99
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </nav>

      {/* Footer - Fixed */}
      <div className="flex-shrink-0 p-2 border-t border-gray-200">
        <Button
          variant="ghost"
          className="w-full justify-start text-restaurant-muted"
          onClick={handleSignOut}
        >
          <LogOut className="h-4 w-4 mr-3" />
          {t('signOut')}
        </Button>
      </div>
    </aside>
  );
};

export default AdminSidebar;
