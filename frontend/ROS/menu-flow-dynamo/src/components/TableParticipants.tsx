import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Users, User, Clock, Wifi } from 'lucide-react';
import { TableParticipant } from '@/services/sharedCartService';

interface TableParticipantsProps {
  participants: TableParticipant[];
  customerSessionId: string;
  className?: string;
}

const TableParticipants: React.FC<TableParticipantsProps> = ({
  participants,
  customerSessionId,
  className = ''
}) => {
  const activeParticipants = participants.filter(p => p.is_active);
  const participantCount = activeParticipants.length;

  if (participantCount === 0) {
    return null;
  }

  return (
    <Card className={`bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200 ${className}`}>
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-blue-600" />
            <span className="font-medium text-gray-900">
              {participantCount === 1 ? '1 person' : `${participantCount} people`} at your table
            </span>
          </div>
          <Badge variant="outline" className="bg-white/50">
            <Wifi className="h-3 w-3 mr-1" />
            Live
          </Badge>
        </div>
        
        <div className="space-y-2">
          {activeParticipants.map((participant) => {
            const isMe = participant.customer_session_id === customerSessionId;
            const joinedTime = new Date(participant.joined_at);
            const lastActivity = new Date(participant.last_activity_at);
            const isRecentlyActive = (Date.now() - lastActivity.getTime()) < 60000; // Active within last minute
            
            return (
              <div 
                key={participant.id}
                className={`flex items-center justify-between p-2 rounded-lg transition-colors ${
                  isMe ? 'bg-blue-100/70' : 'bg-white/50'
                }`}
              >
                <div className="flex items-center gap-2">
                  <div className={`p-1 rounded-full ${isMe ? 'bg-blue-600' : 'bg-gray-400'}`}>
                    <User className="h-3 w-3 text-white" />
                  </div>
                  <div>
                    <div className="flex items-center gap-2">
                      <span className={`text-sm font-medium ${isMe ? 'text-blue-900' : 'text-gray-700'}`}>
                        {participant.customer_display_name}
                      </span>
                      {isMe && (
                        <Badge variant="secondary" className="text-xs px-1 py-0">
                          You
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-1 text-xs text-gray-500">
                      <Clock className="h-3 w-3" />
                      <span>
                        Joined {joinedTime.toLocaleTimeString([], { 
                          hour: '2-digit', 
                          minute: '2-digit' 
                        })}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-1">
                  <div 
                    className={`w-2 h-2 rounded-full ${
                      isRecentlyActive ? 'bg-green-500' : 'bg-yellow-500'
                    }`}
                    title={isRecentlyActive ? 'Active now' : 'Idle'}
                  />
                  <span className="text-xs text-gray-500">
                    {isRecentlyActive ? 'Active' : 'Idle'}
                  </span>
                </div>
              </div>
            );
          })}
        </div>
        
        {participantCount > 1 && (
          <div className="mt-3 pt-3 border-t border-blue-200/50">
            <p className="text-xs text-gray-600 text-center">
              🤝 Everyone can add items to the shared cart
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TableParticipants;
