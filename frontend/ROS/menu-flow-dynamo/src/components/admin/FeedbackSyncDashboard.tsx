/**
 * Admin Dashboard for Feedback Sync Management
 * Allows testing and monitoring sync to centralized SME Analytica database
 */

import React, { useState, useEffect } from 'react';
import { FeedbackSyncService } from '../../services/feedbackSyncService';
import { migrateFeedback, checkMigrationStatus, setupAutoSync } from '../../services/migrateFeedback';
import { useLanguage } from '@/contexts/LanguageContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface SyncStatus {
  localCount: number;
  centralizedCount: number;
  needsMigration: boolean;
  summary: string;
}

interface MigrationResult {
  success: boolean;
  message: string;
  migrated: number;
  failed: number;
  details: Array<{
    id: string;
    status: string;
    error?: string;
  }>;
}

export const FeedbackSyncDashboard: React.FC = () => {
  const { t } = useLanguage();
  const [isLoading, setIsLoading] = useState(false);
  const [syncStatus, setSyncStatus] = useState<SyncStatus | null>(null);
  const [migrationResult, setMigrationResult] = useState<MigrationResult | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<boolean | null>(null);
  const [testResult, setTestResult] = useState<string>('');

  // Check sync status on component mount
  useEffect(() => {
    checkStatus();
    testConnection();
  }, []);

  const checkStatus = async () => {
    try {
      setIsLoading(true);
      const status = await checkMigrationStatus();
      setSyncStatus(status);
    } catch (error) {
      console.error('Error checking status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const testConnection = async () => {
    try {
      const isConnected = await FeedbackSyncService.testConnection();
      setConnectionStatus(isConnected);
    } catch (error) {
      console.error('Connection test error:', error);
      setConnectionStatus(false);
    }
  };

  const runMigration = async () => {
    try {
      setIsLoading(true);
      setTestResult(`🔄 ${t('startingFeedbackMigration')}`);
      
      const result = await migrateFeedback();
      setMigrationResult(result);
      
      if (result.success) {
        setTestResult(`✅ ${t('migrationSuccessful')} ${result.migrated} ${t('recordsSyncedToCentralized')}`);
      } else {
        setTestResult(`❌ ${t('migrationFailed')} ${result.message}`);
      }
      
      // Refresh status after migration
      await checkStatus();
    } catch (error) {
      console.error('Migration error:', error);
      setTestResult(`❌ ${t('migrationError')} ${error instanceof Error ? error.message : t('unknownError')}`);
    } finally {
      setIsLoading(false);
    }
  };

  const setupSync = async () => {
    try {
      setIsLoading(true);
      const result = await setupAutoSync();
      
      if (result.success) {
        setTestResult(`✅ ${t('autoSyncConfigured')}`);
      } else {
        setTestResult(`❌ ${t('autoSyncSetupFailed')} ${result.message}`);
      }
    } catch (error) {
      console.error('Setup error:', error);
      setTestResult(`❌ ${t('setupError')} ${error instanceof Error ? error.message : t('unknownError')}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testSampleSync = async () => {
    try {
      setIsLoading(true);
      setTestResult(`🧪 ${t('testingSampleFeedbackSync')}`);
      
      const sampleFeedback = {
        id: 'test_' + Date.now(),
        restaurant_id: 'test_restaurant_' + Date.now(),
        order_id: 'test_order_' + Date.now(),
        table_id: 'table_1',
        customer_email: '<EMAIL>',
        food_rating: 5,
        service_rating: 4,
        app_rating: 5,
        comments: 'Test feedback for AI training verification',
        sentiment_score: 0.9,
        sentiment_magnitude: 0.8,
        created_at: new Date().toISOString()
      };
      
      const syncResult = await FeedbackSyncService.syncToMainSystem(sampleFeedback);
      
      if (syncResult.success) {
        setTestResult(`✅ ${t('sampleSyncSuccessful')}`);
      } else {
        setTestResult(`❌ ${t('sampleSyncFailed')} ${syncResult.error}`);
      }
    } catch (error) {
      console.error('Test sync error:', error);
      setTestResult(`❌ ${t('testSyncError')} ${error instanceof Error ? error.message : t('unknownError')}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold mb-2 text-restaurant-primary">🤖 {t('feedbackSyncDashboard')}</h1>
        <p className="text-restaurant-muted">{t('manageFeedbackSync')}</p>
      </div>
      
      {/* Connection Status */}
      <Card>
        <CardHeader>
          <CardTitle className="text-restaurant-primary">{t('connectionStatus')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2">
            <span className={`w-3 h-3 rounded-full ${connectionStatus === true ? 'bg-green-500' : connectionStatus === false ? 'bg-red-500' : 'bg-yellow-500'}`}></span>
            <span className="text-sm font-medium text-restaurant-foreground">
              {connectionStatus === true ? t('connectedToCentralizedDatabase') : 
               connectionStatus === false ? t('cannotConnect') : 
               t('testingConnection')}
            </span>
          </div>
        </CardContent>
      </Card>

      {/* Sync Status */}
      {syncStatus && (
        <Card>
          <CardHeader>
            <CardTitle className="text-restaurant-primary">{t('syncStatus')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm text-restaurant-muted space-y-1">
              <p><strong>{t('localFeedback')}:</strong> {syncStatus.localCount} records</p>
              <p><strong>{t('needsMigration')}:</strong> {syncStatus.needsMigration ? t('yes') : t('no')}</p>
              <p>{syncStatus.summary}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <Card>
        <CardHeader>
          <CardTitle className="text-restaurant-primary">{t('actions')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button
              onClick={checkStatus}
              disabled={isLoading}
              variant="outline"
              className="bg-restaurant-primary text-white hover:bg-restaurant-primary/90"
            >
              📊 {t('checkStatus')}
            </Button>
            
            <Button
              onClick={testSampleSync}
              disabled={isLoading}
              variant="outline"
              className="bg-restaurant-secondary text-white hover:bg-restaurant-secondary/90"
            >
              🧪 {t('testSync')}
            </Button>
            
            <Button
              onClick={runMigration}
              disabled={isLoading}
              className="bg-green-600 text-white hover:bg-green-700"
            >
              🔄 {t('migrateData')}
            </Button>
            
            <Button
              onClick={setupSync}
              disabled={isLoading}
              className="bg-orange-600 text-white hover:bg-orange-700"
            >
              ⚙️ {t('setupAutoSync')}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Test Results */}
      {testResult && (
        <Card>
          <CardHeader>
            <CardTitle className="text-restaurant-primary">{t('results')}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm whitespace-pre-wrap text-restaurant-foreground">{testResult}</p>
          </CardContent>
        </Card>
      )}

      {/* Migration Details */}
      {migrationResult && (
        <Card>
          <CardHeader>
            <CardTitle className="text-restaurant-primary">{t('migrationResults')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm space-y-2 text-restaurant-foreground">
              <p><strong>{t('status')}:</strong> {migrationResult.success ? '✅ Success' : '❌ Failed'}</p>
              <p><strong>{t('message')}:</strong> {migrationResult.message}</p>
              <p><strong>{t('migrated')}:</strong> {migrationResult.migrated} records</p>
              <p><strong>{t('failed')}:</strong> {migrationResult.failed} records</p>
              
              {migrationResult.details && migrationResult.details.length > 0 && (
                <div className="mt-4">
                  <p><strong>{t('details')}:</strong></p>
                  <div className="max-h-40 overflow-y-auto bg-restaurant-background p-2 rounded border">
                    {migrationResult.details.map((detail, index) => (
                      <div key={index} className="text-xs mb-1 text-restaurant-muted font-mono">
                        {detail.id}: {detail.status} {detail.error && `- ${detail.error}`}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Information */}
      <Card className="bg-restaurant-background">
        <CardHeader>
          <CardTitle className="text-restaurant-primary">{t('aboutAiTrainingSync')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-restaurant-muted space-y-2">
            <p>{t('dashboardDescription')}</p>
            <p><strong>{t('migration')}:</strong> {t('migrationDescription')}</p>
            <p><strong>{t('autoSync')}:</strong> {t('autoSyncDescription')}</p>
            <p><strong>{t('purpose')}:</strong> {t('purposeDescription')}</p>
          </div>
        </CardContent>
      </Card>
      
      {/* AI Intelligence Notice */}
      <Card className="bg-blue-50 border-blue-200">
        <CardHeader>
          <CardTitle className="text-blue-800 text-base">🧠 Advanced AI Intelligence</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-blue-700 space-y-2">
            <p><strong>For SME Analytica Team:</strong> Cross-restaurant AI intelligence features have been moved to our internal admin portal for platform-wide analysis.</p>
            <p><strong>Access:</strong> <code>https://smeanalytica.com/admin</code> (Internal Use Only)</p>
            <p><strong>Features:</strong> Restaurant search, AI insights, system health monitoring, and training data management.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default FeedbackSyncDashboard; 