import React from 'react';
import { motion } from 'framer-motion';
import { Plus, Minus } from 'lucide-react';
import { RestaurantTheme } from '@/types/theme';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MenuItemType } from '@/components/MenuItem';

// Extend MenuItemType with optional properties we need
interface ExtendedMenuItem extends MenuItemType {
  isSpecial?: boolean;
}
import { toast } from 'sonner';
import { formatPrice } from '@/lib/utils';

interface ThemedMenuItemProps {
  item: ExtendedMenuItem;
  onAddToCart: (item: ExtendedMenuItem) => void;
  layoutMode: 'grid' | 'list';
  themeStyles: {
    cardStyle: string;
    headingStyle: string;
    fontFamily: string;
    fontSize: string;
    colors: {
      primary: string;
      secondary: string;
      background: string;
      cardBackground: string;
      textPrimary: string;
      textSecondary: string;
      accent: string;
    }
  };
  activeTheme: RestaurantTheme | null;
}

const ThemedMenuItem = ({ 
  item, 
  onAddToCart, 
  layoutMode, 
  themeStyles, 
  activeTheme 
}: ThemedMenuItemProps) => {
  // Extract colors for inline styling
  const cardBgColor = activeTheme?.colors?.cardBackground || '#ffffff';
  const textPrimaryColor = activeTheme?.colors?.textPrimary || '#1e293b';
  const textSecondaryColor = activeTheme?.colors?.textSecondary || '#64748b';
  const accentColor = activeTheme?.colors?.accent || '#0ea5e9';
  
  // Handle add to cart with animation feedback
  const handleAddToCart = () => {
    onAddToCart(item);
    toast.success(`${item.name} added to cart`);
  };
  
  // Generate dynamic card style based on theme settings with enhanced visual impact
  const cardStyle = cn(
    'overflow-hidden transition-all duration-300 border-2 shadow-lg hover:shadow-xl transform hover:scale-102',
    layoutMode === 'grid' ? 'h-full rounded-xl' : 'flex items-center rounded-lg',
    themeStyles.cardStyle || 'border-blue-500'
  );
  
  if (layoutMode === 'list') {
    return (
      <Card 
        className={cardStyle}
        style={{ backgroundColor: cardBgColor, borderColor: `${accentColor}10` }}
      >
        <div className="flex items-center w-full">
          {/* Image container - fixed width in list mode */}
          {item.image && (
            <div className="w-24 h-24 mr-4 relative overflow-hidden flex-shrink-0">
              <img 
                src={item.image} 
                alt={item.name} 
                className="w-full h-full object-cover"
              />
              {item.isSpecial && (
                <div 
                  className="absolute top-0 right-0 px-2 py-1 text-xs font-semibold"
                  style={{ backgroundColor: accentColor, color: 'white' }}
                >
                  Special
                </div>
              )}
            </div>
          )}
          
          {/* Content */}
          <div className="flex-1 py-3 min-w-0">
            <div className="flex justify-between items-start">
              <div>
                <h3 
                  className={cn("text-lg line-clamp-1", themeStyles.headingStyle)} 
                  style={{ color: textPrimaryColor }}
                >
                  {item.name}
                </h3>
                {item.description && (
                  <p 
                    className="text-sm line-clamp-2 mt-1" 
                    style={{ color: textSecondaryColor }}
                  >
                    {item.description}
                  </p>
                )}
              </div>
              
              <div className="flex items-center space-x-2">
                <span 
                  className="font-semibold whitespace-nowrap"
                  style={{ color: textPrimaryColor }}
                >
                  {formatPrice(item.price)}
                </span>
                <Button 
                  onClick={handleAddToCart}
                  size="sm" 
                  className="ml-2 flex-shrink-0"
                  style={{ backgroundColor: accentColor }}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </Card>
    );
  }
  
  // Grid layout
  return (
    <Card 
      className={cardStyle}
      style={{ backgroundColor: cardBgColor, borderColor: `${accentColor}10` }}
    >
      {/* Image container with consistent aspect ratio */}
      {item.image && (
        <div className="relative w-full pt-[60%] overflow-hidden">
          <img 
            src={item.image} 
            alt={item.name} 
            className="absolute inset-0 w-full h-full object-cover"
          />
          {item.isSpecial && (
            <div 
              className="absolute top-2 right-2 px-2 py-1 text-xs font-semibold rounded"
              style={{ backgroundColor: accentColor, color: 'white' }}
            >
              Special
            </div>
          )}
        </div>
      )}
      
      <CardHeader className="pb-2">
        <CardTitle 
          className={themeStyles.headingStyle}
          style={{ color: textPrimaryColor }}
        >
          {item.name}
        </CardTitle>
        {item.description && (
          <CardDescription 
            className="line-clamp-2"
            style={{ color: textSecondaryColor }}
          >
            {item.description}
          </CardDescription>
        )}
      </CardHeader>
      
      <CardFooter className="flex justify-between items-center pt-2">
        <span 
          className="font-semibold"
          style={{ color: textPrimaryColor }}
        >
          {formatPrice(item.price)}
        </span>
        <Button 
          onClick={handleAddToCart}
          size="sm"
          style={{ backgroundColor: accentColor }}
        >
          Add
        </Button>
      </CardFooter>
    </Card>
  );
};

export default ThemedMenuItem;
