import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { Upload, X, Image as ImageIcon } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

interface LogoUploaderProps {
  currentLogoUrl?: string | null;
  onLogoChange: (logoUrl: string | null) => void;
  disabled?: boolean;
  maxSizeKB?: number;
  label?: string;
  description?: string;
}

export const LogoUploader: React.FC<LogoUploaderProps> = ({
  currentLogoUrl,
  onLogoChange,
  disabled = false,
  maxSizeKB = 500, // 500KB default for logos
  label = "Restaurant Logo",
  description = "Upload your restaurant logo (up to 500KB)"
}) => {
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentLogoUrl);

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Check file size
    const maxSizeBytes = maxSizeKB * 1024;
    if (file.size > maxSizeBytes) {
      toast({
        variant: "destructive",
        title: "File too large",
        description: `Logo must be less than ${maxSizeKB}KB for optimal performance`,
      });
      return;
    }

    // Check file type
    if (!file.type.startsWith('image/')) {
      toast({
        variant: "destructive",
        title: "Invalid file type",
        description: "Please upload an image file (JPEG, PNG, WebP, etc.)",
      });
      return;
    }

    setIsProcessing(true);

    try {
      // Create a canvas to convert and resize the logo
      const img = new Image();
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      // Wait for the image to load
      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
        img.src = URL.createObjectURL(file);
      });

      // Calculate new dimensions (max 300px width/height for logos while maintaining aspect ratio)
      let width = img.width;
      let height = img.height;
      const maxDimension = 300;
      
      if (width > height && width > maxDimension) {
        height = Math.round(height * (maxDimension / width));
        width = maxDimension;
      } else if (height > maxDimension) {
        width = Math.round(width * (maxDimension / height));
        height = maxDimension;
      }

      // Set canvas dimensions to the resized logo
      canvas.width = width;
      canvas.height = height;

      // Draw the image on the canvas with high quality
      if (ctx) {
        ctx.imageSmoothingQuality = 'high';
        ctx.drawImage(img, 0, 0, width, height);

        // Use high quality format for logos
        let outputFormat = 'image/png'; // PNG is better for logos with transparency
        let quality = 0.95;
        
        // Use JPEG for photos, PNG for graphics/logos
        if (file.type === 'image/jpeg' && file.name.toLowerCase().includes('photo')) {
          outputFormat = 'image/jpeg';
          quality = 0.9;
        }

        // Convert to data URL
        const dataUrl = canvas.toDataURL(outputFormat, quality);
        console.log(`Logo converted to data URL: ${Math.round(dataUrl.length/1024)}KB using ${outputFormat}`);

        // Update preview and notify parent
        setPreviewUrl(dataUrl);
        onLogoChange(dataUrl);
        
        toast({
          title: "Logo uploaded successfully",
          description: `Logo processed and ready to use (${Math.round(dataUrl.length/1024)}KB)`,
        });
        
        // Clean up the object URL
        URL.revokeObjectURL(img.src);
      }
    } catch (error) {
      console.error('Error processing logo:', error);
      toast({
        variant: "destructive",
        title: "Logo processing error",
        description: "There was a problem processing your logo. Please try a different image.",
      });
    } finally {
      setIsProcessing(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleRemoveLogo = () => {
    setPreviewUrl(null);
    onLogoChange(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    toast({
      title: "Logo removed",
      description: "Logo has been removed from your restaurant branding",
    });
  };

  return (
    <div className="space-y-3">
      <div>
        <Label className="text-sm font-medium text-gray-700">{label}</Label>
        <p className="text-xs text-gray-500 mt-1">{description}</p>
      </div>

      {/* Logo Preview Card */}
      {previewUrl && (
        <Card className="w-full max-w-xs">
          <CardContent className="p-4">
            <div className="relative">
              <img
                src={previewUrl}
                alt="Restaurant Logo Preview"
                className="w-full h-24 object-contain bg-gray-50 rounded-lg border"
              />
              <Button
                variant="destructive"
                size="sm"
                onClick={handleRemoveLogo}
                disabled={disabled || isProcessing}
                className="absolute -top-2 -right-2 h-6 w-6 p-0 rounded-full"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
            <p className="text-xs text-center text-gray-500 mt-2">Current Logo</p>
          </CardContent>
        </Card>
      )}

      {/* Upload Area */}
      <div className="flex items-center space-x-2">
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          disabled={disabled || isProcessing}
          className="hidden"
          id="logo-upload"
        />
        
        <Button
          variant="outline"
          onClick={() => fileInputRef.current?.click()}
          disabled={disabled || isProcessing}
          className="flex items-center space-x-2"
        >
          {isProcessing ? (
            <>
              <div className="animate-spin h-4 w-4 border-2 border-sky-500 border-t-transparent rounded-full" />
              <span>Processing...</span>
            </>
          ) : (
            <>
              <Upload className="h-4 w-4" />
              <span>{previewUrl ? 'Change Logo' : 'Upload Logo'}</span>
            </>
          )}
        </Button>

        {!previewUrl && (
          <div className="flex items-center text-gray-400">
            <ImageIcon className="h-8 w-8" />
          </div>
        )}
      </div>
    </div>
  );
};

export default LogoUploader;
