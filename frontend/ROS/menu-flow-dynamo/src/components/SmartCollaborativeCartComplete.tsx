/**
 * Complete Smart Collaborative Cart System
 *
 * Full implementation of collaborative cart with real-time sync, participants management,
 * and intelligent features for restaurants
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Users,
  Brain,
  TrendingUp,
  Clock,
  DollarSign,
  ShoppingCart,
  UserPlus,
  Zap,
  Target,
  Star,
  AlertCircle,
  CheckCircle,
  RefreshCw,
  Activity,
  Sparkles,
  Trash2,
  Plus,
  Minus,
  Lock,
  Unlock,
  Share,
  Timer,
  Wifi,
  WifiOff
} from 'lucide-react';
import { toast } from 'sonner';
import { useSharedCart } from '@/hooks/useSharedCart';
import { useTableParticipants } from '@/hooks/useTableParticipants';
import { MenuItemType } from '@/components/MenuItem';
import { generateSessionId } from '@/config/app';
import { useLanguage } from '@/contexts/LanguageContext';
import { motion, AnimatePresence } from 'framer-motion';

interface SmartCollaborativeCartProps {
  tableId: string;
  restaurantId: string;
  onCheckout?: () => void;
  onAddItem?: (item: MenuItemType) => void;
  className?: string;
}

interface CartItemDisplay {
  id: string;
  name: string;
  price: number;
  quantity: number;
  addedBy: string;
  addedAt: string;
  canModify: boolean;
}

export const SmartCollaborativeCartComplete: React.FC<SmartCollaborativeCartProps> = ({
  tableId,
  restaurantId,
  onCheckout,
  onAddItem,
  className = ""
}) => {
  const { t } = useLanguage();
  const [customerSessionId] = useState(() => generateSessionId());
  const [isExpanded, setIsExpanded] = useState(false);
  const [lastSyncTime, setLastSyncTime] = useState<Date>(new Date());
  const connectionRef = useRef<boolean>(true);

  // Collaborative cart hook
  const sharedCart = useSharedCart({
    tableId,
    customerSessionId,
    restaurantId,
    enabled: !!tableId && !!restaurantId
  });

  // Table participants hook
  const tableParticipants = useTableParticipants({
    tableId,
    customerSessionId,
    enabled: !!tableId
  });

  // Connection health monitoring
  useEffect(() => {
    const checkConnection = () => {
      connectionRef.current = navigator.onLine;
      setLastSyncTime(new Date());
    };

    window.addEventListener('online', checkConnection);
    window.addEventListener('offline', checkConnection);

    const interval = setInterval(checkConnection, 30000); // Check every 30s

    return () => {
      window.removeEventListener('online', checkConnection);
      window.removeEventListener('offline', checkConnection);
      clearInterval(interval);
    };
  }, []);

  // Auto-join cart when component mounts
  useEffect(() => {
    if (tableId && restaurantId && !sharedCart.cart && !sharedCart.isJoining) {

      sharedCart.joinCart();
    }
  }, [tableId, restaurantId, sharedCart.cart, sharedCart.isJoining]);

  // Format cart items for display
  const cartItemsDisplay: CartItemDisplay[] = sharedCart.cartItems.map(item => ({
    id: item.menu_item_id,
    name: item.item_name,
    price: item.unit_price,
    quantity: item.quantity,
    addedBy: item.added_by_session || 'Unknown',
    addedAt: item.created_at,
    canModify: sharedCart.canModifyItem(item)
  }));

  // Calculate totals
  const totalItems = cartItemsDisplay.reduce((sum, item) => sum + item.quantity, 0);
  const totalAmount = cartItemsDisplay.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const myItems = sharedCart.getMyItems();
  const myTotal = myItems.reduce((sum, item) => sum + (item.unit_price * item.quantity), 0);

  // Handle quantity changes
  const handleUpdateQuantity = useCallback(async (itemId: string, newQuantity: number) => {
    try {
      if (newQuantity <= 0) {
        await sharedCart.removeItem(itemId);
        toast.success('Item removed from cart');
      } else {
        await sharedCart.updateQuantity(itemId, newQuantity);
        toast.success('Quantity updated');
      }
    } catch (error) {
      console.error('Error updating quantity:', error);
      toast.error('Failed to update quantity');
    }
  }, [sharedCart]);

  // Handle cart locking
  const handleLockToggle = useCallback(async () => {
    try {
      if (sharedCart.isLocked) {
        if (sharedCart.lockedByMe) {
          await sharedCart.unlockCart();
          toast.success('Cart unlocked');
        } else {
          toast.error('Cart is locked by another customer');
        }
      } else {
        const success = await sharedCart.lockForCheckout();
        if (success) {
          toast.success('Cart locked for checkout');
        }
      }
    } catch (error) {
      console.error('Error toggling cart lock:', error);
      toast.error('Failed to toggle cart lock');
    }
  }, [sharedCart]);

  // Handle checkout
  const handleCheckout = useCallback(async () => {
    if (totalItems === 0) {
      toast.error('Cart is empty');
      return;
    }

    try {
      // Lock cart first
      if (!sharedCart.isLocked) {
        const locked = await sharedCart.lockForCheckout();
        if (!locked) {
          toast.error('Unable to lock cart for checkout');
          return;
        }
      }

      if (onCheckout) {
        onCheckout();
      }
    } catch (error) {
      console.error('Error during checkout:', error);
      toast.error('Failed to start checkout');
    }
  }, [totalItems, sharedCart, onCheckout]);

  // Loading state
  if (sharedCart.isLoading) {
    return (
      <Card className={`w-full ${className}`}>
        <CardContent className="p-6">
          <div className="flex items-center justify-center space-x-2">
            <RefreshCw className="h-4 w-4 animate-spin" />
            <span>Loading collaborative cart...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Error state
  if (!sharedCart.cart && !sharedCart.isJoining) {
    return (
      <Card className={`w-full border-orange-200 bg-orange-50 ${className}`}>
        <CardContent className="p-4">
          <div className="flex items-center space-x-2 text-orange-700">
            <AlertCircle className="h-4 w-4" />
            <span>Unable to connect to collaborative cart</span>
            <Button
              size="sm"
              variant="outline"
              onClick={sharedCart.joinCart}
              disabled={sharedCart.isJoining}
            >
              {sharedCart.isJoining ? 'Joining...' : 'Try Again'}
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`w-full bg-gradient-to-br from-blue-50 to-purple-50 border-blue-200 ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="relative">
              <ShoppingCart className="h-5 w-5 text-blue-600" />
              {totalItems > 0 && (
                <Badge className="absolute -top-2 -right-2 h-4 w-4 p-0 flex items-center justify-center text-xs bg-blue-600">
                  {totalItems}
                </Badge>
              )}
            </div>
            <CardTitle className="text-lg">Shared Cart</CardTitle>
          </div>

          <div className="flex items-center space-x-2">
            {/* Connection status */}
            <div className="flex items-center space-x-1">
              {connectionRef.current ? (
                <Wifi className="h-3 w-3 text-green-500" />
              ) : (
                <WifiOff className="h-3 w-3 text-red-500" />
              )}
              <span className="text-xs text-gray-500">
                {connectionRef.current ? 'Connected' : 'Offline'}
              </span>
            </div>

            {/* Cart status */}
            {sharedCart.isLocked && (
              <Badge variant="outline" className="bg-yellow-50 border-yellow-300 text-yellow-700">
                <Lock className="h-3 w-3 mr-1" />
                {sharedCart.lockedByMe ? 'Locked by you' : 'Locked'}
              </Badge>
            )}
          </div>
        </div>

        {/* Participants summary */}
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="flex items-center space-x-2">
            <Users className="h-4 w-4" />
            <span>
              {tableParticipants.participantCount} participant{tableParticipants.participantCount !== 1 ? 's' : ''}
            </span>
            {sharedCart.customerName && (
              <span className="text-blue-600 font-medium">
                • You: {sharedCart.customerName}
              </span>
            )}
          </div>

          {totalAmount > 0 && (
            <div className="font-semibold text-blue-700">
              Total: €{totalAmount.toFixed(2)}
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Participants list */}
        {tableParticipants.participants && tableParticipants.participants.length > 0 && (
          <div className="mb-4">
            <div className="flex items-center space-x-2 mb-2">
              <span className="text-sm font-medium text-gray-700">Table Participants:</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {tableParticipants.participants.map((participant) => {
                const isMe = participant.customer_session_id === customerSessionId;
                const isActive = participant.is_active;

                return (
                  <div
                    key={participant.id}
                    className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs border ${
                      isMe
                        ? 'bg-blue-100 border-blue-300 text-blue-700'
                        : isActive
                          ? 'bg-green-100 border-green-300 text-green-700'
                          : 'bg-gray-100 border-gray-300 text-gray-500'
                    }`}
                  >
                    <div className={`h-2 w-2 rounded-full ${
                      isMe ? 'bg-blue-500' : isActive ? 'bg-green-500' : 'bg-gray-400'
                    }`} />
                    <span>{participant.customer_name}{isMe ? ' (You)' : ''}</span>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Cart items */}
        {cartItemsDisplay.length > 0 ? (
          <div className="space-y-3">
            <Separator />
            <ScrollArea className="h-48">
              <AnimatePresence>
                {cartItemsDisplay.map((item) => (
                  <motion.div
                    key={item.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="flex items-center justify-between p-3 bg-white rounded-lg border mb-2"
                  >
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{item.name}</h4>
                      <div className="flex items-center space-x-2 text-sm text-gray-600">
                        <span>€{item.price.toFixed(2)} each</span>
                        <span>•</span>
                        <span>Added by {item.addedBy === customerSessionId ? 'You' : 'Other'}</span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      {item.canModify && !sharedCart.isLocked && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleUpdateQuantity(item.id, item.quantity - 1)}
                          disabled={item.quantity <= 1}
                        >
                          <Minus className="h-3 w-3" />
                        </Button>
                      )}

                      <span className="font-medium min-w-[2rem] text-center">
                        {item.quantity}
                      </span>

                      {item.canModify && !sharedCart.isLocked && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleUpdateQuantity(item.id, item.quantity + 1)}
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                      )}

                      {item.canModify && !sharedCart.isLocked && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleUpdateQuantity(item.id, 0)}
                          className="text-red-600 border-red-200 hover:bg-red-50"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      )}

                      <div className="text-right min-w-[4rem]">
                        <div className="font-semibold">
                          €{(item.price * item.quantity).toFixed(2)}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
            </ScrollArea>

            {/* Cart summary */}
            <Separator />
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Your items:</span>
                <span className="font-medium">€{myTotal.toFixed(2)}</span>
              </div>
              <div className="flex justify-between items-center text-lg font-semibold">
                <span>Total ({totalItems} items):</span>
                <span className="text-blue-700">€{totalAmount.toFixed(2)}</span>
              </div>
            </div>

            {/* Actions */}
            <div className="flex space-x-2 pt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleLockToggle}
                className="flex-1"
                disabled={sharedCart.isLocked && !sharedCart.lockedByMe}
              >
                {sharedCart.isLocked ? (
                  sharedCart.lockedByMe ? (
                    <>
                      <Unlock className="h-4 w-4 mr-1" />
                      Unlock Cart
                    </>
                  ) : (
                    <>
                      <Lock className="h-4 w-4 mr-1" />
                      Locked by Other
                    </>
                  )
                ) : (
                  <>
                    <Lock className="h-4 w-4 mr-1" />
                    Lock for Checkout
                  </>
                )}
              </Button>

              <Button
                onClick={handleCheckout}
                className="flex-1 bg-blue-600 hover:bg-blue-700"
                disabled={totalItems === 0 || (sharedCart.isLocked && !sharedCart.lockedByMe)}
              >
                <ShoppingCart className="h-4 w-4 mr-1" />
                Checkout
              </Button>
            </div>
          </div>
        ) : (
          // Empty cart state
          <div className="text-center py-8">
            <ShoppingCart className="h-12 w-12 mx-auto text-gray-400 mb-3" />
            <h3 className="text-lg font-medium text-gray-900 mb-1">Cart is empty</h3>
            <p className="text-gray-500 mb-4">Add items from the menu to get started</p>
            {tableParticipants.participantCount > 1 && (
              <p className="text-sm text-blue-600">
                Items added by any table participant will appear here
              </p>
            )}
          </div>
        )}

        {/* Last sync info */}
        <div className="mt-4 pt-3 border-t border-gray-200">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <div className="flex items-center space-x-1">
              <Activity className="h-3 w-3" />
              <span>Last sync: {lastSyncTime.toLocaleTimeString()}</span>
            </div>
            <div className="flex items-center space-x-1">
              {sharedCart.isJoining ? (
                <>
                  <RefreshCw className="h-3 w-3 animate-spin" />
                  <span>Joining...</span>
                </>
              ) : (
                <>
                  <CheckCircle className="h-3 w-3 text-green-500" />
                  <span>Connected</span>
                </>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SmartCollaborativeCartComplete;