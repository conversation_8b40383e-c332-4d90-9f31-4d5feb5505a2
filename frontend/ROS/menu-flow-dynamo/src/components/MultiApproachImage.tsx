import React, { useState, useEffect } from 'react';

interface MultiApproachImageProps {
  src: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
  fallbackSrc?: string;
}

/**
 * A component that tries multiple approaches to load an image
 * It will try different URL parameters and techniques until one works
 */
const MultiApproachImage: React.FC<MultiApproachImageProps> = ({
  src,
  alt,
  className,
  width,
  height,
  fallbackSrc = 'https://placehold.co/200x200?text=Menu'
}) => {
  const [currentSrc, setCurrentSrc] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(false);
  const [attemptCount, setAttemptCount] = useState(0);
  const [showFallback, setShowFallback] = useState(false);

  // Generate different approaches to try
  useEffect(() => {
    if (!src) {
      setShowFallback(true);
      return;
    }

    // Reset states when src changes
    setIsLoading(true);
    setError(false);
    setAttemptCount(0);
    setShowFallback(false);

    // Different approaches to try
    const approaches = [
      // Approach 1: Original URL
      src,
      // Approach 2: With download=true
      src.includes('?') ? `${src}&download=true` : `${src}?download=true`,
      // Approach 3: With transform=public
      src.includes('?') ? `${src}&transform=public` : `${src}?transform=public`,
      // Approach 4: With both download=true and transform=public
      src.includes('?') ? `${src}&download=true&transform=public` : `${src}?download=true&transform=public`,
      // Approach 5: With direct=true
      src.includes('?') ? `${src}&direct=true` : `${src}?direct=true`,
      // Approach 6: With all parameters
      src.includes('?') ? `${src}&download=true&transform=public&direct=true` : `${src}?download=true&transform=public&direct=true`,
    ];

    // Start with the first approach
    setCurrentSrc(approaches[0]);
    console.log(`MultiApproachImage: Starting with approach 1: ${approaches[0]}`);
  }, [src]);

  // Try the next approach when an error occurs
  useEffect(() => {
    if (!error || !src) return;

    // Different approaches to try
    const approaches = [
      // Approach 1: Original URL
      src,
      // Approach 2: With download=true
      src.includes('?') ? `${src}&download=true` : `${src}?download=true`,
      // Approach 3: With transform=public
      src.includes('?') ? `${src}&transform=public` : `${src}?transform=public`,
      // Approach 4: With both download=true and transform=public
      src.includes('?') ? `${src}&download=true&transform=public` : `${src}?download=true&transform=public`,
      // Approach 5: With direct=true
      src.includes('?') ? `${src}&direct=true` : `${src}?direct=true`,
      // Approach 6: With all parameters
      src.includes('?') ? `${src}&download=true&transform=public&direct=true` : `${src}?download=true&transform=public&direct=true`,
    ];

    // Move to the next approach
    const nextAttempt = attemptCount + 1;
    if (nextAttempt < approaches.length) {
      setAttemptCount(nextAttempt);
      setError(false);
      const nextSrc = approaches[nextAttempt];
      console.log(`MultiApproachImage: Trying approach ${nextAttempt + 1}: ${nextSrc}`);
      setCurrentSrc(nextSrc);
    } else {
      // All approaches failed, show fallback
      console.error(`MultiApproachImage: All approaches failed for ${src}`);
      setShowFallback(true);
    }
  }, [error, src, attemptCount]);

  const handleLoad = () => {
    console.log(`MultiApproachImage: Successfully loaded image from ${currentSrc}`);
    setIsLoading(false);
  };

  const handleError = () => {
    console.error(`MultiApproachImage: Failed to load image from ${currentSrc}`);
    setError(true);
  };

  // If all approaches failed, show the fallback
  if (showFallback) {
    return (
      <img
        src={fallbackSrc}
        alt={alt}
        className={className}
        width={width}
        height={height}
      />
    );
  }

  return (
    <>
      {isLoading && (
        <div className={`${className || ''} flex items-center justify-center bg-gray-100`}>
          <span className="text-xs text-gray-500">
            {error ? `Trying approach ${attemptCount + 1}/6...` : 'Loading...'}
          </span>
        </div>
      )}
      <img
        src={currentSrc}
        alt={alt}
        className={`${className || ''} ${isLoading ? 'hidden' : ''}`}
        width={width}
        height={height}
        onLoad={handleLoad}
        onError={handleError}
        crossOrigin="anonymous"
      />
    </>
  );
};

export default MultiApproachImage;
