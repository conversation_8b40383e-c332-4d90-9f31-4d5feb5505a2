"use client";

import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { motion } from "framer-motion";
import { Button } from '@/components/ui/button';
import { QrCode, Utensils, Clock, Globe } from 'lucide-react';
import { useLanguage, Language } from '@/contexts/LanguageContext';
import { useRestaurant } from '@/contexts/RestaurantContext';

export default function HeroSection() {
  const { language, setLanguage, t } = useLanguage();
  const { restaurantInfo, isLoading } = useRestaurant();
  return (
    <div className="relative mx-auto my-10 flex max-w-7xl flex-col items-center justify-center">
      <Navbar />
      <div className="absolute inset-y-0 left-0 h-full w-px bg-restaurant-primary/20">
        <motion.div
          className="absolute top-0 h-40 w-px bg-gradient-to-b from-transparent via-restaurant-secondary to-transparent"
          animate={{
            top: ["0%", "100%", "0%"],
          }}
          transition={{
            duration: 8,
            ease: "easeInOut",
            repeat: Infinity,
            repeatType: "loop"
          }}
        />
      </div>
      <div className="absolute inset-y-0 right-0 h-full w-px bg-restaurant-primary/20">
        <motion.div
          className="absolute h-40 w-px bg-gradient-to-b from-transparent via-restaurant-secondary to-transparent"
          animate={{
            top: ["100%", "0%", "100%"],
          }}
          transition={{
            duration: 8,
            ease: "easeInOut",
            repeat: Infinity,
            repeatType: "loop"
          }}
        />
      </div>
      <div className="absolute inset-x-0 bottom-0 h-px w-full bg-restaurant-primary/20">
        <motion.div
          className="absolute h-px w-40 bg-gradient-to-r from-transparent via-restaurant-secondary to-transparent"
          style={{ left: "calc(50% - 80px)" }}
          animate={{
            left: ["0%", "calc(100% - 160px)", "0%"],
          }}
          transition={{
            duration: 8,
            ease: "easeInOut",
            repeat: Infinity,
            repeatType: "loop"
          }}
        />
      </div>
      <div className="px-4 py-10 md:py-20">
        <h1 className="relative z-10 mx-auto max-w-4xl text-center text-2xl font-bold text-restaurant-text md:text-4xl lg:text-6xl">
          {isLoading ? (
            <motion.span
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="inline-block"
            >
              {t('welcomeToRestaurant')}
            </motion.span>
          ) : (
            `${language === 'en' ? 'Welcome to' : 'Bienvenido a'} ${restaurantInfo.name}`
              .split(" ")
              .map((word, index) => (
                <motion.span
                  key={index}
                  initial={{ opacity: 0, filter: "blur(4px)", y: 10 }}
                  animate={{ opacity: 1, filter: "blur(0px)", y: 0 }}
                  transition={{
                    duration: 0.3,
                    delay: index * 0.1,
                    ease: "easeInOut",
                  }}
                  className="mr-2 inline-block"
                >
                  {word}
                </motion.span>
              ))
          )}
        </h1>
        <motion.p
          initial={{
            opacity: 0,
          }}
          animate={{
            opacity: 1,
          }}
          transition={{
            duration: 0.3,
            delay: 0.8,
          }}
          className="relative z-10 mx-auto max-w-xl py-4 text-center text-lg font-normal text-restaurant-muted"
        >
          {t('enjoyDiningExperience')}
        </motion.p>
        <motion.div
          initial={{
            opacity: 0,
          }}
          animate={{
            opacity: 1,
          }}
          transition={{
            duration: 0.3,
            delay: 1,
          }}
          className="relative z-10 mt-8 flex flex-wrap items-center justify-center gap-4"
        >
          <Link to="/menu">
            <Button className="w-60 transform rounded-lg bg-restaurant-primary px-6 py-2 font-medium text-white transition-all duration-300 hover:-translate-y-0.5 hover:bg-restaurant-primary/90">
              {t('viewMenuAndOrder')}
            </Button>
          </Link>
          <Link to="/admin">
            <Button className="w-60 transform rounded-lg border border-restaurant-primary/30 bg-restaurant-card px-6 py-2 font-medium text-restaurant-text transition-all duration-300 hover:-translate-y-0.5 hover:bg-restaurant-card/80">
              {t('restaurantOwnerLogin')}
            </Button>
          </Link>
        </motion.div>

        <motion.div
          initial={{
            opacity: 0,
            y: 10,
          }}
          animate={{
            opacity: 1,
            y: 0,
          }}
          transition={{
            duration: 0.3,
            delay: 1.2,
          }}
          className="relative z-10 mt-20"
        >
          <div className="grid md:grid-cols-3 gap-6">
            <motion.div
              className="bg-restaurant-card p-6 rounded-xl border border-restaurant-primary/20 shadow-lg overflow-hidden relative"
              whileHover={{ y: -5, transition: { duration: 0.2 } }}
            >
              <div className="absolute top-0 right-0 w-24 h-24 bg-restaurant-secondary/10 rounded-bl-full -mr-8 -mt-8"></div>
              <div className="bg-restaurant-primary/10 p-3 rounded-full mb-4 w-16 h-16 flex items-center justify-center">
                <QrCode className="h-8 w-8 text-restaurant-secondary" />
              </div>
              <h3 className="font-semibold mb-2 text-restaurant-text text-xl">{t('scanQrCode')}</h3>
              <p className="text-restaurant-muted">
                {t('scanQrCodeDesc')}
              </p>
              <motion.div
                className="absolute bottom-0 left-0 h-1 bg-restaurant-secondary"
                initial={{ width: 0 }}
                whileHover={{ width: "100%" }}
                transition={{ duration: 0.3 }}
              />
            </motion.div>

            <motion.div
              className="bg-restaurant-card p-6 rounded-xl border border-restaurant-primary/20 shadow-lg overflow-hidden relative"
              whileHover={{ y: -5, transition: { duration: 0.2 } }}
            >
              <div className="absolute top-0 right-0 w-24 h-24 bg-restaurant-secondary/10 rounded-bl-full -mr-8 -mt-8"></div>
              <div className="bg-restaurant-primary/10 p-3 rounded-full mb-4 w-16 h-16 flex items-center justify-center">
                <Utensils className="h-8 w-8 text-restaurant-secondary" />
              </div>
              <h3 className="font-semibold mb-2 text-restaurant-text text-xl">{t('placeYourOrder')}</h3>
              <p className="text-restaurant-muted">
                {t('placeYourOrderDesc')}
              </p>
              <motion.div
                className="absolute bottom-0 left-0 h-1 bg-restaurant-secondary"
                initial={{ width: 0 }}
                whileHover={{ width: "100%" }}
                transition={{ duration: 0.3 }}
              />
            </motion.div>

            <motion.div
              className="bg-restaurant-card p-6 rounded-xl border border-restaurant-primary/20 shadow-lg overflow-hidden relative"
              whileHover={{ y: -5, transition: { duration: 0.2 } }}
            >
              <div className="absolute top-0 right-0 w-24 h-24 bg-restaurant-secondary/10 rounded-bl-full -mr-8 -mt-8"></div>
              <div className="bg-restaurant-primary/10 p-3 rounded-full mb-4 w-16 h-16 flex items-center justify-center">
                <Clock className="h-8 w-8 text-restaurant-secondary" />
              </div>
              <h3 className="font-semibold mb-2 text-restaurant-text text-xl">{t('enjoyYourMeal')}</h3>
              <p className="text-restaurant-muted">
                {t('enjoyYourMealDesc')}
              </p>
              <motion.div
                className="absolute bottom-0 left-0 h-1 bg-restaurant-secondary"
                initial={{ width: 0 }}
                whileHover={{ width: "100%" }}
                transition={{ duration: 0.3 }}
              />
            </motion.div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}

const Navbar = () => {
  const { language, setLanguage, t } = useLanguage();
  const { restaurantInfo, isLoading } = useRestaurant();

  const toggleLanguage = () => {
    setLanguage(language === 'en' ? 'es' : 'en');
  };

  return (
    <motion.nav
      className="flex w-full items-center justify-between border-t border-b border-restaurant-primary/20 px-4 py-4"
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        className="flex items-center gap-2"
        whileHover={{ scale: 1.05 }}
        transition={{ type: "spring", stiffness: 400, damping: 10 }}
      >
        <motion.div
          className="size-8 rounded-full bg-gradient-to-br from-restaurant-secondary to-restaurant-primary"
          animate={{
            boxShadow: [
              "0 0 0 0 rgba(17, 165, 232, 0)",
              "0 0 0 4px rgba(17, 165, 232, 0.3)",
              "0 0 0 0 rgba(17, 165, 232, 0)"
            ]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            repeatType: "loop"
          }}
        />
        <h1 className="text-base font-bold md:text-2xl text-restaurant-text">
          {isLoading ? 'Loading...' : restaurantInfo.name}
        </h1>
      </motion.div>
      <div className="flex items-center gap-3">
        <motion.button
          onClick={toggleLanguage}
          className="flex items-center gap-1 px-3 py-1 rounded-md bg-restaurant-card border border-restaurant-primary/20 text-restaurant-text"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <Globe className="h-4 w-4 text-restaurant-secondary" />
          <span>{language === 'en' ? 'ES' : 'EN'}</span>
        </motion.button>

        <Link to="/admin">
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Button className="transform rounded-lg bg-restaurant-primary px-6 py-2 font-medium text-white transition-all duration-300 hover:bg-restaurant-primary/90 md:w-32">
              {t('login')}
            </Button>
          </motion.div>
        </Link>
      </div>
    </motion.nav>
  );
};
