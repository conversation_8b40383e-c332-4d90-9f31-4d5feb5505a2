/**
 * Rating Component
 * Star rating component for feedback collection
 */

import React from 'react';
import { Star } from 'lucide-react';
import { cn } from '@/lib/utils';

interface RatingProps {
  value: number;
  onChange: (value: number) => void;
  max?: number;
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  className?: string;
}

export function Rating({
  value,
  onChange,
  max = 5,
  size = 'md',
  disabled = false,
  className
}: RatingProps) {
  const sizes = {
    sm: 'w-3.5 h-3.5',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };
  
  const sizeClass = sizes[size];
  
  return (
    <div className={cn('flex items-center gap-1', className)}>
      {Array.from({ length: max }).map((_, index) => {
        const starValue = index + 1;
        const isFilled = value >= starValue;
        
        return (
          <button
            key={index}
            type="button"
            className={cn(
              'focus:outline-none transition-colors',
              disabled ? 'cursor-default' : 'cursor-pointer'
            )}
            onClick={() => !disabled && onChange(starValue)}
            disabled={disabled}
            aria-label={`${starValue} stars`}
          >
            <Star
              className={cn(
                sizeClass,
                'transition-colors',
                isFilled 
                  ? 'fill-amber-400 text-amber-400' 
                  : 'fill-none text-gray-300'
              )}
            />
          </button>
        );
      })}
    </div>
  );
}
