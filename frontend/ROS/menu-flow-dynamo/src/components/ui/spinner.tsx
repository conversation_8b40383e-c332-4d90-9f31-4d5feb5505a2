import React from 'react';
import { cn } from '@/lib/utils';

type SpinnerProps = React.HTMLAttributes<HTMLDivElement>;

/**
 * A simple spinner component for loading states
 * Uses the sky blue accent color (#0ea5e9) to match the Analytica brand
 */
export function Spinner({ className, ...props }: SpinnerProps) {
  return (
    <div
      className={cn(
        "inline-block h-6 w-6 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]",
        "text-sky-500", // Sky blue accent color (#0ea5e9)
        className
      )}
      role="status"
      aria-label="Loading"
      {...props}
    >
      <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">
        Loading...
      </span>
    </div>
  );
}
