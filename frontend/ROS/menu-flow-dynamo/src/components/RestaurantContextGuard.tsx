import React from 'react';
import { useRestaurant } from '@/contexts/RestaurantContext';

// Create a simple spinner component directly in this file
// This avoids the import error and keeps the component self-contained
const Spinner = ({ className = '', ...props }: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={`inline-block h-6 w-6 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] text-sky-500 ${className}`}
    role="status"
    aria-label="Loading"
    {...props}
  >
    <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">
      Loading...
    </span>
  </div>
);

interface RestaurantContextGuardProps {
  children: React.ReactNode;
}

/**
 * A component that ensures the restaurant context is loaded before rendering its children
 * This prevents components from trying to use the restaurant context before it's ready
 */
export function RestaurantContextGuard({ children }: RestaurantContextGuardProps) {
  const { restaurantInfo, isLoading } = useRestaurant();

  // If the context is still loading, show a spinner
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[50vh]">
        <Spinner className="h-8 w-8 text-sky-500" />
        <span className="ml-2 text-gray-600">Loading restaurant data...</span>
      </div>
    );
  }

  // If the restaurant ID is missing, show an error
  if (!restaurantInfo || !restaurantInfo.id) {
    return (
      <div className="p-6 max-w-md mx-auto bg-white rounded-lg shadow-md">
        <h2 className="text-xl font-semibold text-red-600 mb-4">Restaurant Not Found</h2>
        <p className="text-gray-700 mb-4">
          We couldn't find the restaurant information. This could be because:
        </p>
        <ul className="list-disc pl-5 mb-4 text-gray-600">
          <li>You're not logged in</li>
          <li>You don't have access to any restaurants</li>
          <li>There was an error loading the restaurant data</li>
        </ul>
        <p className="text-gray-700">
          Please try refreshing the page or contact support if the problem persists.
        </p>
      </div>
    );
  }

  // Restaurant context is loaded and valid, render children
  return <>{children}</>;
}
