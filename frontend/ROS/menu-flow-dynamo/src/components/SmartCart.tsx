import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import {
  ShoppingCart,
  Plus,
  Minus,
  Trash2,
  Users,
  User,
  Lock,
  Unlock,
  RefreshCw,
  AlertCircle,
  Settings
} from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { UseSmartCartReturn } from '@/hooks/useSmartCart';

interface SmartCartProps {
  cart: UseSmartCartReturn;
  isOpen: boolean;
  onClose: () => void;
  onCheckout: () => void;
  orderComment?: string;
  onCommentChange?: (comment: string) => void;
}

const SmartCart: React.FC<SmartCartProps> = ({
  cart,
  isOpen,
  onClose,
  onCheckout,
  orderComment = '',
  onCommentChange
}) => {
  const { t } = useLanguage();
  const [showModeSwitch, setShowModeSwitch] = useState(false);

  if (!isOpen) return null;

  const {
    mode,
    items,
    totalAmount,
    participantCount,
    isLocked,
    lockedByMe,
    customerName,
    participantMap,
    currentSessionId,
    isLoading,
    error,
    isInitialized,
    addItem,
    removeItem,
    updateQuantity,
    switchMode,
    lockForCheckout,
    unlockCart,
    getItemsByCustomer,
    canModifyItem,
    forceRefreshMode
  } = cart;

  // Mode icon and text
  const getModeInfo = () => {
    switch (mode) {
      case 'collaborative':
        return {
          icon: <Users className="h-4 w-4" />,
          text: 'Collaborative Cart',
          description: `${participantCount} people at table`
        };
      case 'individual':
        return {
          icon: <User className="h-4 w-4" />,
          text: 'Individual Cart',
          description: 'Your personal order'
        };
      case 'detecting':
        return {
          icon: <RefreshCw className="h-4 w-4 animate-spin" />,
          text: 'Detecting...',
          description: 'Setting up your cart'
        };
    }
  };

  const modeInfo = getModeInfo();

  // Handle quantity changes
  const handleIncreaseQuantity = (itemId: string) => {

    const item = items.find(i => i.id === itemId);
    if (item) {

      updateQuantity(itemId, item.quantity + 1);
    } else {

    }
  };

  const handleDecreaseQuantity = (itemId: string) => {

    const item = items.find(i => i.id === itemId);
    if (item) {

      if (item.quantity > 1) {
        updateQuantity(itemId, item.quantity - 1);
      } else {

        removeItem(itemId);
      }
    } else {

    }
  };

  // Handle checkout
  const handleCheckout = async () => {
    if (mode === 'collaborative' && !isLocked) {
      const locked = await lockForCheckout();
      if (!locked) {
        return; // Cart couldn't be locked
      }
    }
    onCheckout();
  };

  // Group items by customer for collaborative mode
  const groupedItems = mode === 'collaborative'
    ? items.reduce((acc, item) => {
        const sessionId = item.added_by_session || 'unknown';
        if (!acc[sessionId]) {
          acc[sessionId] = [];
        }
        acc[sessionId].push(item);
        return acc;
      }, {} as Record<string, typeof items>)
    : { [customerName]: items };

  // Helper function to check if item can be modified
  const canModifyItemDebug = (item: any) => {
    const canModify = canModifyItem(item);

    return canModify;
  };

  // Helper function to get display name for a session ID
  const getDisplayName = (sessionId: string) => {

    // Check if this is the current user's session ID
    if (sessionId === currentSessionId) {
      return 'You';
    }

    // Try to get the customer name from participant map
    const customerDisplayName = participantMap[sessionId];

    if (customerDisplayName) {
      // Return the customer name (e.g., "Customer 1", "Customer 2")
      return customerDisplayName;
    }

    // Final fallback: show shortened session ID
    if (sessionId && sessionId.startsWith('restaurant_mgmt_')) {
      return sessionId.split('_').pop()?.slice(-4) || sessionId;
    }

    return sessionId || 'Unknown';
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-end z-50">
      <div className="bg-white h-full w-full max-w-sm flex flex-col shadow-2xl animate-in slide-in-from-right duration-300">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b bg-gradient-to-r from-sky-50 to-blue-50">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <div className="p-2 bg-white rounded-full shadow-sm border border-sky-100">
                {modeInfo.icon}
              </div>
              <div>
                <h2 className="text-lg font-semibold text-gray-800">{modeInfo.text}</h2>
                <p className="text-sm text-gray-600">{modeInfo.description}</p>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {/* Mode refresh button */}
            {isInitialized && mode !== 'detecting' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => forceRefreshMode()}
                className="text-gray-500 hover:bg-white/60 h-8 w-8 p-0"
                title="Refresh cart mode detection"
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            )}

            {/* Mode switch button */}
            {isInitialized && mode !== 'detecting' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowModeSwitch(!showModeSwitch)}
                className="text-gray-500 hover:bg-white/60 h-8 w-8 p-0"
                title="Switch cart mode"
              >
                <Settings className="h-4 w-4" />
              </Button>
            )}

            {/* Lock/Unlock controls for collaborative mode */}
            {mode === 'collaborative' && (
              <div className="flex items-center space-x-1">
                {isLocked ? (
                  <Badge variant={lockedByMe ? "default" : "secondary"} className="text-xs bg-sky-100 text-sky-800">
                    <Lock className="h-3 w-3 mr-1" />
                    {lockedByMe ? 'Locked by you' : 'Locked'}
                  </Badge>
                ) : (
                  <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                    <Unlock className="h-3 w-3 mr-1" />
                    Unlocked
                  </Badge>
                )}

                {/* Lock/Unlock button */}
                {lockedByMe ? (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={unlockCart}
                    className="text-sky-600 hover:bg-sky-50 h-8 w-8 p-0"
                    title="Unlock cart"
                  >
                    <Unlock className="h-4 w-4" />
                  </Button>
                ) : !isLocked ? (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={lockForCheckout}
                    className="text-amber-600 hover:bg-amber-50 h-8 w-8 p-0"
                    title="Lock cart"
                  >
                    <Lock className="h-4 w-4" />
                  </Button>
                ) : null}
              </div>
            )}

            {/* Close button */}
            <Button variant="ghost" size="sm" onClick={onClose} className="hover:bg-white/60 h-8 w-8 p-0">
              <svg
                className="h-5 w-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                strokeWidth={2}
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M18 6 6 18" />
                <path d="m6 6 12 12" />
              </svg>
            </Button>
          </div>
        </div>

        {/* Mode switch panel */}
        {showModeSwitch && (
          <div className="p-3 bg-gray-50 border-b">
            <div className="space-y-2">
              <Button
                variant={mode === 'individual' ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  switchMode('individual');
                  setShowModeSwitch(false);
                }}
                className={`w-full justify-start text-sm ${mode === 'individual' ? 'bg-sky-500 hover:bg-sky-600' : 'hover:bg-sky-50 hover:border-sky-200'}`}
              >
                <User className="h-4 w-4 mr-2" />
                Individual Cart
              </Button>
              <Button
                variant={mode === 'collaborative' ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  switchMode('collaborative');
                  setShowModeSwitch(false);
                }}
                className={`w-full justify-start text-sm ${mode === 'collaborative' ? 'bg-sky-500 hover:bg-sky-600' : 'hover:bg-sky-50 hover:border-sky-200'}`}
              >
                <Users className="h-4 w-4 mr-2" />
                Collaborative Cart
              </Button>
            </div>
          </div>
        )}

        {/* Error display */}
        {error && (
          <div className="p-4 bg-red-50 border-b">
            <div className="flex items-center space-x-2 text-red-600">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">{error}</span>
            </div>
          </div>
        )}

        {/* Loading state */}
        {(isLoading || mode === 'detecting' || !isInitialized) && (
          <div className="p-4 text-center">
            <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
            <p className="text-sm text-gray-500">
              {mode === 'detecting' || !isInitialized ? 'Setting up your cart...' : 'Updating cart...'}
            </p>
            <div className="mt-2 text-xs text-gray-400">
              {mode === 'detecting' ? 'Detecting collaboration mode...' :
               !isInitialized ? 'Initializing cart system...' :
               'Syncing cart data...'}
            </div>
          </div>
        )}

        {/* Empty cart - only show when fully initialized and not detecting */}
        {!isLoading && mode !== 'detecting' && isInitialized && items.length === 0 && (
          <div className="flex-1 flex flex-col items-center justify-center p-6">
            <div className="bg-sky-50 rounded-full p-6 mb-4">
              <ShoppingCart className="h-12 w-12 text-sky-400" />
            </div>
            <h3 className="text-lg font-semibold mb-2 text-gray-800">
              {t('yourCartIsEmpty')}
            </h3>
            <p className="text-gray-500 text-center text-sm max-w-xs">
              {mode === 'collaborative'
                ? 'Add items to share with your table'
                : t('addItemsToStart')
              }
            </p>
          </div>
        )}

        {/* Cart items - only show when fully initialized */}
        {!isLoading && mode !== 'detecting' && isInitialized && items.length > 0 && (
          <>
            <ScrollArea className="flex-1">
              <div className="p-4 space-y-3">
                {Object.entries(groupedItems).map(([sessionId, customerItems]) => (
                  <div key={sessionId}>
                    {/* Customer header for collaborative mode */}
                    {mode === 'collaborative' && Object.keys(groupedItems).length > 1 && (
                      <div className="flex items-center space-x-2 mb-3 p-2 bg-gray-50 rounded-lg">
                        <User className="h-4 w-4 text-sky-600" />
                        <span className="text-sm font-medium text-gray-700">
                          {getDisplayName(sessionId)}
                        </span>
                        <Badge variant="outline" className="text-xs bg-sky-50 text-sky-700 border-sky-200">
                          {(customerItems as typeof items).length} items
                        </Badge>
                      </div>
                    )}

                    {/* Items for this customer */}
                    <div className="space-y-2">
                      {(customerItems as typeof items).map((item) => (
                        <div key={`${item.id}-${item.customerSessionId}`} className="bg-white border border-gray-100 rounded-lg p-3 shadow-sm hover:shadow-md transition-shadow">
                          <div className="flex items-center justify-between">
                            <div className="flex-1 min-w-0">
                              <h4 className="font-medium text-gray-800 text-sm truncate">{item.name}</h4>
                              <p className="text-xs text-gray-600">€{item.price.toFixed(2)} each</p>
                              {item.quantity > 1 && (
                                <p className="text-xs text-sky-600 font-medium">
                                  €{(item.price * item.quantity).toFixed(2)} total
                                </p>
                              )}
                            </div>

                            <div className="flex items-center space-x-1 ml-3">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  console.log('➖ SmartCart: Decrease button clicked for item:', item.id, item.name);
                                  handleDecreaseQuantity(item.id);
                                }}
                                disabled={!canModifyItemDebug(item) || isLocked}
                                className={`h-7 w-7 p-0 border-gray-200 transition-colors ${
                                  (!canModifyItemDebug(item) || isLocked)
                                    ? 'opacity-50 cursor-not-allowed'
                                    : 'hover:border-sky-300 hover:bg-sky-50'
                                }`}
                                title={(!canModifyItemDebug(item) || isLocked) ? 'Cannot modify this item' : 'Decrease quantity'}
                              >
                                <Minus className="h-3 w-3" />
                              </Button>

                              <span className="w-8 text-center text-sm font-medium text-gray-700">
                                {item.quantity}
                              </span>

                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {

                                  handleIncreaseQuantity(item.id);
                                }}
                                disabled={!canModifyItemDebug(item) || isLocked}
                                className={`h-7 w-7 p-0 border-gray-200 transition-colors ${
                                  (!canModifyItemDebug(item) || isLocked)
                                    ? 'opacity-50 cursor-not-allowed'
                                    : 'hover:border-sky-300 hover:bg-sky-50'
                                }`}
                                title={(!canModifyItemDebug(item) || isLocked) ? 'Cannot modify this item' : 'Increase quantity'}
                              >
                                <Plus className="h-3 w-3" />
                              </Button>

                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {

                                  removeItem(item.id);
                                }}
                                disabled={!canModifyItemDebug(item) || isLocked}
                                className={`h-7 w-7 p-0 text-red-500 ml-1 transition-colors ${
                                  (!canModifyItemDebug(item) || isLocked)
                                    ? 'opacity-50 cursor-not-allowed'
                                    : 'hover:text-red-700 hover:bg-red-50'
                                }`}
                                title={(!canModifyItemDebug(item) || isLocked) ? 'Cannot modify this item' : 'Remove item'}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>

            {/* Footer with total and checkout */}
            <div className="border-t bg-white p-4 space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-lg font-semibold text-gray-800">Total</span>
                <span className="text-xl font-bold text-sky-600">€{totalAmount.toFixed(2)}</span>
              </div>

              {mode === 'collaborative' && (
                <div className={`rounded-lg p-2 border ${
                  isLocked
                    ? 'bg-red-50 border-red-200'
                    : 'bg-amber-50 border-amber-200'
                }`}>
                  <p className={`text-xs text-center ${
                    isLocked
                      ? 'text-red-700'
                      : 'text-amber-700'
                  }`}>
                    {isLocked
                      ? (lockedByMe
                          ? '🔒 Cart locked by you - others cannot modify items'
                          : '🔒 Cart locked by another customer')
                      : '💡 Anyone can lock the cart to prevent changes from others'
                    }
                  </p>
                </div>
              )}

              {/* Special Instructions Comment Section - DEBUG STYLING */}
              <div className="border-2 border-red-500 bg-yellow-50 p-4 rounded-lg">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('specialInstructions')}
                </label>
                <Textarea
                  placeholder={t('specialInstructionsPlaceholder')}
                  value={orderComment}
                  onChange={(e) => onCommentChange?.(e.target.value)}
                  className="w-full resize-none border-gray-300 focus:border-sky-500 focus:ring-sky-500"
                  rows={3}
                  maxLength={500}
                  disabled={isLocked && !lockedByMe}
                />
                <div className="flex justify-between items-center mt-1">
                  <span className="text-xs text-gray-500">
                    {t('optional')}
                  </span>
                  <span className="text-xs text-gray-500">
                    {orderComment.length}/500
                  </span>
                </div>
              </div>

              <Button
                onClick={handleCheckout}
                disabled={isLoading || items.length === 0}
                className="w-full bg-sky-500 hover:bg-sky-600 text-white font-medium py-3 rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
              >
                {isLoading ? (
                  <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <ShoppingCart className="h-4 w-4 mr-2" />
                )}
                {mode === 'collaborative' && !isLocked ? 'Lock & Checkout' : 'Checkout'}
              </Button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default SmartCart;
