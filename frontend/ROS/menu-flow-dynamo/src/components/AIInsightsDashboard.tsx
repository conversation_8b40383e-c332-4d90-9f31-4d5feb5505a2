/**
 * AI Insights Dashboard Component
 * Displays enhanced AI analysis and sentiment insights for restaurant management
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AlertCircle, TrendingUp, MessageSquare, Brain, Lightbulb } from 'lucide-react';
import { AIInsightsService } from '@/services/aiInsightsService';

interface AIInsightsDashboardProps {
  restaurantId: string;
}

interface InsightData {
  type: 'sentiment' | 'competitor' | 'growth' | 'market';
  title: string;
  content: string;
  confidence: number;
  recommendations: string[];
  timestamp: string;
  loading?: boolean;
}

export const AIInsightsDashboard: React.FC<AIInsightsDashboardProps> = ({ restaurantId }) => {
  const [insights, setInsights] = useState<InsightData[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('sentiment');
  const [connectionStatus, setConnectionStatus] = useState<boolean | null>(null);

  useEffect(() => {
    checkConnection();
    loadInitialInsights();
  }, [restaurantId, checkConnection, loadInitialInsights]);

  const checkConnection = useCallback(async () => {
    try {
      const connected = await AIInsightsService.testConnection();
      setConnectionStatus(connected);
    } catch (error) {
      console.error('Connection check failed:', error);
      setConnectionStatus(false);
    }
  }, []);

  const loadInitialInsights = useCallback(async () => {
    setLoading(true);
    try {
      const sentimentInsight = await loadInsight('sentiment');
      setInsights([sentimentInsight]);
    } catch (error) {
      console.error('Failed to load initial insights:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  const loadInsight = async (type: 'sentiment' | 'competitor' | 'growth' | 'market'): Promise<InsightData> => {
    try {
      const response = await AIInsightsService.getRestaurantInsights(restaurantId, type);
      
      return {
        type,
        title: `${type.charAt(0).toUpperCase() + type.slice(1)} Analysis`,
        content: response.analysis.summary,
        confidence: response.analysis.confidence,
        recommendations: response.analysis.recommendations || [],
        timestamp: response.timestamp
      };
    } catch (error) {
      console.error(`Failed to load ${type} insight:`, error);
      return {
        type,
        title: `${type.charAt(0).toUpperCase() + type.slice(1)} Analysis`,
        content: 'AI analysis temporarily unavailable. Please try again later.',
        confidence: 0,
        recommendations: [],
        timestamp: new Date().toISOString()
      };
    }
  };

  const handleTabChange = async (value: string) => {
    setActiveTab(value);
    
    // Check if we already have this insight
    const existingInsight = insights.find(insight => insight.type === value);
    if (existingInsight) return;

    // Load new insight
    setLoading(true);
    try {
      const newInsight = await loadInsight(value as 'demand' | 'optimization' | 'seasonal' | 'personalization');
      setInsights(prev => [...prev, newInsight]);
    } catch (error) {
      console.error(`Failed to load ${value} insight:`, error);
    } finally {
      setLoading(false);
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getTabIcon = (type: string) => {
    switch (type) {
      case 'sentiment': return <MessageSquare className="w-4 h-4" />;
      case 'competitor': return <TrendingUp className="w-4 h-4" />;
      case 'growth': return <Lightbulb className="w-4 h-4" />;
      case 'market': return <Brain className="w-4 h-4" />;
      default: return <AlertCircle className="w-4 h-4" />;
    }
  };

  if (connectionStatus === false) {
    return (
      <Card className="border-red-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <AlertCircle className="w-5 h-5" />
            AI Insights Unavailable
          </CardTitle>
          <CardDescription>
            Unable to connect to AI insights service. Please check your connection and try again.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={checkConnection} variant="outline">
            Retry Connection
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">🤖 AI Insights Dashboard</h2>
          <p className="text-muted-foreground">
            Enhanced AI analysis powered by advanced machine learning models
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={connectionStatus ? 'default' : 'secondary'}>
            {connectionStatus ? '✅ AI Connected' : '🔄 Checking...'}
          </Badge>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="sentiment" className="flex items-center gap-2">
            {getTabIcon('sentiment')}
            Sentiment
          </TabsTrigger>
          <TabsTrigger value="competitor" className="flex items-center gap-2">
            {getTabIcon('competitor')}
            Competitor
          </TabsTrigger>
          <TabsTrigger value="growth" className="flex items-center gap-2">
            {getTabIcon('growth')}
            Growth
          </TabsTrigger>
          <TabsTrigger value="market" className="flex items-center gap-2">
            {getTabIcon('market')}
            Market
          </TabsTrigger>
        </TabsList>

        {['sentiment', 'competitor', 'growth', 'market'].map((type) => {
          const insight = insights.find(i => i.type === type);
          
          return (
            <TabsContent key={type} value={type} className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      {getTabIcon(type)}
                      {insight?.title || `${type.charAt(0).toUpperCase() + type.slice(1)} Analysis`}
                    </span>
                    {insight && (
                      <Badge className={getConfidenceColor(insight.confidence)}>
                        {Math.round(insight.confidence * 100)}% Confidence
                      </Badge>
                    )}
                  </CardTitle>
                  {insight && (
                    <CardDescription>
                      Last updated: {new Date(insight.timestamp).toLocaleString()}
                    </CardDescription>
                  )}
                </CardHeader>
                <CardContent>
                  {loading && !insight ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                      <span className="ml-2">Loading AI analysis...</span>
                    </div>
                  ) : insight ? (
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-medium mb-2">Analysis Summary</h4>
                        <p className="text-sm text-muted-foreground leading-relaxed">
                          {insight.content}
                        </p>
                      </div>
                      
                      {insight.recommendations.length > 0 && (
                        <div>
                          <h4 className="font-medium mb-2 flex items-center gap-2">
                            <Lightbulb className="w-4 h-4" />
                            Recommendations
                          </h4>
                          <ul className="space-y-2">
                            {insight.recommendations.map((rec, index) => (
                              <li key={index} className="flex items-start gap-2 text-sm">
                                <span className="text-primary">•</span>
                                <span>{rec}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <Brain className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p>AI analysis will load automatically</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          );
        })}
      </Tabs>
    </div>
  );
};