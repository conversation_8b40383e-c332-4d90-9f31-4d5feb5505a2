import React, { useState, useEffect } from 'react';

interface SupabaseDirectImageProps {
  src: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
  fallbackSrc?: string;
}

/**
 * A component specifically designed to handle Supabase storage images
 * It directly embeds the image URL with the correct parameters
 */
const SupabaseDirectImage: React.FC<SupabaseDirectImageProps> = ({
  src,
  alt,
  className,
  width,
  height,
  fallbackSrc = 'https://placehold.co/200x200?text=Menu'
}) => {
  const [imageSrc, setImageSrc] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(false);

  useEffect(() => {
    if (!src) {
      setError(true);
      setIsLoading(false);
      return;
    }

    // For Supabase URLs, modify to ensure it works
    let modifiedSrc = src;
    if (src.includes('supabase.co')) {
      // Add download=true parameter and transform=public
      const hasParams = src.includes('?');
      modifiedSrc = `${src}${hasParams ? '&' : '?'}download=true&transform=public`;
      
      console.log(`SupabaseDirectImage: Modified URL: ${modifiedSrc}`);
    }

    setImageSrc(modifiedSrc);
  }, [src]);

  const handleLoad = () => {
    console.log(`SupabaseDirectImage: Successfully loaded image from ${imageSrc}`);
    setIsLoading(false);
  };

  const handleError = () => {
    console.error(`SupabaseDirectImage: Failed to load image from ${imageSrc}`);
    
    // Try a different approach if the first one fails
    if (src && src.includes('supabase.co') && !imageSrc.includes('&direct=true')) {
      const directSrc = `${imageSrc}&direct=true`;
      console.log(`SupabaseDirectImage: Trying with direct=true: ${directSrc}`);
      setImageSrc(directSrc);
    } else {
      setError(true);
      setIsLoading(false);
    }
  };

  // If there was an error loading the image, show the fallback
  if (error) {
    return (
      <img
        src={fallbackSrc}
        alt={alt}
        className={className}
        width={width}
        height={height}
      />
    );
  }

  return (
    <>
      {isLoading && (
        <div className={`${className || ''} flex items-center justify-center bg-gray-100`}>
          <span className="text-xs text-gray-500">Loading...</span>
        </div>
      )}
      <img
        src={imageSrc}
        alt={alt}
        className={`${className || ''} ${isLoading ? 'hidden' : ''}`}
        width={width}
        height={height}
        onLoad={handleLoad}
        onError={handleError}
        crossOrigin="anonymous"
      />
    </>
  );
};

export default SupabaseDirectImage;
