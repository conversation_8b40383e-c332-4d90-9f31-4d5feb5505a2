import React, { useState, useEffect } from 'react';

interface DataUrlImageProps {
  src: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
  fallbackSrc?: string;
}

/**
 * A component that converts an image to a data URL using a canvas
 */
const DataUrlImage: React.FC<DataUrlImageProps> = ({
  src,
  alt,
  className,
  width,
  height,
  fallbackSrc = 'https://placehold.co/200x200?text=Menu'
}) => {
  const [dataUrl, setDataUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(false);

  useEffect(() => {
    if (!src) {
      setError(true);
      setIsLoading(false);
      return;
    }

    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    img.onload = () => {
      try {
        // Create a canvas to convert the image to a data URL
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        
        const ctx = canvas.getContext('2d');
        if (!ctx) {
          throw new Error('Failed to get canvas context');
        }
        
        // Draw the image on the canvas
        ctx.drawImage(img, 0, 0);
        
        // Convert to data URL
        const dataUrl = canvas.toDataURL('image/jpeg');
        console.log(`DataUrlImage: Successfully converted to data URL, length: ${dataUrl.length}`);
        
        setDataUrl(dataUrl);
        setIsLoading(false);
      } catch (error) {
        console.error('Error converting image to data URL:', error);
        setError(true);
        setIsLoading(false);
      }
    };
    
    img.onerror = (e) => {
      console.error(`DataUrlImage: Failed to load image from ${src}`, e);
      setError(true);
      setIsLoading(false);
    };
    
    // Add parameters to help with loading
    let imgSrc = src;
    if (src.includes('supabase.co')) {
      imgSrc = src.includes('?') 
        ? `${src}&download=true&t=${Date.now()}` 
        : `${src}?download=true&t=${Date.now()}`;
    }
    
    console.log(`DataUrlImage: Loading image from ${imgSrc}`);
    img.src = imgSrc;
    
    // Cleanup
    return () => {
      img.onload = null;
      img.onerror = null;
    };
  }, [src]);

  if (isLoading) {
    return (
      <div className={`${className || ''} flex items-center justify-center bg-gray-100`}>
        <span className="text-xs text-gray-500">Loading...</span>
      </div>
    );
  }

  if (error || !dataUrl) {
    return (
      <img
        src={fallbackSrc}
        alt={alt}
        className={className}
        width={width}
        height={height}
      />
    );
  }

  return (
    <img
      src={dataUrl}
      alt={alt}
      className={className}
      width={width}
      height={height}
    />
  );
};

export default DataUrlImage;
