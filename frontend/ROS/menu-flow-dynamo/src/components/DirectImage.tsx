import React, { useState, useEffect } from 'react';
import { transformImageUrl } from '@/utils/imageUtils';

interface DirectImageProps {
  src: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
  fallbackSrc?: string;
}

/**
 * A simple image component that handles loading errors and provides fallbacks
 * This is a simpler alternative to ImageProxy when direct loading is preferred
 */
const DirectImage: React.FC<DirectImageProps> = ({
  src,
  alt,
  className,
  width,
  height,
  fallbackSrc = 'https://placehold.co/200x200?text=Menu'
}) => {
  const [error, setError] = useState(false);
  const [loaded, setLoaded] = useState(false);
  const [imageSrc, setImageSrc] = useState<string>('');

  // Transform the image URL when the component mounts or src changes
  useEffect(() => {
    if (!src) {
      setError(true);
      return;
    }

    // Transform the URL to handle Supabase storage URLs
    const transformedUrl = transformImageUrl(src);
    console.log(`DirectImage: Original URL: ${src}`);
    console.log(`DirectImage: Transformed URL: ${transformedUrl}`);
    setImageSrc(transformedUrl);

    // Reset states when src changes
    setError(false);
    setLoaded(false);
  }, [src]);

  // Handle image load success
  const handleLoad = () => {
    console.log(`DirectImage: Successfully loaded image from ${imageSrc}`);
    setLoaded(true);
  };

  // Handle image load error
  const handleError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    console.error(`DirectImage: Failed to load image from ${imageSrc}`, e);
    setError(true);

    // Try with a direct URL as a last resort
    if (imageSrc !== src && !error) {
      console.log(`DirectImage: Trying direct URL as fallback: ${src}`);
      setImageSrc(src);
    }
  };

  // If there was an error loading the image, show the fallback
  if (error) {
    return (
      <img
        src={fallbackSrc}
        alt={alt}
        className={className}
        width={width}
        height={height}
      />
    );
  }

  return (
    <>
      {!loaded && (
        <div className={`${className || ''} flex items-center justify-center bg-gray-100`}>
          <span className="text-xs text-gray-500">Loading...</span>
        </div>
      )}
      <img
        src={imageSrc}
        alt={alt}
        className={`${className || ''} ${loaded ? '' : 'hidden'}`}
        width={width}
        height={height}
        onLoad={handleLoad}
        onError={handleError}
        crossOrigin="anonymous"
      />
    </>
  );
};

export default DirectImage;
