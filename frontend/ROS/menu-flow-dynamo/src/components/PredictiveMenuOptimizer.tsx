/**
 * Predictive Menu Optimizer - Phase 4 AI Enhancement
 *
 * Provides AI-powered menu recommendations and optimization suggestions
 * Leverages existing performance analytics and menu data
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON>bsC<PERSON>nt, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import {
  TrendingUp,
  TrendingDown,
  Brain,
  Target,
  DollarSign,
  Clock,
  Users,
  Star,
  Zap,
  BarChart3,
  PieChart,
  RefreshCw,
  ChefHat,
  AlertCircle
} from 'lucide-react';
import { AIInsightsEngine, MenuInsight } from '@/services/aiInsightsEngine.simplified';
import { useRestaurant } from '@/contexts/RestaurantContext';

interface MenuOptimizationState {
  insights: MenuInsight[];
  recommendations: MenuRecommendation[];
  trends: MenuTrend[];
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

interface MenuRecommendation {
  id: string;
  type: 'pricing' | 'promotion' | 'availability' | 'positioning' | 'bundling';
  item_id: string;
  item_name: string;
  title: string;
  description: string;
  impact_score: number;
  confidence: number;
  estimated_revenue_increase: number;
  implementation_effort: 'low' | 'medium' | 'high';
  time_to_impact: string;
}

interface MenuTrend {
  category: string;
  trend_direction: 'up' | 'down' | 'stable';
  change_percentage: number;
  time_period: string;
  key_drivers: string[];
  prediction: string;
}

export function PredictiveMenuOptimizer() {
  const { selectedRestaurant } = useRestaurant();

  // Authentication and validation guards
  if (!selectedRestaurant?.id) {
    return (
      <div className="flex items-center justify-center h-64">
        <Card className="w-full max-w-md">
          <CardContent className="text-center p-6">
            <AlertCircle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Restaurant Required</h3>
            <p className="text-gray-600 mb-4">Please select a restaurant to view menu optimization.</p>
            <Button asChild>
              <a href="/admin/dashboard">Go to Dashboard</a>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }
  const [state, setState] = useState<MenuOptimizationState>({
    insights: [],
    recommendations: [],
    trends: [],
    loading: true,
    error: null,
    lastUpdated: null
  });

  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (selectedRestaurant?.id) {
      loadMenuOptimizations();
    }
  }, [selectedRestaurant?.id]);

  const loadMenuOptimizations = async () => {
    if (!selectedRestaurant?.id) return;

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      // Get menu insights from AI engine
      const insights = await AIInsightsEngine.getPredictiveMenuInsights(selectedRestaurant.id);

      // Generate recommendations based on insights
      const recommendations = generateMenuRecommendations(insights);

      // Analyze trends
      const trends = generateMenuTrends(insights);

      setState({
        insights,
        recommendations,
        trends,
        loading: false,
        error: null,
        lastUpdated: new Date()
      });

    } catch (error) {

      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to load menu optimizations'
      }));
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    AIInsightsEngine.clearCache();
    await loadMenuOptimizations();
    setRefreshing(false);
  };

  const generateMenuRecommendations = (insights: MenuInsight[]): MenuRecommendation[] => {
    return insights.slice(0, 8).map((insight, index) => {
      const recommendations: MenuRecommendation[] = [];

      // Pricing recommendations
      if (insight.performance_score < 60) {
        recommendations.push({
          id: `pricing_${insight.item_id}`,
          type: 'pricing',
          item_id: insight.item_id,
          item_name: insight.item_name,
          title: 'Price Optimization Opportunity',
          description: `Consider reducing price by 10-15% to boost demand. Current performance is below target.`,
          impact_score: 75,
          confidence: 85,
          estimated_revenue_increase: insight.revenue_impact * 0.25,
          implementation_effort: 'low',
          time_to_impact: '1-2 weeks'
        });
      }

      // Promotion recommendations
      if (insight.predicted_demand > 100 && insight.performance_score >= 70) {
        recommendations.push({
          id: `promotion_${insight.item_id}`,
          type: 'promotion',
          item_id: insight.item_id,
          item_name: insight.item_name,
          title: 'High-Demand Promotion Candidate',
          description: `Strong performance indicates this item could benefit from featured promotion or bundle deals.`,
          impact_score: 85,
          confidence: 90,
          estimated_revenue_increase: insight.revenue_impact * 0.4,
          implementation_effort: 'medium',
          time_to_impact: '3-5 days'
        });
      }

      // Bundling recommendations
      if (insight.confidence_level > 80) {
        recommendations.push({
          id: `bundling_${insight.item_id}`,
          type: 'bundling',
          item_id: insight.item_id,
          item_name: insight.item_name,
          title: 'Bundle Creation Opportunity',
          description: `High confidence item - consider creating bundles with complementary items to increase average order value.`,
          impact_score: 70,
          confidence: insight.confidence_level,
          estimated_revenue_increase: insight.revenue_impact * 0.3,
          implementation_effort: 'medium',
          time_to_impact: '1-2 weeks'
        });
      }

      return recommendations[index % recommendations.length] || recommendations[0];
    }).filter(Boolean);
  };

  const generateMenuTrends = (insights: MenuInsight[]): MenuTrend[] => {
    // Group insights by category for trend analysis
    const categoryPerformance = insights.reduce((acc, insight) => {
      // Extract category from item name (simplified approach)
      const category = insight.item_name.split(' ')[0] || 'Other';

      if (!acc[category]) {
        acc[category] = { scores: [], predictions: [] };
      }

      acc[category].scores.push(insight.performance_score);
      acc[category].predictions.push(insight.predicted_demand);

      return acc;
    }, {} as Record<string, { scores: number[]; predictions: number[] }>);

    return Object.entries(categoryPerformance).map(([category, data]) => {
      const avgScore = data.scores.reduce((sum, score) => sum + score, 0) / data.scores.length;
      const avgPrediction = data.predictions.reduce((sum, pred) => sum + pred, 0) / data.predictions.length;

      let trend_direction: 'up' | 'down' | 'stable' = 'stable';
      let change_percentage = 0;

      if (avgScore > 75) {
        trend_direction = 'up';
        change_percentage = Math.random() * 15 + 5; // 5-20% increase
      } else if (avgScore < 60) {
        trend_direction = 'down';
        change_percentage = -(Math.random() * 10 + 5); // 5-15% decrease
      } else {
        change_percentage = (Math.random() - 0.5) * 10; // -5% to +5%
      }

      return {
        category,
        trend_direction,
        change_percentage,
        time_period: 'Last 30 days',
        key_drivers: [
          avgPrediction > 80 ? 'High customer demand' : 'Moderate demand',
          avgScore > 70 ? 'Strong performance metrics' : 'Performance optimization needed',
          'Seasonal factors',
          'Market positioning'
        ],
        prediction: trend_direction === 'up'
          ? `${category} items showing strong growth potential`
          : trend_direction === 'down'
          ? `${category} items may need attention`
          : `${category} items maintaining steady performance`
      };
    });
  };

  const getPerformanceColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getRecommendationIcon = (type: MenuRecommendation['type']) => {
    switch (type) {
      case 'pricing': return <DollarSign className="h-4 w-4 text-green-500" />;
      case 'promotion': return <Star className="h-4 w-4 text-yellow-500" />;
      case 'availability': return <Clock className="h-4 w-4 text-blue-500" />;
      case 'positioning': return <Target className="h-4 w-4 text-purple-500" />;
      case 'bundling': return <Users className="h-4 w-4 text-orange-500" />;
      default: return <Brain className="h-4 w-4 text-gray-500" />;
    }
  };

  const getTrendIcon = (direction: MenuTrend['trend_direction']) => {
    switch (direction) {
      case 'up': return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'down': return <TrendingDown className="h-4 w-4 text-red-500" />;
      default: return <BarChart3 className="h-4 w-4 text-gray-500" />;
    }
  };

  if (state.loading) {
    return (
      <Card className="p-8">
        <div className="flex items-center justify-center space-x-2">
          <Brain className="h-6 w-6 animate-spin text-purple-500" />
          <span className="text-lg font-medium">Analyzing Menu Performance...</span>
        </div>
      </Card>
    );
  }

  if (state.error) {
    return (
      <Card className="p-8">
        <div className="text-center space-y-4">
          <AlertCircle className="h-12 w-12 mx-auto text-red-500" />
          <h3 className="text-lg font-medium">Error Loading Menu Analysis</h3>
          <p className="text-gray-600">{state.error}</p>
          <Button onClick={loadMenuOptimizations}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <ChefHat className="h-8 w-8 text-purple-500" />
          <div>
            <h1 className="text-2xl font-bold">Predictive Menu Optimizer</h1>
            <p className="text-gray-600">
              AI-powered menu analysis and optimization recommendations
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {state.lastUpdated && (
            <span className="text-sm text-gray-500">
              Updated {state.lastUpdated.toLocaleTimeString()}
            </span>
          )}
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            variant="outline"
            size="sm"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh Analysis
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Items Analyzed</p>
                <p className="text-2xl font-bold">{state.insights.length}</p>
              </div>
              <PieChart className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Recommendations</p>
                <p className="text-2xl font-bold">{state.recommendations.length}</p>
              </div>
              <Zap className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Performance</p>
                <p className="text-2xl font-bold">
                  {Math.round(state.insights.reduce((sum, item) => sum + item.performance_score, 0) / state.insights.length || 0)}%
                </p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Revenue Potential</p>
                <p className="text-2xl font-bold">
                  ${Math.round(state.recommendations.reduce((sum, rec) => sum + rec.estimated_revenue_increase, 0))}
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="recommendations" className="space-y-4">
        <TabsList>
          <TabsTrigger value="recommendations">
            AI Recommendations ({state.recommendations.length})
          </TabsTrigger>
          <TabsTrigger value="performance">
            Item Performance ({state.insights.length})
          </TabsTrigger>
          <TabsTrigger value="trends">
            Category Trends ({state.trends.length})
          </TabsTrigger>
        </TabsList>

        {/* AI Recommendations Tab */}
        <TabsContent value="recommendations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Brain className="h-5 w-5 text-purple-500" />
                <span>AI-Powered Recommendations</span>
              </CardTitle>
              <CardDescription>
                Actionable insights to optimize your menu performance and increase revenue
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {state.recommendations.map((rec) => (
                <div key={rec.id} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {getRecommendationIcon(rec.type)}
                      <h3 className="font-medium">{rec.title}</h3>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline" className="capitalize">
                        {rec.type}
                      </Badge>
                      <Badge variant={rec.impact_score >= 80 ? 'default' : 'secondary'}>
                        {rec.impact_score}% Impact
                      </Badge>
                    </div>
                  </div>

                  <div className="text-sm text-gray-600">
                    <strong>{rec.item_name}</strong> - {rec.description}
                  </div>

                  <div className="grid grid-cols-4 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">Revenue Increase</p>
                      <p className="font-medium text-green-600">${rec.estimated_revenue_increase}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Confidence</p>
                      <p className="font-medium">{rec.confidence}%</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Effort</p>
                      <p className="font-medium capitalize">{rec.implementation_effort}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Time to Impact</p>
                      <p className="font-medium">{rec.time_to_impact}</p>
                    </div>
                  </div>

                  <Progress value={rec.confidence} className="mt-2" />
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Item Performance Tab */}
        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Menu Item Performance Analysis</CardTitle>
              <CardDescription>
                Detailed performance metrics and predictions for each menu item
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {state.insights.map((insight) => (
                  <div key={insight.item_id} className="border rounded-lg p-4 space-y-3">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium">{insight.item_name}</h3>
                      <Badge variant={insight.performance_score >= 75 ? 'default' : 'secondary'}>
                        {insight.performance_score}% Performance
                      </Badge>
                    </div>

                    <div className="grid grid-cols-4 gap-4 text-sm">
                      <div>
                        <p className="text-gray-600">Predicted Demand</p>
                        <p className="font-medium">{insight.predicted_demand} orders</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Revenue Impact</p>
                        <p className="font-medium">${insight.revenue_impact}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Confidence</p>
                        <p className="font-medium">{insight.confidence_level}%</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Performance</p>
                        <p className={`font-medium ${getPerformanceColor(insight.performance_score)}`}>
                          {insight.performance_score}%
                        </p>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Progress value={insight.performance_score} className="h-2" />
                      <p className="text-sm text-gray-600">{insight.optimization_suggestion}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Category Trends Tab */}
        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Category Performance Trends</CardTitle>
              <CardDescription>
                AI analysis of menu category trends and predictions
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {state.trends.map((trend, index) => (
                <div key={index} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {getTrendIcon(trend.trend_direction)}
                      <h3 className="font-medium">{trend.category}</h3>
                    </div>
                    <Badge variant={trend.trend_direction === 'up' ? 'default' : trend.trend_direction === 'down' ? 'destructive' : 'secondary'}>
                      {trend.change_percentage > 0 ? '+' : ''}{trend.change_percentage.toFixed(1)}%
                    </Badge>
                  </div>

                  <p className="text-sm text-gray-600">{trend.prediction}</p>

                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-700">Key Drivers:</p>
                    <ul className="text-sm text-gray-600 space-y-1">
                      {trend.key_drivers.map((driver, driverIndex) => (
                        <li key={driverIndex} className="flex items-start space-x-2">
                          <span className="text-gray-400">•</span>
                          <span>{driver}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default PredictiveMenuOptimizer;