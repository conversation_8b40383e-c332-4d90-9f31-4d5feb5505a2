/**
 * Order Feedback Form Component
 * Collects customer ratings and comments after order delivery
 * Integrates with sentiment analysis for restaurant insights
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { Rating } from './ui/rating';
import { submitFeedback, markFeedbackDeclined } from '@/services/feedbackService';
import { FeedbackData } from '@/services/feedbackService';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useLanguage } from '@/contexts/LanguageContext';

interface OrderFeedbackFormProps {
  orderId: string;
  restaurantId: string;
  tableId?: string;
  onCompleted: () => void;
  onDismiss: () => void;
}

export function OrderFeedbackForm({
  orderId,
  restaurantId,
  tableId,
  onCompleted,
  onDismiss
}: OrderFeedbackFormProps) {
  const { t } = useLanguage();
  const { toast } = useToast();
  
  const [feedback, setFeedback] = useState<Partial<FeedbackData>>({
    order_id: orderId,
    restaurant_id: restaurantId,
    table_id: tableId,
    food_rating: 0,
    service_rating: 0,
    app_rating: 0,
    comments: ''
  });
  
  const [email, setEmail] = useState('');
  const [shareEmail, setShareEmail] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  
  // Handle rating change
  const handleRatingChange = (type: 'food' | 'service' | 'app', value: number) => {
    setFeedback(prev => ({
      ...prev,
      [type === 'food' ? 'food_rating' : type === 'service' ? 'service_rating' : 'app_rating']: value
    }));
  };
  
  // Handle comments change
  const handleCommentsChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFeedback(prev => ({
      ...prev,
      comments: e.target.value
    }));
  };
  
  // Handle email change
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
  };
  
  // Handle share email toggle
  const handleShareEmailToggle = (e: React.ChangeEvent<HTMLInputElement>) => {
    setShareEmail(e.target.checked);
  };
  
  // Submit feedback
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (feedback.food_rating === 0 && feedback.service_rating === 0 && feedback.app_rating === 0) {
      toast({
        title: t('pleaseRateAtLeastOne'),
        description: t('pleaseProvideRating'),
        variant: 'destructive'
      });
      return;
    }
    
    setSubmitting(true);
    
    try {
      const fullFeedback: FeedbackData = {
        order_id: orderId,
        restaurant_id: restaurantId,
        table_id: tableId,
        food_rating: feedback.food_rating || 0,
        service_rating: feedback.service_rating || 0,
        app_rating: feedback.app_rating || 0,
        comments: feedback.comments || '',
        // Only include email if user opted to share it
        ...(shareEmail && email ? { customer_email: email } : {})
      };
      
      const result = await submitFeedback(fullFeedback);
      
      if (result.success) {
        toast({
          title: t('thankYouForFeedback'),
          description: result.message
        });
        onCompleted();
      } else {
        toast({
          title: t('errorSubmittingFeedback'),
          description: result.message,
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error submitting feedback:', error);
      toast({
        title: t('errorSubmittingFeedback'),
        description: t('pleaseTryAgainLater'),
        variant: 'destructive'
      });
    } finally {
      setSubmitting(false);
    }
  };
  
  // Decline to give feedback
  const handleDecline = async () => {
    try {
      await markFeedbackDeclined(orderId, restaurantId);
    } catch (error) {
      console.error('Error marking feedback as declined:', error);
    }
    onDismiss();
  };
  
  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="text-center">{t('shareFeedback')}</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium mb-1 block">
                {t('foodQuality')}
              </label>
              <Rating 
                value={feedback.food_rating || 0} 
                onChange={(value) => handleRatingChange('food', value)} 
              />
            </div>
            
            <div>
              <label className="text-sm font-medium mb-1 block">
                {t('serviceQuality')}
              </label>
              <Rating 
                value={feedback.service_rating || 0} 
                onChange={(value) => handleRatingChange('service', value)}
              />
            </div>
            
            <div>
              <label className="text-sm font-medium mb-1 block">
                {t('appExperience')}
              </label>
              <Rating 
                value={feedback.app_rating || 0} 
                onChange={(value) => handleRatingChange('app', value)}
              />
            </div>
          </div>
          
          <div>
            <label htmlFor="comments" className="text-sm font-medium mb-1 block">
              {t('additionalComments')}
            </label>
            <Textarea
              id="comments"
              placeholder={t('tellUsMoreAboutExperience')}
              value={feedback.comments}
              onChange={handleCommentsChange}
              className="resize-none"
              rows={4}
            />
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="shareEmail"
                checked={shareEmail}
                onChange={handleShareEmailToggle}
                className="h-4 w-4 rounded border-gray-300 text-restaurant-primary focus:ring-restaurant-primary"
              />
              <label htmlFor="shareEmail" className="ml-2 block text-sm text-gray-700">
                {t('shareEmailForUpdates')}
              </label>
            </div>
            
            {shareEmail && (
              <Input
                type="email"
                placeholder={t('emailAddress')}
                value={email}
                onChange={handleEmailChange}
              />
            )}
          </div>
          
          <CardFooter className="flex justify-between px-0 pt-2 pb-0">
            <Button 
              type="button" 
              variant="outline" 
              onClick={handleDecline}
              disabled={submitting}
            >
              {t('skipFeedback')}
            </Button>
            
            <Button 
              type="submit" 
              disabled={submitting}
              className="bg-restaurant-primary hover:bg-restaurant-primary/90"
            >
              {submitting ? t('submitting') : t('submitFeedback')}
            </Button>
          </CardFooter>
        </form>
      </CardContent>
    </Card>
  );
}
