import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Textarea } from '@/components/ui/textarea';
import { useLanguage } from '@/contexts/LanguageContext';
import { 
  ShoppingCart, 
  Plus, 
  Minus, 
  Trash2, 
  Users, 
  Lock, 
  Unlock,
  User,
  Clock,
  MessageSquare
} from 'lucide-react';
import { SharedCartItem } from '@/services/sharedCartService';
import { TableParticipant } from '@/services/sharedCartService';

interface CollaborativeOrderCartProps {
  // Cart data
  items: SharedCartItem[];
  totalAmount: number;
  isLocked: boolean;
  lockedByMe: boolean;
  
  // Participants
  participants: TableParticipant[];
  customerSessionId: string;
  getParticipantName: (sessionId: string) => string;
  
  // Actions
  onIncreaseQuantity: (itemId: string) => void;
  onDecreaseQuantity: (itemId: string) => void;
  onRemoveItem: (itemId: string) => void;
  onCheckout: () => void;
  onLockCart: () => void;
  onUnlockCart: () => void;
  
  // UI state
  isOpen: boolean;
  onClose: () => void;
  canModifyItem: (item: SharedCartItem) => boolean;
  
  // Comment functionality
  orderComment?: string;
  onCommentChange?: (comment: string) => void;
}

const CollaborativeOrderCart: React.FC<CollaborativeOrderCartProps> = ({
  items,
  totalAmount,
  isLocked,
  lockedByMe,
  participants,
  customerSessionId,
  getParticipantName,
  onIncreaseQuantity,
  onDecreaseQuantity,
  onRemoveItem,
  onCheckout,
  onLockCart,
  onUnlockCart,
  isOpen,
  onClose,
  canModifyItem,
  orderComment = '',
  onCommentChange
}) => {
  const { t } = useLanguage();
  
  if (!isOpen) return null;

  // Group items by customer
  const itemsByCustomer = items.reduce((acc, item) => {
    const sessionId = item.added_by_session;
    if (!acc[sessionId]) {
      acc[sessionId] = [];
    }
    acc[sessionId].push(item);
    return acc;
  }, {} as Record<string, SharedCartItem[]>);

  const participantCount = participants.length;
  const myItems = items.filter(item => item.added_by_session === customerSessionId);
  const myTotal = myItems.reduce((sum, item) => sum + item.price * item.quantity, 0);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-end backdrop-blur-sm">
      <div className="bg-white w-full max-w-md flex flex-col h-full shadow-2xl transform transition-all duration-300 ease-out animate-in slide-in-from-right">
        
        {/* Header with collaboration info */}
        <div className="px-4 py-3 border-b bg-gradient-to-r from-blue-50 to-purple-50">
          <div className="flex justify-between items-center mb-2">
            <div className="flex items-center">
              <ShoppingCart className="h-5 w-5 mr-2 text-blue-600" />
              <h2 className="text-xl font-semibold text-gray-900">Shared Cart</h2>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              aria-label="Close cart"
              className="hover:bg-white/50 focus:ring-2 focus:ring-blue-300 transition-colors"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M18 6 6 18" />
                <path d="m6 6 12 12" />
              </svg>
            </Button>
          </div>
          
          {/* Participants info */}
          <div className="flex items-center justify-between">
            <div className="flex items-center text-sm text-gray-600">
              <Users className="h-4 w-4 mr-1" />
              <span>{participantCount} {participantCount === 1 ? 'person' : 'people'} at table</span>
            </div>
            
            {/* Lock status */}
            {isLocked && (
              <Badge variant={lockedByMe ? "default" : "secondary"} className="flex items-center gap-1">
                <Lock className="h-3 w-3" />
                {lockedByMe ? 'Locked by you' : 'Locked for checkout'}
              </Badge>
            )}
          </div>
        </div>

        {items.length === 0 ? (
          <div className="flex-1 flex flex-col items-center justify-center p-8">
            <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-full p-6 mb-6">
              <ShoppingCart className="h-16 w-16 text-blue-400" />
            </div>
            <h3 className="text-xl font-semibold mb-3 text-gray-900">Shared cart is empty</h3>
            <p className="text-gray-500 text-center leading-relaxed max-w-sm">
              Add items to start your collaborative order
            </p>
            <div className="mt-6 text-center">
              <div className="inline-flex items-center text-sm text-gray-400">
                <span>💡 Everyone at your table can add items</span>
              </div>
            </div>
          </div>
        ) : (
          <>
            <ScrollArea className="flex-1 max-h-[calc(100vh-300px)]">
              <div className="p-4">
                {/* Show items grouped by customer */}
                {Object.entries(itemsByCustomer).map(([sessionId, customerItems]) => {
                  const customerName = getParticipantName(sessionId);
                  const isMe = sessionId === customerSessionId;
                  const customerTotal = customerItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
                  
                  return (
                    <div key={sessionId} className="mb-6 last:mb-0">
                      {/* Customer header */}
                      <div className="flex items-center justify-between mb-3 pb-2 border-b border-gray-100">
                        <div className="flex items-center gap-2">
                          <User className={`h-4 w-4 ${isMe ? 'text-blue-600' : 'text-gray-400'}`} />
                          <span className={`font-medium ${isMe ? 'text-blue-600' : 'text-gray-600'}`}>
                            {isMe ? 'Your items' : customerName}
                          </span>
                          {isMe && <Badge variant="outline" className="text-xs">You</Badge>}
                        </div>
                        <span className="text-sm font-medium text-gray-700">
                          €{customerTotal.toFixed(2)}
                        </span>
                      </div>
                      
                      {/* Customer's items */}
                      {customerItems.map((item) => {
                        const canModify = canModifyItem(item);
                        
                        return (
                          <div 
                            key={`${item.id}-${sessionId}`} 
                            className={`py-3 border-b last:border-b-0 transition-colors rounded-lg px-2 -mx-2 ${
                              canModify ? 'hover:bg-blue-50/50' : 'hover:bg-gray-50/30'
                            }`}
                          >
                            <div className="flex justify-between">
                              <div className="flex-1 min-w-0">
                                <div className="font-semibold text-gray-900 truncate">{item.name}</div>
                                <div className="text-sm text-gray-600 mt-1 flex items-center gap-2">
                                  <span className="font-medium">€{item.price.toFixed(2)}</span>
                                  {item.category && (
                                    <span className="text-xs bg-gray-100 px-2 py-1 rounded-full capitalize">
                                      {item.category}
                                    </span>
                                  )}
                                  {item.added_at && (
                                    <span className="text-xs text-gray-400 flex items-center gap-1">
                                      <Clock className="h-3 w-3" />
                                      {new Date(item.added_at).toLocaleTimeString([], { 
                                        hour: '2-digit', 
                                        minute: '2-digit' 
                                      })}
                                    </span>
                                  )}
                                </div>
                              </div>
                              
                              {/* Quantity controls */}
                              <div className={`flex items-center space-x-1 rounded-lg p-1 ${
                                canModify ? 'bg-blue-100' : 'bg-gray-100'
                              }`}>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className={`h-7 w-7 transition-all ${
                                    canModify 
                                      ? 'hover:bg-white hover:shadow-sm' 
                                      : 'opacity-50 cursor-not-allowed'
                                  }`}
                                  onClick={() => canModify && onDecreaseQuantity(item.id)}
                                  disabled={!canModify}
                                  aria-label={`Decrease quantity ${item.name}`}
                                >
                                  <Minus className="h-3 w-3" />
                                </Button>
                                <span className="w-8 text-center font-medium text-sm">
                                  {item.quantity}
                                </span>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className={`h-7 w-7 transition-all ${
                                    canModify 
                                      ? 'hover:bg-white hover:shadow-sm' 
                                      : 'opacity-50 cursor-not-allowed'
                                  }`}
                                  onClick={() => canModify && onIncreaseQuantity(item.id)}
                                  disabled={!canModify}
                                  aria-label={`Increase quantity ${item.name}`}
                                >
                                  <Plus className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                            
                            <div className="mt-3 flex justify-between items-center">
                              <div className="text-sm font-medium text-gray-700">
                                Subtotal: <span className="text-gray-900">€{(item.price * item.quantity).toFixed(2)}</span>
                              </div>
                              {canModify && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full transition-all"
                                  onClick={() => onRemoveItem(item.id)}
                                  aria-label={`Remove ${item.name}`}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  );
                })}
              </div>
            </ScrollArea>
            
            {/* Footer with totals and checkout */}
            <div className="border-t bg-gradient-to-r from-blue-50/50 to-purple-50/50 p-6">
              {/* Optional Comment Section - DEBUG: SHOULD BE VISIBLE */}
              <div className="mb-6 border-2 border-red-500 bg-yellow-50 p-4 rounded-lg">
                <div className="flex items-center mb-3">
                  <MessageSquare className="h-4 w-4 mr-2 text-gray-600" />
                  <label className="text-sm font-medium text-gray-700">
                    {t('specialInstructions')} <span className="text-gray-500 font-normal">({t('optional')})</span>
                  </label>
                </div>
                <Textarea
                  placeholder={t('specialInstructionsPlaceholder')}
                  value={orderComment}
                  onChange={(e) => onCommentChange?.(e.target.value)}
                  className="min-h-[80px] resize-none border-gray-200 focus:border-sky-500 focus:ring-sky-500 rounded-lg text-sm"
                  maxLength={500}
                  disabled={isLocked && !lockedByMe}
                />
                <div className="flex justify-between items-center mt-1">
                  <p className="text-xs text-gray-500">
                    {t('specialInstructionsHint')}
                  </p>
                  <span className="text-xs text-gray-400">
                    {orderComment.length}/500
                  </span>
                </div>
              </div>
              
              {/* My total vs table total */}
              <div className="mb-4 space-y-2">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-600">Your items:</span>
                  <span className="font-medium text-gray-900">€{myTotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-lg font-bold text-gray-900">Table total:</span>
                  <span className="text-2xl font-bold text-gray-900">€{totalAmount.toFixed(2)}</span>
                </div>
              </div>
              
              {/* Action buttons */}
              <div className="space-y-3">
                {!isLocked ? (
                  <Button
                    className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]"
                    onClick={onLockCart}
                    disabled={items.length === 0}
                  >
                    <div className="flex items-center justify-center gap-2">
                      <Lock className="h-4 w-4" />
                      <span>Lock & Checkout</span>
                      <span className="text-sm opacity-90">→</span>
                    </div>
                  </Button>
                ) : lockedByMe ? (
                  <div className="space-y-2">
                    <Button
                      className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]"
                      onClick={onCheckout}
                    >
                      <div className="flex items-center justify-center gap-2">
                        <span>Place Order</span>
                        <span className="text-sm opacity-90">→</span>
                      </div>
                    </Button>
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={onUnlockCart}
                    >
                      <div className="flex items-center justify-center gap-2">
                        <Unlock className="h-4 w-4" />
                        <span>Unlock Cart</span>
                      </div>
                    </Button>
                  </div>
                ) : (
                  <div className="text-center p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                    <Lock className="h-6 w-6 text-yellow-600 mx-auto mb-2" />
                    <p className="text-sm text-yellow-800 font-medium">
                      Cart is locked for checkout by another customer
                    </p>
                  </div>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default CollaborativeOrderCart;
