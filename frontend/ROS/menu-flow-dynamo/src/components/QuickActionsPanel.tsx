import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  PlusCircle, 
  RefreshCcw, 
  FileText, 
  QrCode,
  Download,
  BarChart2,
  MenuSquare,
  Table,
  Settings
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '@/contexts/LanguageContext';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

export function QuickActionsPanel() {
  const navigate = useNavigate();
  const { t } = useLanguage();
  
  const actions = [
    { 
      icon: <PlusCircle className="h-5 w-5" />, 
      label: t('addMenuItem'),
      action: () => navigate('/admin/menu-management?action=add'),
      color: 'bg-green-500'
    },
    { 
      icon: <MenuSquare className="h-5 w-5" />, 
      label: t('menuManagement'),
      action: () => navigate('/admin/menus'),
      color: 'bg-blue-500'
    },
    { 
      icon: <Table className="h-5 w-5" />, 
      label: t('tableManagement'),
      action: () => navigate('/admin/tables'),
      color: 'bg-purple-500'
    },
    { 
      icon: <BarChart2 className="h-5 w-5" />, 
      label: t('viewAnalytics'),
      action: () => navigate('/admin/analytics'),
      color: 'bg-amber-500'
    },
    { 
      icon: <QrCode className="h-5 w-5" />, 
      label: t('qrCodes'),
      action: () => navigate('/admin/tables/qr-management'),
      color: 'bg-indigo-500'
    },
    { 
      icon: <RefreshCcw className="h-5 w-5" />, 
      label: t('syncPOS'),
      action: () => navigate('/admin/settings?tab=integrations'),
      color: 'bg-rose-500'
    },
    { 
      icon: <FileText className="h-5 w-5" />, 
      label: t('reports'),
      action: () => navigate('/admin/analytics?tab=reports'),
      color: 'bg-emerald-500'
    },
    { 
      icon: <Download className="h-5 w-5" />, 
      label: t('export'),
      action: () => navigate('/admin/analytics?tab=export'),
      color: 'bg-cyan-500'
    },
    { 
      icon: <Settings className="h-5 w-5" />, 
      label: t('settings'),
      action: () => navigate('/admin/settings'),
      color: 'bg-slate-500'
    },
  ];

  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-md">{t('quickActions')}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-wrap gap-2">
          {actions.map((action, index) => (
            <TooltipProvider key={index}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={action.action}
                    className="flex flex-col items-center p-2 h-auto gap-1 hover:bg-gray-100 dark:hover:bg-gray-800"
                  >
                    <div className={`${action.color} text-white p-2 rounded-md`}>
                      {action.icon}
                    </div>
                    <span className="text-xs font-normal">{action.label}</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{action.label}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
