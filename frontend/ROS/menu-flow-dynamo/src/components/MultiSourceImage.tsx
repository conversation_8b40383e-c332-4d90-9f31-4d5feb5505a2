import React, { useState, useEffect } from 'react';
import { transformImageUrl } from '@/utils/imageUtils';
import { createSignedUrl, isSupabaseStorageUrl } from '@/utils/supabaseUtils';

interface MultiSourceImageProps {
  src: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
  fallbackSrc?: string;
}

/**
 * A component that tries multiple approaches to load an image
 * It will attempt different URL transformations and loading strategies
 */
const MultiSourceImage: React.FC<MultiSourceImageProps> = ({
  src,
  alt,
  className,
  width,
  height,
  fallbackSrc = 'https://placehold.co/200x200?text=Menu'
}) => {
  const [currentSrc, setCurrentSrc] = useState<string>('');
  const [error, setError] = useState(false);
  const [loaded, setLoaded] = useState(false);
  const [attemptCount, setAttemptCount] = useState(0);

  // Generate different versions of the URL to try
  useEffect(() => {
    if (!src) {
      setError(true);
      return;
    }

    // Reset states when src changes
    setError(false);
    setLoaded(false);
    setAttemptCount(0);

    // Start with the transformed URL
    const transformedUrl = transformImageUrl(src);
    setCurrentSrc(transformedUrl);

    console.log(`MultiSourceImage: Starting with URL: ${transformedUrl}`);

    // If it's a Supabase storage URL, try to create a signed URL
    if (isSupabaseStorageUrl(src)) {
      createSignedUrl(src).then(signedUrl => {
        console.log(`MultiSourceImage: Created signed URL: ${signedUrl}`);
        // Only set the signed URL if we haven't already loaded the image
        if (!loaded) {
          setCurrentSrc(signedUrl);
        }
      }).catch(error => {
        console.error('Error creating signed URL:', error);
      });
    }
  }, [src, loaded]);

  // Try different approaches when an error occurs
  useEffect(() => {
    if (!error || loaded || !src || attemptCount >= 3) return;

    // Different strategies to try
    const strategies = [
      // Strategy 1: Original URL
      src,
      // Strategy 2: URL with download=true parameter
      src.includes('?') ? `${src}&download=true` : `${src}?download=true`,
      // Strategy 3: URL with cache busting
      `${src}${src.includes('?') ? '&' : '?'}t=${Date.now()}`
    ];

    // Get the next strategy
    const nextSrc = strategies[attemptCount];
    console.log(`MultiSourceImage: Attempt ${attemptCount + 1} with URL: ${nextSrc}`);
    setCurrentSrc(nextSrc);

    // Increment attempt count
    setAttemptCount(prev => prev + 1);
  }, [error, loaded, src, attemptCount]);

  // Handle image load success
  const handleLoad = () => {
    console.log(`MultiSourceImage: Successfully loaded image from ${currentSrc}`);
    setLoaded(true);
    setError(false);
  };

  // Handle image load error
  const handleError = () => {
    console.error(`MultiSourceImage: Failed to load image from ${currentSrc}`);
    setError(true);
  };

  // If all attempts failed, show the fallback
  if (error && attemptCount >= 3) {
    return (
      <img
        src={fallbackSrc}
        alt={alt}
        className={className}
        width={width}
        height={height}
      />
    );
  }

  return (
    <>
      {!loaded && (
        <div className={`${className || ''} flex items-center justify-center bg-gray-100`}>
          <span className="text-xs text-gray-500">
            {error ? `Trying again (${attemptCount}/3)...` : 'Loading...'}
          </span>
        </div>
      )}
      <img
        src={currentSrc}
        alt={alt}
        className={`${className || ''} ${loaded ? '' : 'hidden'}`}
        width={width}
        height={height}
        onLoad={handleLoad}
        onError={handleError}
        crossOrigin="anonymous"
      />
    </>
  );
};

export default MultiSourceImage;
