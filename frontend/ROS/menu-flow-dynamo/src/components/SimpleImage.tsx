import React, { useState, useEffect } from 'react';
import { ensureSupabaseImageUrl } from '@/utils/imageUtils';

interface SimpleImageProps {
  src: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
  fallbackSrc?: string;
}

/**
 * A simple image component that adds download=true parameter to Supabase URLs
 */
const SimpleImage: React.FC<SimpleImageProps> = ({
  src,
  alt,
  className,
  width,
  height,
  fallbackSrc = 'https://placehold.co/200x200?text=Menu'
}) => {
  const [imageSrc, setImageSrc] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(false);

  useEffect(() => {
    if (!src) {
      setError(true);
      setIsLoading(false);
      return;
    }

    // Use the utility function to ensure the URL has the correct parameters
    const modifiedSrc = ensureSupabaseImageUrl(src);

    // Add a cache-busting parameter
    const cacheBuster = `&t=${Date.now()}`;
    const finalSrc = modifiedSrc.includes('?') ? `${modifiedSrc}${cacheBuster}` : `${modifiedSrc}?t=${Date.now()}`;

    console.log(`SimpleImage: Original URL: ${src}`);
    console.log(`SimpleImage: Modified URL: ${finalSrc}`);

    setImageSrc(finalSrc);
  }, [src]);

  const handleLoad = () => {
    console.log(`SimpleImage: Successfully loaded image from ${imageSrc}`);
    setIsLoading(false);
  };

  const handleError = () => {
    console.error(`SimpleImage: Failed to load image from ${imageSrc}`);
    setError(true);
    setIsLoading(false);
  };

  return (
    <>
      {isLoading && !error && (
        <div className={`${className || ''} flex items-center justify-center bg-gray-100`}>
          <span className="text-xs text-gray-500">Loading...</span>
        </div>
      )}

      {error ? (
        <img
          src={fallbackSrc}
          alt={alt}
          className={className}
          width={width}
          height={height}
        />
      ) : (
        <img
          src={imageSrc}
          alt={alt}
          className={className}
          width={width}
          height={height}
          onLoad={handleLoad}
          onError={handleError}
          style={{ display: isLoading ? 'none' : 'block' }}
        />
      )}
    </>
  );
};

export default SimpleImage;
