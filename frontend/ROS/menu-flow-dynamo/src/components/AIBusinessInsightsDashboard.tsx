/**
 * AI Business Insights Dashboard - Phase 4 Implementation
 *
 * Leverages existing analytics data and monitoring infrastructure
 * to provide intelligent business insights and recommendations
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  TrendingUp,
  TrendingDown,
  Brain,
  Target,
  AlertTriangle,
  CheckCircle,
  DollarSign,
  Users,
  Activity,
  Zap,
  RefreshCw,
  BarChart3,
  PieChart,
  LineChart
} from 'lucide-react';
import {
  AIInsightsEngine,
  BusinessMetrics,
  MenuInsight,
  PredictiveAlert,
  OperationalInsight
} from '@/services/aiInsightsEngine.simplified';
import { useRestaurant } from '@/contexts/RestaurantContext';

interface DashboardState {
  metrics: BusinessMetrics | null;
  menuInsights: MenuInsight[];
  alerts: PredictiveAlert[];
  operationalInsights: OperationalInsight[];
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

export function AIBusinessInsightsDashboard() {
  const { selectedRestaurant } = useRestaurant();

  const [state, setState] = useState<DashboardState>({
    metrics: null,
    menuInsights: [],
    alerts: [],
    operationalInsights: [],
    loading: true,
    error: null,
    lastUpdated: null
  });

  const [refreshing, setRefreshing] = useState(false);

  // Load AI insights on component mount and restaurant change
  useEffect(() => {
    if (selectedRestaurant?.id) {
      loadAIInsights();
    }
  }, [selectedRestaurant?.id]);

  const loadAIInsights = useCallback(async () => {
    if (!selectedRestaurant?.id) return;

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      // Load all AI insights in parallel
      const [metrics, menuInsights, alerts, operationalInsights] = await Promise.all([
        AIInsightsEngine.getBusinessMetrics(selectedRestaurant.id),
        AIInsightsEngine.getPredictiveMenuInsights(selectedRestaurant.id),
        AIInsightsEngine.generatePredictiveAlerts(selectedRestaurant.id),
        AIInsightsEngine.getOperationalInsights(selectedRestaurant.id)
      ]);

      setState({
        metrics,
        menuInsights,
        alerts,
        operationalInsights,
        loading: false,
        error: null,
        lastUpdated: new Date()
      });

    } catch (error) {

      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to load AI insights'
      }));
    }
  }, [selectedRestaurant?.id]);

  const handleRefresh = async () => {
    setRefreshing(true);
    AIInsightsEngine.clearCache();
    await loadAIInsights();
    setRefreshing(false);
  };

  // Authentication and validation guards
  if (!selectedRestaurant?.id) {
    return (
      <div className="flex items-center justify-center h-64">
        <Card className="w-full max-w-md">
          <CardContent className="text-center p-6">
            <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Restaurant Required</h3>
            <p className="text-gray-600 mb-4">Please select a restaurant to view AI insights.</p>
            <Button asChild>
              <a href="/admin/dashboard">Go to Dashboard</a>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const getMetricIcon = (value: number) => {
    if (value >= 80) return <TrendingUp className="h-4 w-4 text-green-500" />;
    if (value >= 60) return <Activity className="h-4 w-4 text-yellow-500" />;
    return <TrendingDown className="h-4 w-4 text-red-500" />;
  };

  const getMetricColor = (value: number) => {
    if (value >= 80) return 'text-green-600';
    if (value >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getAlertIcon = (type: PredictiveAlert['type']) => {
    switch (type) {
      case 'opportunity': return <Target className="h-4 w-4 text-green-500" />;
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'trend': return <TrendingUp className="h-4 w-4 text-blue-500" />;
      case 'optimization': return <Zap className="h-4 w-4 text-purple-500" />;
      default: return <Brain className="h-4 w-4 text-gray-500" />;
    }
  };

  const getImpactColor = (level: PredictiveAlert['impact_level']) => {
    switch (level) {
      case 'critical': return 'destructive';
      case 'high': return 'destructive';
      case 'medium': return 'default';
      case 'low': return 'secondary';
      default: return 'secondary';
    }
  };

  if (state.loading) {
    return (
      <Card className="p-8">
        <div className="flex items-center justify-center space-x-2">
          <Brain className="h-6 w-6 animate-spin text-blue-500" />
          <span className="text-lg font-medium">Generating AI Insights...</span>
        </div>
      </Card>
    );
  }

  if (state.error) {
    return (
      <Card className="p-8">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Error Loading AI Insights</AlertTitle>
          <AlertDescription>{state.error}</AlertDescription>
        </Alert>
        <Button onClick={loadAIInsights} className="mt-4">
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Brain className="h-8 w-8 text-blue-500" />
          <div>
            <h1 className="text-2xl font-bold">AI Business Insights</h1>
            <p className="text-gray-600">
              Powered by real-time analytics and predictive intelligence
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {state.lastUpdated && (
            <span className="text-sm text-gray-500">
              Updated {state.lastUpdated.toLocaleTimeString()}
            </span>
          )}
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            variant="outline"
            size="sm"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Business Metrics Overview */}
      {state.metrics && (
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Revenue Trend</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <div className={`text-2xl font-bold ${getMetricColor(state.metrics.revenue_trend)}`}>
                  {Math.round(state.metrics.revenue_trend)}%
                </div>
                {getMetricIcon(state.metrics.revenue_trend)}
              </div>
              <Progress value={state.metrics.revenue_trend} className="mt-2" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Customer Growth</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <div className={`text-2xl font-bold ${getMetricColor(state.metrics.customer_growth)}`}>
                  {Math.round(state.metrics.customer_growth)}%
                </div>
                {getMetricIcon(state.metrics.customer_growth)}
              </div>
              <Progress value={state.metrics.customer_growth} className="mt-2" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Menu Performance</CardTitle>
              <PieChart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <div className={`text-2xl font-bold ${getMetricColor(state.metrics.menu_performance)}`}>
                  {Math.round(state.metrics.menu_performance)}%
                </div>
                {getMetricIcon(state.metrics.menu_performance)}
              </div>
              <Progress value={state.metrics.menu_performance} className="mt-2" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Operational Efficiency</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <div className={`text-2xl font-bold ${getMetricColor(state.metrics.operational_efficiency)}`}>
                  {Math.round(state.metrics.operational_efficiency)}%
                </div>
                {getMetricIcon(state.metrics.operational_efficiency)}
              </div>
              <Progress value={state.metrics.operational_efficiency} className="mt-2" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Market Position</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <div className={`text-2xl font-bold ${getMetricColor(state.metrics.market_position)}`}>
                  {Math.round(state.metrics.market_position)}%
                </div>
                {getMetricIcon(state.metrics.market_position)}
              </div>
              <Progress value={state.metrics.market_position} className="mt-2" />
            </CardContent>
          </Card>
        </div>
      )}

      {/* Detailed Insights Tabs */}
      <Tabs defaultValue="alerts" className="space-y-4">
        <TabsList>
          <TabsTrigger value="alerts" className="flex items-center space-x-2">
            <AlertTriangle className="h-4 w-4" />
            <span>Alerts ({state.alerts.length})</span>
          </TabsTrigger>
          <TabsTrigger value="menu" className="flex items-center space-x-2">
            <PieChart className="h-4 w-4" />
            <span>Menu Insights ({state.menuInsights.length})</span>
          </TabsTrigger>
          <TabsTrigger value="operations" className="flex items-center space-x-2">
            <Activity className="h-4 w-4" />
            <span>Operations ({state.operationalInsights.length})</span>
          </TabsTrigger>
        </TabsList>

        {/* Predictive Alerts */}
        <TabsContent value="alerts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Brain className="h-5 w-5 text-blue-500" />
                <span>Predictive Alerts</span>
              </CardTitle>
              <CardDescription>
                AI-generated alerts based on real-time monitoring and predictive analysis
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {state.alerts.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
                  <p>No alerts - your restaurant is operating optimally!</p>
                </div>
              ) : (
                state.alerts.map((alert) => (
                  <Alert key={alert.id} variant={getImpactColor(alert.impact_level) as "default" | "destructive"}>
                    <div className="flex items-start space-x-3">
                      {getAlertIcon(alert.type)}
                      <div className="flex-1">
                        <AlertTitle className="flex items-center justify-between">
                          <span>{alert.title}</span>
                          <Badge variant="outline">
                            {alert.confidence}% confidence
                          </Badge>
                        </AlertTitle>
                        <AlertDescription className="mt-2">
                          {alert.description}
                        </AlertDescription>
                        <div className="mt-3 p-3 bg-gray-50 rounded-md">
                          <p className="text-sm font-medium text-gray-700">Recommended Action:</p>
                          <p className="text-sm text-gray-600 mt-1">{alert.recommended_action}</p>
                        </div>
                      </div>
                    </div>
                  </Alert>
                ))
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Menu Insights */}
        <TabsContent value="menu" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <PieChart className="h-5 w-5 text-purple-500" />
                <span>Predictive Menu Insights</span>
              </CardTitle>
              <CardDescription>
                AI analysis of menu performance with demand predictions and optimization suggestions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {state.menuInsights.slice(0, 8).map((insight) => (
                  <div key={insight.item_id} className="border rounded-lg p-4 space-y-3">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium">{insight.item_name}</h3>
                      <Badge variant={insight.performance_score >= 75 ? 'default' : 'secondary'}>
                        {insight.performance_score}% Performance
                      </Badge>
                    </div>

                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <p className="text-gray-600">Predicted Demand</p>
                        <p className="font-medium">{insight.predicted_demand} orders</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Revenue Impact</p>
                        <p className="font-medium">${insight.revenue_impact}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Confidence</p>
                        <p className="font-medium">{insight.confidence_level}%</p>
                      </div>
                    </div>

                    <div className="p-3 bg-blue-50 rounded-md">
                      <p className="text-sm font-medium text-blue-800">AI Recommendation:</p>
                      <p className="text-sm text-blue-700 mt-1">{insight.optimization_suggestion}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Operational Insights */}
        <TabsContent value="operations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="h-5 w-5 text-green-500" />
                <span>Operational Intelligence</span>
              </CardTitle>
              <CardDescription>
                Real-time operational insights with predictive analysis and recommendations
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {state.operationalInsights.map((insight, index) => (
                <div key={index} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium capitalize">{insight.category.replace('_', ' ')}</h3>
                    <Badge variant="outline" className="flex items-center space-x-1">
                      {insight.trend_direction === 'up' ? (
                        <TrendingUp className="h-3 w-3 text-green-500" />
                      ) : insight.trend_direction === 'down' ? (
                        <TrendingDown className="h-3 w-3 text-red-500" />
                      ) : (
                        <Activity className="h-3 w-3 text-gray-500" />
                      )}
                      <span>{insight.trend_direction}</span>
                    </Badge>
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">Current Value</p>
                      <p className="font-medium">{insight.current_value.toFixed(2)}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Predicted Value</p>
                      <p className="font-medium">{insight.predicted_value.toFixed(2)}</p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <p className="text-sm text-gray-700">{insight.explanation}</p>

                    <div className="p-3 bg-green-50 rounded-md">
                      <p className="text-sm font-medium text-green-800 mb-2">Recommendations:</p>
                      <ul className="text-sm text-green-700 space-y-1">
                        {insight.recommendations.map((rec, recIndex) => (
                          <li key={recIndex} className="flex items-start space-x-2">
                            <span className="text-green-600">•</span>
                            <span>{rec}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default AIBusinessInsightsDashboard;