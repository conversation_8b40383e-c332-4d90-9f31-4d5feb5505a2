import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, <PERSON><PERSON>T<PERSON>le, DialogFooter } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { useLanguage } from '@/contexts/LanguageContext';
import { QRCodeSVG } from 'qrcode.react';
import { Download } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { createTable, updateTable } from '@/services/tableDbService';

interface TableFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  editingTable?: any;
}

interface TableFormData {
  table_number: string;
  capacity: number;
  location: string;
}

const TableForm: React.FC<TableFormProps> = ({ isOpen, onClose, onSuccess, editingTable }) => {
  const { t } = useLanguage();
  const { toast } = useToast();
  const [showQRCode, setShowQRCode] = useState(false);
  const [tableId, setTableId] = useState<string>('');
  const [tableNumber, setTableNumber] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { register, handleSubmit, reset, formState: { errors } } = useForm<TableFormData>({
    defaultValues: editingTable ? {
      table_number: editingTable.table_number,
      capacity: editingTable.capacity,
      location: editingTable.location || '',
    } : {
      table_number: '',
      capacity: 4,
      location: '',
    }
  });

  const { user } = useAuth();

  const onSubmit = async (data: TableFormData) => {
    setIsSubmitting(true);
    try {
      // Prepare table data for database
      const tableData = {
        table_number: data.table_number,
        capacity: data.capacity,
        location: data.location,
        is_occupied: editingTable ? editingTable.is_occupied : false,
      };

      if (editingTable) {
        // Update existing table in database
        const updatedTable = await updateTable(editingTable.id, tableData);

        toast({
          title: "Table updated",
          description: `Table ${data.table_number} has been updated successfully.`,
        });

        onSuccess();
        onClose();
      } else {
        // Create new table in database
        const qrCodeUrl = `${window.location.origin}/menu?table=`;
        const newTable = await createTable({
          ...tableData,
          qr_code_url: qrCodeUrl, // Will be updated with the actual ID after creation
        }, user);

        // Update the QR code URL with the actual table ID
        if (newTable && newTable.id) {
          const updatedQrCodeUrl = `${qrCodeUrl}${newTable.id}`;
          await updateTable(newTable.id, { qr_code_url: updatedQrCodeUrl });

          setTableId(newTable.id);
          setTableNumber(data.table_number);
          setShowQRCode(true);
        }

        toast({
          title: "Table created",
          description: `Table ${data.table_number} has been created successfully.`,
        });

        onSuccess();
      }
    } catch (err: any) {
      toast({
        variant: "destructive",
        title: "Error",
        description: err.message,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    reset();
    setShowQRCode(false);
    onClose();
  };

  const downloadQRCode = () => {
    try {
      // Get the SVG element
      const svgElement = document.getElementById('table-qrcode');
      if (!svgElement) {
        console.error('QR code SVG element not found');
        return;
      }

      // Create a canvas element
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        console.error('Could not get canvas context');
        return;
      }

      // Set canvas dimensions to match the QR code size
      canvas.width = 200;
      canvas.height = 200;

      // Create an image from the SVG
      const img = new Image();
      const svgData = new XMLSerializer().serializeToString(svgElement);
      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
      const url = URL.createObjectURL(svgBlob);

      img.onload = () => {
        // Draw the image on the canvas
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

        // Convert canvas to data URL
        const pngUrl = canvas.toDataURL('image/png');

        // Create download link
        const downloadLink = document.createElement('a');
        downloadLink.href = pngUrl;
        downloadLink.download = `table-${tableNumber}-qrcode.png`;
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);

        // Clean up
        URL.revokeObjectURL(url);
      };

      img.src = url;

      toast({
        title: "QR Code Download",
        description: "Your QR code is being downloaded.",
      });
    } catch (error) {
      console.error('Error downloading QR code:', error);
      toast({
        variant: "destructive",
        title: "Download Failed",
        description: "Could not download the QR code. Please try again.",
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {showQRCode
              ? `QR Code for Table ${tableNumber}`
              : editingTable
                ? `Edit Table ${editingTable.table_number}`
                : t('addTable')}
          </DialogTitle>
        </DialogHeader>

        {showQRCode ? (
          <div className="flex flex-col items-center space-y-4 py-4">
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <QRCodeSVG
                id="table-qrcode"
                value={`${window.location.origin}/menu?table=${tableId}`}
                size={200}
                level="H"
                includeMargin={true}
                bgColor="#FFFFFF"
                fgColor="#000000"
              />
            </div>
            <div className="text-center space-y-2">
              <p className="font-medium">Table {tableNumber}</p>
              <p className="text-sm text-muted-foreground">
                Scan this QR code to access the menu
              </p>
              <p className="text-xs text-muted-foreground break-all">
                URL: {window.location.origin}/menu?table={tableId}
              </p>
            </div>
            <div className="flex gap-3 mt-2">
              <Button onClick={downloadQRCode} className="mt-4">
                <Download className="h-4 w-4 mr-2" /> Download QR Code
              </Button>
              <Button onClick={handleClose} variant="outline" className="mt-4">
                Close
              </Button>
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="table_number">{t('tableNumber')}</Label>
              <Input
                id="table_number"
                {...register('table_number', { required: 'Table number is required' })}
              />
              {errors.table_number && (
                <p className="text-sm text-red-500">{errors.table_number.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="capacity">{t('capacity')}</Label>
              <Input
                id="capacity"
                type="number"
                {...register('capacity', {
                  required: 'Capacity is required',
                  min: { value: 1, message: 'Capacity must be at least 1' },
                  valueAsNumber: true
                })}
              />
              {errors.capacity && (
                <p className="text-sm text-red-500">{errors.capacity.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="location">{t('location')}</Label>
              <Input
                id="location"
                placeholder="e.g., Patio, Main Hall, etc."
                {...register('location')}
              />
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Saving...' : editingTable ? 'Update Table' : 'Add Table'}
              </Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default TableForm;
