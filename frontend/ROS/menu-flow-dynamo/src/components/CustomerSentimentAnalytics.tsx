import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { TrendingUp, TrendingDown, Heart, Frown, <PERSON>h, Smile, Star } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useRestaurant } from '@/contexts/RestaurantContext';
import { useLanguage } from '@/contexts/LanguageContext';

interface SentimentData {
  averageRating: number;
  totalFeedback: number;
  sentimentDistribution: {
    positive: number;
    neutral: number;
    negative: number;
  };
  recentFeedback: Array<{
    id: string;
    food_rating: number;
    service_rating: number;
    app_rating: number;
    comments: string;
    sentiment_score: number;
    created_at: string;
  }>;
  trends: {
    foodRatingTrend: number;
    serviceRatingTrend: number;
    appRatingTrend: number;
  };
}

interface CustomerSentimentAnalyticsProps {
  restaurantId?: string;
  timeRange?: 'day' | 'week' | 'month';
}

const CustomerSentimentAnalytics: React.FC<CustomerSentimentAnalyticsProps> = ({
  restaurantId,
  timeRange = 'week'
}) => {
  const { user } = useAuth();
  const { restaurantInfo } = useRestaurant();
  const { t } = useLanguage();
  const [sentimentData, setSentimentData] = useState<SentimentData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchSentimentData = async () => {
      setIsLoading(true);
      try {
        // Get the restaurant ID from props, RestaurantContext, or user metadata (in that order)
        const targetRestaurantId = restaurantId || restaurantInfo?.id || user?.user_metadata?.restaurant_id;

        if (!targetRestaurantId) {

          setSentimentData({
            averageRating: 0,
            totalFeedback: 0,
            sentimentDistribution: { positive: 0, neutral: 0, negative: 0 },
            recentFeedback: [],
            trends: { foodRatingTrend: 0, serviceRatingTrend: 0, appRatingTrend: 0 }
          });
          setIsLoading(false);
          return;
        }

        // Calculate date range
        const now = new Date();
        const startDate = new Date();
        switch (timeRange) {
          case 'day':
            startDate.setDate(now.getDate() - 1);
            break;
          case 'week':
            startDate.setDate(now.getDate() - 7);
            break;
          case 'month':
            startDate.setDate(now.getDate() - 30);
            break;
        }

        console.log('🔔 Date range:', { from: startDate.toISOString(), to: now.toISOString() });

        // First, test basic connectivity to customer_feedback table

        const { data: testData, error: testError } = await supabase
          .from('customer_feedback')
          .select('id, restaurant_id, created_at')
          .limit(5);

        // Fetch customer feedback data
        const { data: feedbackData, error } = await supabase
          .from('customer_feedback')
          .select('*')
          .eq('restaurant_id', targetRestaurantId)
          .gte('created_at', startDate.toISOString())
          .order('created_at', { ascending: false });

        console.log('🔔 Customer feedback query result:', {
          error: error?.message,
          dataCount: feedbackData?.length || 0,
          sampleData: feedbackData?.slice(0, 2) // Show first 2 records for debugging
        });

        if (error) {

          setSentimentData({
            averageRating: 0,
            totalFeedback: 0,
            sentimentDistribution: { positive: 0, neutral: 0, negative: 0 },
            recentFeedback: [],
            trends: { foodRatingTrend: 0, serviceRatingTrend: 0, appRatingTrend: 0 }
          });
          setIsLoading(false);
          return;
        }

        if (!feedbackData || feedbackData.length === 0) {
          setSentimentData({
            averageRating: 0,
            totalFeedback: 0,
            sentimentDistribution: { positive: 0, neutral: 0, negative: 0 },
            recentFeedback: [],
            trends: { foodRatingTrend: 0, serviceRatingTrend: 0, appRatingTrend: 0 }
          });
          setIsLoading(false);
          return;
        }

        // Calculate analytics
        const totalFeedback = feedbackData.length;
        const averageRating = feedbackData.reduce((sum, item) =>
          sum + (item.food_rating + item.service_rating + item.app_rating) / 3, 0
        ) / totalFeedback;

        // Calculate sentiment distribution
        const sentimentDistribution = feedbackData.reduce((acc, item) => {
          const avgRating = (item.food_rating + item.service_rating + item.app_rating) / 3;
          if (avgRating >= 4) acc.positive++;
          else if (avgRating >= 3) acc.neutral++;
          else acc.negative++;
          return acc;
        }, { positive: 0, neutral: 0, negative: 0 });

        // Calculate trends (compare with previous period)
        const midPoint = Math.floor(feedbackData.length / 2);
        const recentData = feedbackData.slice(0, midPoint);
        const olderData = feedbackData.slice(midPoint);

        const calculateTrend = (recent: any[], older: any[], ratingField: string) => {
          if (older.length === 0) return 0;
          const recentAvg = recent.reduce((sum, item) => sum + item[ratingField], 0) / recent.length;
          const olderAvg = older.reduce((sum, item) => sum + item[ratingField], 0) / older.length;
          return ((recentAvg - olderAvg) / olderAvg) * 100;
        };

        const trends = {
          foodRatingTrend: calculateTrend(recentData, olderData, 'food_rating'),
          serviceRatingTrend: calculateTrend(recentData, olderData, 'service_rating'),
          appRatingTrend: calculateTrend(recentData, olderData, 'app_rating')
        };

        setSentimentData({
          averageRating,
          totalFeedback,
          sentimentDistribution,
          recentFeedback: feedbackData.slice(0, 5), // Show 5 most recent
          trends
        });

      } catch (error) {
        console.error('Error processing sentiment data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSentimentData();
  }, [restaurantId, user, timeRange]);

  const getSentimentIcon = (rating: number) => {
    if (rating >= 4) return <Smile className="h-4 w-4 text-green-500" />;
    if (rating >= 3) return <Meh className="h-4 w-4 text-yellow-500" />;
    return <Frown className="h-4 w-4 text-red-500" />;
  };

  const getTrendIcon = (trend: number) => {
    if (trend > 0) return <TrendingUp className="h-4 w-4 text-green-500" />;
    if (trend < 0) return <TrendingDown className="h-4 w-4 text-red-500" />;
    return <Meh className="h-4 w-4 text-gray-500" />;
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-48 w-full" />
      </div>
    );
  }

  if (!sentimentData) {
    return (
      <Card>
        <CardContent className="pt-6">
          <p className="text-center text-muted-foreground">{t('noFeedbackData')}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">{t('overallRating')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Star className="h-5 w-5 text-yellow-500 mr-2" />
              <div>
                <p className="text-2xl font-bold">{sentimentData.averageRating.toFixed(1)}</p>
                <p className="text-xs text-muted-foreground">{t('outOfFive')}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">{t('totalFeedback')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Heart className="h-5 w-5 text-red-500 mr-2" />
              <div>
                <p className="text-2xl font-bold">{sentimentData.totalFeedback}</p>
                <p className="text-xs text-muted-foreground">{t('responses')}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">{t('positive')} {t('customerSentiment')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Smile className="h-5 w-5 text-green-500 mr-2" />
              <div>
                <p className="text-2xl font-bold">
                  {Math.round((sentimentData.sentimentDistribution.positive / sentimentData.totalFeedback) * 100)}%
                </p>
                <p className="text-xs text-muted-foreground">
                  {sentimentData.sentimentDistribution.positive} {t('reviews')}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">{t('foodRating')} {t('trends')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              {getTrendIcon(sentimentData.trends.foodRatingTrend)}
              <div className="ml-2">
                <p className="text-2xl font-bold">
                  {sentimentData.trends.foodRatingTrend > 0 ? '+' : ''}
                  {sentimentData.trends.foodRatingTrend.toFixed(1)}%
                </p>
                <p className="text-xs text-muted-foreground">{t('vsPreviousPeriod')}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Feedback */}
      <Card>
        <CardHeader>
          <CardTitle>{t('recentFeedback')}</CardTitle>
        </CardHeader>
        <CardContent>
          {sentimentData.recentFeedback.length > 0 ? (
            <div className="space-y-4">
              {sentimentData.recentFeedback.map((feedback) => {
                const avgRating = (feedback.food_rating + feedback.service_rating + feedback.app_rating) / 3;
                return (
                  <div key={feedback.id} className="border-b pb-4 last:border-b-0">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        {getSentimentIcon(avgRating)}
                        <span className="font-medium">{avgRating.toFixed(1)}/5.0</span>
                        <Badge variant="outline">
                          {t('foodRating')}: {feedback.food_rating} | {t('serviceRating')}: {feedback.service_rating} | {t('appRating')}: {feedback.app_rating}
                        </Badge>
                      </div>
                      <span className="text-xs text-muted-foreground">
                        {new Date(feedback.created_at).toLocaleDateString()}
                      </span>
                    </div>
                    {feedback.comments && (
                      <p className="text-sm text-muted-foreground italic">"{feedback.comments}"</p>
                    )}
                  </div>
                );
              })}
            </div>
          ) : (
            <p className="text-center text-muted-foreground">{t('noFeedbackData')}</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default CustomerSentimentAnalytics;
