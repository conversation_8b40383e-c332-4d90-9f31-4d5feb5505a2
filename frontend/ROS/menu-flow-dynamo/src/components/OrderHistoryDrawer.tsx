/**
 * Order History Drawer Component
 * Displays a slide-out drawer showing the customer's order history
 * Pulls from localStorage and allows tracking active orders
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Sheet,
  Sheet<PERSON>ontent,
  She<PERSON><PERSON>eader,
  She<PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger
} from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import {
  ClipboardList,
  Clock,
  ShoppingBag,
  Check,
  AlertTriangle,
  ChefHat,
  Bell
} from 'lucide-react';
import { Order } from '../types';
import { getOrderHistory, getOrderExpirationInfo } from '@/services/orderHistoryService';
import { getOrderStatusMessage } from '@/services/orderStatusService';
import { Badge } from '@/components/ui/badge';
import { formatCurrency } from '@/utils/currencyUtils';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';
import { TableInfoBadge } from './TableInfoBadge';
import { getTableSession } from '@/services/tableSessionService';
import { setupOrderStatusListener, fetchActiveOrdersForTable, fetchAllOrdersForTable, fetchOrderStatus } from '@/services/orderStatusService';

/**
 * Merges local and server order lists, giving priority to server data
 * @param localOrders Orders from local storage (filtered by table)
 * @param serverOrders Orders from server
 * @returns Merged order list with no duplicates
 */
function mergeOrderLists(localOrders: Order[], serverOrders: Order[]): Order[] {
  // Create a map of server orders by ID for quick lookup
  const serverOrderMap = new Map(serverOrders.map(order => [order.id, order]));

  // Take all server orders and add local orders that don't exist on server
  const mergedOrders = [...serverOrders];

  // Only add local orders that aren't in the server list
  // This prevents duplicates while ensuring we have the most up-to-date data
  localOrders.forEach(localOrder => {
    if (!serverOrderMap.has(localOrder.id)) {
      mergedOrders.push(localOrder);
    }
  });

  // Sort by creation date, newest first
  return mergedOrders.sort((a, b) => {
    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
  });
}

export function OrderHistoryDrawer() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [open, setOpen] = useState(false);
  const [expirationInfo, setExpirationInfo] = useState<{ hours: number; minutes: number; hasOrders: boolean } | null>(null);
  const { t } = useLanguage();
  const navigate = useNavigate();

  useEffect(() => {
    // Get the current table session (only if valid within 90 minutes)
    const currentSession = getTableSession();

    const fetchOrders = async () => {
      try {
        // If we have a valid table session, only fetch orders for this table
        if (currentSession && currentSession.isValid) {

          // Use the modified getOrderHistory function that filters by table ID
          const localOrders = getOrderHistory(currentSession.tableId);

          // Fetch all orders from the server for this table (including completed)
          const tableOrders = await fetchAllOrdersForTable(currentSession.tableId);

          // Merge local and server orders, giving priority to server data
          // This ensures we only see orders for the current table
          const mergedOrders = mergeOrderLists(localOrders, tableOrders);
          setOrders(mergedOrders);
        } else {
          // Get orders from local storage for general history
          setOrders(getOrderHistory());
        }

        // Update expiration info
        setExpirationInfo(getOrderExpirationInfo());
      } catch (error) {
        console.error('Error fetching orders:', error);
      }
    };

    // Fetch orders immediately
    fetchOrders();

    // Set up real-time listener for order updates
    // We'll use a dummy orderId since we actually want to listen to all orders
    // and handle filtering in our fetchOrders function
    let unsubscribe = () => {};

    // If we have a current table session, listen for updates to orders for that table
    if (currentSession && currentSession.isValid) {
      unsubscribe = setupOrderStatusListener(currentSession.tableId, (updatedOrder) => {

        // When an order status changes, refresh the orders list
        fetchOrders();
      });
    }

    // Unsubscribe on unmount
    return () => {
      unsubscribe();
    };
  }, []);

  // Also update when drawer opens/closes
  useEffect(() => {
    if (open) {
      // Refresh orders when drawer opens
      const currentSession = getTableSession();
      if (currentSession && currentSession.isValid) {
        fetchAllOrdersForTable(currentSession.tableId).then(setOrders);
      } else {
        setOrders(getOrderHistory());
      }
    }
  }, [open]);

  // Count active orders for badge
  const activeOrdersCount = orders.filter(
    order => !['completed', 'cancelled'].includes(order.status.toLowerCase())
  ).length;

  // Get icon based on order status
  const getStatusIcon = (status: string) => {
    switch(status.toLowerCase()) {
      case 'new': return <Bell className="h-4 w-4" />;
      case 'preparing': return <ChefHat className="h-4 w-4" />;
      case 'ready': return <ShoppingBag className="h-4 w-4" />;
      case 'delivered': return <Clock className="h-4 w-4" />;
      case 'completed': return <Check className="h-4 w-4" />;
      case 'cancelled': return <AlertTriangle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch(status.toLowerCase()) {
      case 'new': return 'bg-blue-100 text-blue-800 border-blue-300';
      case 'preparing': return 'bg-amber-100 text-amber-800 border-amber-300';
      case 'ready': return 'bg-green-100 text-green-800 border-green-300';
      case 'delivered': return 'bg-indigo-100 text-indigo-800 border-indigo-300';
      case 'completed': return 'bg-green-100 text-green-800 border-green-300';
      case 'cancelled': return 'bg-red-100 text-red-800 border-red-300';
      default: return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };

  // Format date for display
  const formatOrderDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString(undefined, {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // View order details
  const viewOrderDetails = (orderId: string) => {
    navigate(`/order-status/${orderId}`);
    setOpen(false);
  };

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="relative"
          aria-label={t('yourOrders')}
        >
          <ClipboardList className="h-5 w-5" />
          {activeOrdersCount > 0 && (
            <Badge
              className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 bg-restaurant-primary text-white"
            >
              {activeOrdersCount}
            </Badge>
          )}
        </Button>
      </SheetTrigger>
      <SheetContent>
        <SheetHeader className="pb-4">
          <SheetTitle>
            {t('yourOrders')}
            {activeOrdersCount > 0 && (
              <Badge variant="outline" className="ml-2 bg-restaurant-primary/10">
                {activeOrdersCount} {t('active')}
              </Badge>
            )}
          </SheetTitle>

          {/* Order expiration info */}
          {expirationInfo && expirationInfo.hasOrders && (
            <div className="text-xs text-muted-foreground mt-2 flex items-center gap-1">
              <Clock className="h-3 w-3" />
              <span>
                Receipts expire in {expirationInfo.hours}h {expirationInfo.minutes}m
              </span>
            </div>
          )}
        </SheetHeader>

        {orders.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-64 text-center">
            <ShoppingBag className="h-12 w-12 text-gray-300 mb-2" />
            <p className="text-muted-foreground">{t('noOrders')}</p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => {
                navigate('/menu');
                setOpen(false);
              }}
            >
              {t('browseMenu')}
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Active orders section */}
            {activeOrdersCount > 0 && (
              <div>
                <h3 className="text-sm font-medium mb-2 text-restaurant-foreground">{t('activeOrders')}</h3>
                <div className="space-y-3">
                  {orders
                    .filter(order => !['completed', 'cancelled'].includes(order.status.toLowerCase()))
                    .map(order => (
                      <div
                        key={order.id}
                        className="p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
                        onClick={() => viewOrderDetails(order.id)}
                      >
                        <div className="flex justify-between items-start mb-1">
                          <div className="font-medium">
                            {t('order')} #{order.id.substring(0, 8)}
                            <div className="mt-1">
                              <TableInfoBadge tableId={order.table_id} />
                            </div>
                          </div>
                          <Badge variant="outline" className={cn("text-xs", getStatusColor(order.status))}>
                            <span className="flex items-center">
                              {getStatusIcon(order.status)}
                              <span className="ml-1">{order.status}</span>
                            </span>
                          </Badge>
                        </div>

                        <div className="text-sm text-muted-foreground mb-1">
                          {formatOrderDate(order.created_at)}
                        </div>

                        <div className="flex justify-between text-sm">
                          <span>{order.items?.length || 0} {t('items')}</span>
                          <span className="font-medium">{formatCurrency(order.total_amount)}</span>
                        </div>

                        <div className="mt-2 text-xs text-muted-foreground">
                          {getOrderStatusMessage(order.status)}
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            )}

            {/* Past orders section */}
            {orders.some(order => ['completed', 'cancelled'].includes(order.status.toLowerCase())) && (
              <div className="mt-6">
                <h3 className="text-sm font-medium mb-2 text-restaurant-foreground">{t('pastOrders')}</h3>
                <div className="space-y-3">
                  {orders
                    .filter(order => ['completed', 'cancelled'].includes(order.status.toLowerCase()))
                    .map(order => (
                      <div
                        key={order.id}
                        className="p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
                        onClick={() => viewOrderDetails(order.id)}
                      >
                        <div className="flex justify-between items-start mb-1">
                          <div className="font-medium">
                            {t('order')} #{order.id.substring(0, 8)}
                            <div className="mt-1">
                              <TableInfoBadge tableId={order.table_id} />
                            </div>
                          </div>
                          <Badge variant="outline" className={cn("text-xs", getStatusColor(order.status))}>
                            {order.status}
                          </Badge>
                        </div>

                        <div className="text-sm text-muted-foreground mb-1">
                          {formatOrderDate(order.created_at)}
                        </div>

                        <div className="flex justify-between text-sm">
                          <span>{order.items?.length || 0} {t('items')}</span>
                          <span className="font-medium">{formatCurrency(order.total_amount)}</span>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            )}
          </div>
        )}
      </SheetContent>
    </Sheet>
  );
}
