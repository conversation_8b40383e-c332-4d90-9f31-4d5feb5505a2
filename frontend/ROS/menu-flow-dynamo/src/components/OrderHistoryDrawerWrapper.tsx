import React from 'react';
import { useLocation, useSearchParams } from 'react-router-dom';
import { OrderHistoryDrawer } from './OrderHistoryDrawer';

/**
 * OrderHistoryDrawerWrapper
 * 
 * This component conditionally renders the OrderHistoryDrawer
 * based on the current context, showing it only when a customer is viewing
 * a menu with a valid table ID or during the ordering flow.
 */
export const OrderHistoryDrawerWrapper: React.FC = () => {
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const currentPath = location.pathname;
  
  // Get table ID from URL parameters (supports both 'table' and 'tableId' for compatibility)
  const tableId = searchParams.get('tableId') || searchParams.get('table');
  
  // Define specific customer-facing order flow paths
  const orderFlowPaths = [
    '/order-confirmation',
  ];
  
  // Also match order status paths
  const isOrderStatusPath = currentPath.startsWith('/order-status/');
  
  // Check if we're in a customer context
  const isCustomerContext = (
    // Either we have a table ID (customer scanning QR code)
    !!tableId ||
    // Or we're in a specific part of the ordering flow
    orderFlowPaths.includes(currentPath) || 
    isOrderStatusPath
  );
  
  // Only render the OrderHistoryDrawer for actual customers with orders
  if (!isCustomerContext) {
    return null;
  }

  return null; // OrderHistoryDrawer is now integrated into Header component
};

export default OrderHistoryDrawerWrapper;
