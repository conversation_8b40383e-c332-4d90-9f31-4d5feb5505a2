import React, { useState, useEffect, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from '@/components/ui/dialog';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/components/ui/use-toast';
import { useLanguage } from '@/contexts/LanguageContext';
import { useQuery } from '@tanstack/react-query';
import { Upload, Image as ImageIcon } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useRestaurant } from '@/contexts/RestaurantContext';
import { fetchMenus as fetchMenusFromDb, Menu } from '@/services/menuDbService';
import { fetchTables as fetchTablesFromDb } from '@/services/tableDbService';
import { createMenuItem, updateMenuItem } from '@/services/menuItemDbService';
import { assignMenusToTable } from '@/services/tableDbService';

interface MenuItemFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  editingItem?: MenuItem | null;
}

interface MenuItem {
  id: string;
  name: string;
  description?: string;
  price: number;
  menu_id: string;
  is_available: boolean;
  image_url?: string;
  category?: string;
  ingredients?: string[];
  allergens?: string[];
  calories?: number;
  is_vegan?: boolean;
  is_vegetarian?: boolean;
  is_gluten_free?: boolean;
  is_dairy_free?: boolean;
  is_nut_free?: boolean;
  spice_level?: number;
  dietary_notes?: string;
}

// Fetch menus for dropdown from database
const fetchMenus = async (restaurantInfo: { id: string; name: string }) => {
  try {
    // Skip fetching if restaurant info is not ready
    if (!restaurantInfo || !restaurantInfo.id) {
      console.warn('Skipping fetchMenus in MenuItemForm: restaurantInfo is not ready');
      return [];
    }

    // Get menus from database, passing the restaurant info to ensure proper filtering
    const menus = await fetchMenusFromDb(restaurantInfo);

    // Map to the format needed for the dropdown
    return menus.map((menu: Menu) => ({
      id: menu.id,
      name: menu.name
    })).sort((a, b) => a.name.localeCompare(b.name));
  } catch (error) {
    console.error('Error fetching menus from database:', error);
    return [];
  }
};

// Define the proper type for table dropdown items
interface TableOption {
  id: string;
  name: string;
}

// Define type for restaurant table based on the database schema
interface RestaurantTable {
  id: string;
  restaurant_id: string;
  table_number: string;
  capacity?: number;
  location?: string;
  is_occupied?: boolean;
  status?: string;
  qr_code_url?: string;
  created_at?: string;
  updated_at?: string;
}

// Fetch tables for dropdown from database
const fetchTables = async (restaurantInfo: { id: string; name: string }): Promise<TableOption[]> => {
  try {
    // Skip fetching if restaurant info is not ready
    if (!restaurantInfo || !restaurantInfo.id) {
      console.warn('Skipping fetchTables in MenuItemForm: restaurantInfo is not ready');
      return [];
    }

    // Query the database directly for tables with this restaurant_id
    const { data: restaurantTables, error } = await supabase
      .from('restaurant_tables')
      .select('*')
      .eq('restaurant_id', restaurantInfo.id);

    if (error) {
      console.error('Error fetching tables from Supabase:', error);
      return [];
    }

    // Map to the format needed for the dropdown
    return (restaurantTables || []).map((table: RestaurantTable) => ({
      id: table.id,
      name: `Table ${table.table_number}` + (table.location ? ` (${table.location})` : '')
    })).sort((a, b) => a.name.localeCompare(b.name));
  } catch (error) {
    console.error('Error fetching tables from database:', error);
    return [];
  }
};

const MenuItemForm: React.FC<MenuItemFormProps> = ({ isOpen, onClose, onSuccess, editingItem }) => {
  const { toast } = useToast();
  const { t } = useLanguage();
  const { user } = useAuth();
  const { restaurantInfo } = useRestaurant();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedTables, setSelectedTables] = useState<string[]>([]);
  const [showTableAssignment, setShowTableAssignment] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Log authentication and restaurant context for debugging
  useEffect(() => {
    if (isOpen) {

    }
  }, [isOpen, user, restaurantInfo]);

  // Wrapper function for fetchMenus to adapt to React Query's expected signature
  const fetchMenusWrapper = async () => {
    if (!restaurantInfo || !restaurantInfo.id) {
      console.warn('Cannot fetch menus: Restaurant context not ready');
      return [];
    }
    return fetchMenus(restaurantInfo);
  };

  // Wrapper function for fetchTables to adapt to React Query's expected signature
  const fetchTablesWrapper = async (): Promise<TableOption[]> => {
    // Ensure we have restaurant info before fetching tables
    if (!restaurantInfo?.id) {

      return [];
    }
    return await fetchTables(restaurantInfo);
  };

  const { data: menus, isLoading: menusLoading } = useQuery({
    queryKey: ['menus', restaurantInfo?.id],
    queryFn: fetchMenusWrapper,
    enabled: isOpen && !!restaurantInfo?.id, // Only run when dialog is open and restaurant ID is available
    staleTime: 0, // Don't use cached data
    refetchOnMount: true // Always refetch when component mounts
  });

  const { data: tables } = useQuery({
    queryKey: ['tables_for_menu', restaurantInfo?.id],
    queryFn: fetchTablesWrapper,
    enabled: isOpen && showTableAssignment && !!restaurantInfo?.id
  });

  const { register, handleSubmit, setValue, watch, reset, formState: { errors } } = useForm({
    defaultValues: {
      name: '',
      description: '',
      price: 0,
      menu_id: '',
      is_available: true,
      image_url: '',
      category: 'food', // Default category
      is_vegan: false,
      is_vegetarian: false,
      is_gluten_free: false,
      is_dairy_free: false,
      is_nut_free: false,
      spice_level: 0,
      allergens: '',
      dietary_notes: ''
    }
  });

  // Set form values when editing an item
  useEffect(() => {
    if (editingItem) {
      setValue('name', editingItem.name);
      setValue('description', editingItem.description || '');
      setValue('price', editingItem.price);
      setValue('menu_id', editingItem.menu_id);
      setValue('is_available', editingItem.is_available);
      setValue('image_url', editingItem.image_url || '');
      setValue('category', editingItem.category || 'food'); // Set category, default to 'food' if not present

      // Set dietary options
      setValue('is_vegan', editingItem.is_vegan || false);
      setValue('is_vegetarian', editingItem.is_vegetarian || false);
      setValue('is_gluten_free', editingItem.is_gluten_free || false);
      setValue('is_dairy_free', editingItem.is_dairy_free || false);
      setValue('is_nut_free', editingItem.is_nut_free || false);
      setValue('spice_level', editingItem.spice_level || 0);
      setValue('allergens', editingItem.allergens?.join(', ') || '');
      setValue('dietary_notes', editingItem.dietary_notes || '');

      if (editingItem.image_url) {
        setImagePreview(editingItem.image_url);
      }
    }
  }, [editingItem, setValue]);

  // Define proper type for form data
  interface MenuItemFormData {
    name: string;
    description?: string;
    price: number;
    category?: string;
    menu_id: string;
    is_available: boolean;
    image_url?: string;
    is_vegan?: boolean;
    is_vegetarian?: boolean;
    is_gluten_free?: boolean;
    is_dairy_free?: boolean;
    is_nut_free?: boolean;
    spice_level?: number;
    allergens?: string;
    dietary_notes?: string;
  }

  const onSubmit = async (data: MenuItemFormData) => {
    try {
      // Check if we have user and restaurant context
      if (!user) {
        throw new Error('Authentication required. Please log in again.');
      }

      if (!restaurantInfo?.id) {
        throw new Error('Restaurant information is missing. Please select a restaurant.');
      }

      setIsSubmitting(true);

      // Handle image upload if there's a file selected
      let imageUrl = data.image_url;
      const fileInput = fileInputRef.current;

      if (fileInput && fileInput.files && fileInput.files.length > 0) {
        const file = fileInput.files[0];

        // Validate file size and type
        if (file.size > 2 * 1024 * 1024) { // 2MB max
          toast({
            variant: "destructive",
            title: "Image too large",
            description: "Please use an image smaller than 2MB for better performance",
          });
          // Continue with upload but warn the user
        }

        console.log(`Processing image with content type: ${file.type}`);

        // Always convert images to base64 and store directly in the database
        // This avoids all the Supabase storage issues with content types
        if (file.type.startsWith('image/')) {
          try {
            // Create a canvas to convert and resize the image
            const img = new Image();
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            // Wait for the image to load
            await new Promise((resolve, reject) => {
              img.onload = resolve;
              img.onerror = reject;
              img.src = URL.createObjectURL(file);
            });

            // Calculate new dimensions (max 800px width/height while maintaining aspect ratio)
            let width = img.width;
            let height = img.height;
            const maxDimension = 800;

            if (width > height && width > maxDimension) {
              height = Math.round(height * (maxDimension / width));
              width = maxDimension;
            } else if (height > maxDimension) {
              width = Math.round(width * (maxDimension / height));
              height = maxDimension;
            }

            // Set canvas dimensions to the resized image
            canvas.width = width;
            canvas.height = height;

            // Draw the image on the canvas with smoothing
            if (ctx) {
              ctx.imageSmoothingQuality = 'high';
              ctx.drawImage(img, 0, 0, width, height);

              // Determine best format based on the original image
              let outputFormat = 'image/jpeg';
              let quality = 0.85;

              // Use WebP for better compression if it's supported
              if (file.type === 'image/webp') {
                outputFormat = 'image/webp';
                quality = 0.85;
              } else if (file.type === 'image/png' && !file.name.toLowerCase().includes('photo')) {
                // Keep PNGs for illustrations/graphics, but not photos
                outputFormat = 'image/png';
                quality = 0.9;
              }

              // Convert to data URL with appropriate format and quality
              const dataUrl = canvas.toDataURL(outputFormat, quality);
              console.log(`Converted image to data URL: ${Math.round(dataUrl.length/1024)}KB using ${outputFormat}`);

              // Store the data URL directly in the database
              imageUrl = dataUrl;
              console.log(`Image converted to data URL successfully.`);

              // Clean up the object URL
              URL.revokeObjectURL(img.src);
            }
          } catch (error) {
            console.error('Error converting image to base64:', error);
            toast({
              variant: "destructive",
              title: "Image processing error",
              description: "There was a problem processing your image. Please try a different image.",
            });
          }
        } else {
          // Not an image file
          toast({
            variant: "destructive",
            title: "Invalid file type",
            description: "Please upload an image file (JPEG, PNG, WebP, etc.)",
          });
        }
      }

      // Parse the price to a number or default to 0
      const numericPrice = Number(data.price) || 0;

      // Prepare item data with all required fields
      const itemData = {
        ...data,
        price: numericPrice,
        current_price: numericPrice, // Ensure current_price is set to avoid NOT NULL constraint
        base_price: numericPrice,    // Ensure base_price is also set
        category: data.category || 'Uncategorized', // Category is required in the database
        image_url: imageUrl,
        restaurant_id: restaurantInfo.id,
        // Dietary options
        is_vegan: data.is_vegan || false,
        is_vegetarian: data.is_vegetarian || false,
        is_gluten_free: data.is_gluten_free || false,
        is_dairy_free: data.is_dairy_free || false,
        is_nut_free: data.is_nut_free || false,
        spice_level: data.spice_level || 0,
        allergens: data.allergens ? data.allergens.split(',').map(s => s.trim()).filter(s => s) : [],
        dietary_notes: data.dietary_notes || ''
        // Note: user_id column doesn't exist in menu_items table
      };

      // Create or update the menu item
      if (editingItem) {
        await updateMenuItem(editingItem.id, itemData);
        toast({
          title: "Menu item updated",
          description: `${data.name} has been updated successfully.`,
        });
      } else {
        await createMenuItem(itemData);
        toast({
          title: "Menu item created",
          description: `${data.name} has been added to the menu.`,
        });
      }

      // Call the success callback
      onSuccess();

      // Reset form if not showing table assignment
      if (!showTableAssignment) {
        handleClose();
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : "An error occurred";
      toast({
        variant: "destructive",
        title: "Error",
        description: errorMessage,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleContinueToTableAssignment = async (data: MenuItemFormData) => {
    // Save the menu item first
    try {
      await onSubmit(data);
      setShowTableAssignment(true);
    } catch (e) {
      // onSubmit already toasts; simply stay on the form
    }
  };

  const handleClose = () => {
    reset();
    setShowTableAssignment(false);
    setImagePreview(null);
    onClose();
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Check file size (limit to 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        variant: "destructive",
        title: "File too large",
        description: "Image must be less than 5MB",
      });
      return;
    }

    // Check file type
    if (!file.type.startsWith('image/')) {
      toast({
        variant: "destructive",
        title: "Invalid file type",
        description: "Please upload an image file",
      });
      return;
    }

    // Create a preview URL
    const reader = new FileReader();
    reader.onload = (e) => {
      setImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle>
            {showTableAssignment
              ? 'Assign Menu to Tables'
              : editingItem
                ? `${t('edit')} ${editingItem.name}`
                : t('addMenuItem')}
          </DialogTitle>
          <DialogDescription>
            {showTableAssignment
              ? 'Select which tables should have access to this menu item'
              : editingItem
                ? t('updateMenuItem')
                : t('createMenuItem')}
          </DialogDescription>
        </DialogHeader>

        {showTableAssignment ? (
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Select the tables that should have access to this menu:
            </p>

            <div className="grid grid-cols-2 gap-2 max-h-[300px] overflow-y-auto p-2">
              {tables && tables.map(table => (
                <div
                  key={table.id}
                  className="flex items-center space-x-2 p-2 border rounded-md"
                >
                  <input
                    type="checkbox"
                    id={`table-${table.id}`}
                    checked={selectedTables.includes(table.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedTables([...selectedTables, table.id]);
                      } else {
                        setSelectedTables(selectedTables.filter(id => id !== table.id));
                      }
                    }}
                    className="h-4 w-4 rounded border-gray-300"
                  />
                  <label
                    htmlFor={`table-${table.id}`}
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {table.name}
                  </label>
                </div>
              ))}
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowTableAssignment(false)}
              >
                Back
              </Button>
              <Button
                type="button"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : 'Save Assignments'}
              </Button>
            </DialogFooter>
          </div>
        ) : (
          <form onSubmit={handleSubmit(handleContinueToTableAssignment)} className="space-y-3">
            {/* Basic Info - 2 Column Layout */}
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-1">
                <Label htmlFor="name" className="text-xs font-medium">{t('name')}</Label>
                <Input
                  id="name"
                  className="h-8 text-sm"
                  {...register('name', { required: 'Name is required' })}
                />
                {errors.name && (
                  <p className="text-xs text-red-500">{errors.name.message}</p>
                )}
              </div>

              <div className="space-y-1">
                <Label htmlFor="price" className="text-xs font-medium">{t('price')}</Label>
                <Input
                  id="price"
                  type="number"
                  step="0.01"
                  min="0"
                  className="h-8 text-sm"
                  {...register('price', {
                    required: 'Price is required',
                    min: {
                      value: 0,
                      message: 'Price must be positive'
                    }
                  })}
                />
                {errors.price && (
                  <p className="text-xs text-red-500">{errors.price.message}</p>
                )}
              </div>
            </div>

            {/* Description */}
            <div className="space-y-1">
              <Label htmlFor="description" className="text-xs font-medium">{t('description')}</Label>
              <Textarea
                id="description"
                className="h-12 text-sm resize-none"
                {...register('description')}
              />
            </div>

            {/* Category, Menu, Available - 3 Column Layout */}
            <div className="grid grid-cols-3 gap-3">
              <div className="space-y-1">
                <Label htmlFor="category" className="text-xs font-medium">{t('category')}</Label>
                <Select
                  defaultValue={editingItem?.category || 'food'}
                  onValueChange={(value) => setValue('category', value)}
                >
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="food">{t('food')}</SelectItem>
                    <SelectItem value="drinks">{t('drinks')}</SelectItem>
                    <SelectItem value="desserts">{t('desserts')}</SelectItem>
                    <SelectItem value="starters">{t('starters')}</SelectItem>
                    <SelectItem value="mains">{t('mains')}</SelectItem>
                    <SelectItem value="sides">{t('sides')}</SelectItem>
                    <SelectItem value="specials">{t('specials')}</SelectItem>
                  </SelectContent>
                </Select>
                <input type="hidden" {...register('category')} />
              </div>

              <div className="space-y-1">
                <Label htmlFor="menu_id" className="text-xs font-medium">{t('menu')}</Label>
                <Select
                  defaultValue={editingItem?.menu_id}
                  onValueChange={(value) => setValue('menu_id', value)}
                >
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue placeholder="Select..." />
                  </SelectTrigger>
                  <SelectContent>
                    {menusLoading ? (
                      <SelectItem value="loading" disabled>
                        Loading...
                      </SelectItem>
                    ) : menus && menus.length > 0 ? (
                      menus.map((menu) => (
                        <SelectItem key={menu.id} value={menu.id}>
                          {menu.name}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="none" disabled>
                        No menus
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
                <input type="hidden" {...register('menu_id', { required: 'Menu is required' })} />
                {errors.menu_id && (
                  <p className="text-xs text-red-500">{errors.menu_id.message}</p>
                )}
              </div>

              <div className="space-y-1">
                <Label className="text-xs font-medium">{t('available')}</Label>
                <input type="hidden" {...register('is_available')} />
                <div className="flex items-center h-8">
                  <Switch
                    id="is_available"
                    checked={watch('is_available') !== false}
                    onCheckedChange={(checked) => setValue('is_available', checked)}
                  />
                  <Label htmlFor="is_available" className="ml-2 text-xs text-muted-foreground">
                    {watch('is_available') !== false ? 'Yes' : 'No'}
                  </Label>
                </div>
              </div>
            </div>

            {/* Dietary Options - Compact with Text Labels */}
            <div className="border-t pt-2">
              <Label className="text-xs font-medium text-gray-600 mb-2 block">{t('dietaryOptions')}</Label>

              {/* Hidden inputs to ensure dietary options are always registered */}
              <input type="hidden" {...register('is_vegan')} />
              <input type="hidden" {...register('is_vegetarian')} />
              <input type="hidden" {...register('is_gluten_free')} />
              <input type="hidden" {...register('is_dairy_free')} />
              <input type="hidden" {...register('is_nut_free')} />

              {/* All dietary options in two rows */}
              <div className="grid grid-cols-3 gap-2 text-xs">
                <div className="flex items-center space-x-1">
                  <Switch
                    id="is_vegan"
                    checked={watch('is_vegan') || false}
                    onCheckedChange={(checked) => setValue('is_vegan', checked)}
                    className="scale-75"
                  />
                  <Label htmlFor="is_vegan" className="text-xs">{t('vegan')}</Label>
                </div>

                <div className="flex items-center space-x-1">
                  <Switch
                    id="is_vegetarian"
                    checked={watch('is_vegetarian') || false}
                    onCheckedChange={(checked) => setValue('is_vegetarian', checked)}
                    className="scale-75"
                  />
                  <Label htmlFor="is_vegetarian" className="text-xs">{t('vegetarian')}</Label>
                </div>

                <div className="flex items-center space-x-1">
                  <Switch
                    id="is_gluten_free"
                    checked={watch('is_gluten_free') || false}
                    onCheckedChange={(checked) => setValue('is_gluten_free', checked)}
                    className="scale-75"
                  />
                  <Label htmlFor="is_gluten_free" className="text-xs">{t('glutenFree')}</Label>
                </div>

                <div className="flex items-center space-x-1">
                  <Switch
                    id="is_dairy_free"
                    checked={watch('is_dairy_free') || false}
                    onCheckedChange={(checked) => setValue('is_dairy_free', checked)}
                    className="scale-75"
                  />
                  <Label htmlFor="is_dairy_free" className="text-xs">{t('dairyFree')}</Label>
                </div>

                <div className="flex items-center space-x-1">
                  <Switch
                    id="is_nut_free"
                    checked={watch('is_nut_free') || false}
                    onCheckedChange={(checked) => setValue('is_nut_free', checked)}
                    className="scale-75"
                  />
                  <Label htmlFor="is_nut_free" className="text-xs">{t('nutFree')}</Label>
                </div>

                <div className="flex items-center space-x-1">
                  <Label className="text-xs">{t('spiceLevel')}</Label>
                  <Select
                    defaultValue={editingItem?.spice_level?.toString() || '0'}
                    onValueChange={(value) => setValue('spice_level', parseInt(value))}
                  >
                    <SelectTrigger className="h-6 text-xs w-12 p-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0">0</SelectItem>
                      <SelectItem value="1">1</SelectItem>
                      <SelectItem value="2">2</SelectItem>
                      <SelectItem value="3">3</SelectItem>
                      <SelectItem value="4">4</SelectItem>
                      <SelectItem value="5">5</SelectItem>
                    </SelectContent>
                  </Select>
                  <input type="hidden" {...register('spice_level')} />
                </div>
              </div>

              {/* Allergens and Notes - Compact */}
              <div className="grid grid-cols-2 gap-2 mt-2">
                <div>
                  <Label htmlFor="allergens" className="text-xs text-gray-500">Allergens</Label>
                  <Textarea
                    id="allergens"
                    placeholder="nuts, dairy..."
                    className="h-8 text-xs resize-none"
                    {...register('allergens')}
                  />
                </div>
                <div>
                  <Label htmlFor="dietary_notes" className="text-xs text-gray-500">Notes</Label>
                  <Textarea
                    id="dietary_notes"
                    placeholder="dietary info..."
                    className="h-8 text-xs resize-none"
                    {...register('dietary_notes')}
                  />
                </div>
              </div>
            </div>

            {/* Image Upload - Compact */}
            <div className="space-y-1">
              <Label className="text-xs font-medium">{t('image')}</Label>
              <div className="flex items-center gap-2">
                {imagePreview ? (
                  <div className="relative w-12 h-12 rounded border">
                    <img
                      src={imagePreview}
                      alt="Preview"
                      className="w-full h-full object-cover rounded"
                    />
                    <button
                      type="button"
                      className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-4 h-4 text-xs flex items-center justify-center"
                      onClick={() => {
                        setImagePreview(null);
                        setValue('image_url', '');
                        if (fileInputRef.current) {
                          fileInputRef.current.value = '';
                        }
                      }}
                    >
                      ×
                    </button>
                  </div>
                ) : (
                  <div className="w-12 h-12 border rounded flex items-center justify-center bg-gray-50">
                    <ImageIcon className="h-4 w-4 text-gray-400" />
                  </div>
                )}
                <div className="flex-1">
                  <input
                    type="file"
                    id="image"
                    className="hidden"
                    accept="image/*"
                    ref={fileInputRef}
                    onChange={handleImageUpload}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    className="w-full h-8 text-xs"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Upload className="mr-1 h-3 w-3" />
                    Upload
                  </Button>
                </div>
              </div>
            </div>

            <DialogFooter className="pt-2">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                {t('cancel')}
              </Button>
              <Button type="submit" size="sm" disabled={isSubmitting}>
                {isSubmitting ? t('saving') : editingItem ? t('save') : t('create')}
              </Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default MenuItemForm;
