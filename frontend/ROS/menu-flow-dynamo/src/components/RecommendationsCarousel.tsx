import React, { useState, useEffect, useRef } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useTheme } from '@/contexts/ThemeContext';
import { useRestaurant } from '@/contexts/RestaurantContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { supabase } from '@/integrations/supabase/client';
import { ChefHat, TrendingUp, Star, ChevronLeft, ChevronRight, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { applyDynamicPricing, isDynamicPricingEnabled } from '@/services/dynamicPricingService';

interface RecommendedItem {
  menu_item_id: string;
  name: string;
  category: string;
  current_price: number;
  recommendation_type: 'featured' | 'popular';
  priority_score: number;
  chef_notes?: string;
  total_orders: number;
}

interface RecommendationsCarouselProps {
  onAddToCart?: (itemId: string) => void;
  className?: string;
}

const RecommendationsCarousel: React.FC<RecommendationsCarouselProps> = ({
  onAddToCart,
  className = ""
}) => {
  const { themeColors, brandSettings } = useTheme();
  const { restaurantInfo } = useRestaurant();
  const { t } = useLanguage();
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);

  // The restaurantInfo.id is already the business ID from RestaurantContext
  // No need to query restaurant_details table
  const businessId = restaurantInfo?.id || null;

  // Fetch recommendations using chef recommendations system
  const { data: recommendations, isLoading, error, refetch } = useQuery({
    queryKey: ['recommendations', businessId],
    queryFn: async () => {
      if (!businessId) {
        return [];
      }

      try {
        // For ROS restaurants, use the RPC function directly
        const { data, error } = await supabase.rpc('get_menu_recommendations', {
          restaurant_business_id: businessId,
          max_featured: 2,
          max_popular: 2
        });

        if (!error && data && data.length > 0) {
          // Apply dynamic pricing to recommendations if enabled
          const dynamicPricingEnabled = await isDynamicPricingEnabled(businessId);

          if (dynamicPricingEnabled) {
            // Convert recommendations to menu items format for dynamic pricing
            const menuItems = data.map(item => ({
              id: item.menu_item_id,
              name: item.name,
              category: item.category,
              price: item.current_price,
              base_price: item.current_price, // Use current_price as base_price
              description: '',
              image: '',
              allergies: [],
              calories: 0
            }));

            // Apply dynamic pricing
            const pricedItems = await applyDynamicPricing(menuItems, businessId, true);

            // Convert back to recommendation format with updated prices
            const pricedRecommendations: RecommendedItem[] = data.map((item, index) => ({
              menu_item_id: item.menu_item_id,
              name: item.name,
              category: item.category,
              current_price: pricedItems[index]?.price || item.current_price,
              recommendation_type: item.recommendation_type as 'featured' | 'popular',
              priority_score: item.priority_score,
              chef_notes: item.chef_notes,
              total_orders: item.total_orders
            }));

            return pricedRecommendations;
          }

          return data as RecommendedItem[];
        }

        // Final fallback: For ROS restaurants, get ALL menus for this restaurant
        const { data: restaurantMenus, error: menuByRestaurantIdError } = await supabase
          .from('menus')
          .select('id, name')
          .eq('restaurant_id', businessId)
          .limit(10);

        if (!menuByRestaurantIdError && restaurantMenus && restaurantMenus.length > 0) {


          // Get menu items from all menus for this restaurant
          const menuIds = restaurantMenus.map(menu => menu.id);

          const { data: menuItems, error: itemsError } = await supabase
            .from('menu_items')
            .select('id, name, category, current_price, description')
            .in('menu_id', menuIds)
            .eq('is_available', true)
            .order('created_at', { ascending: false })
            .limit(6);

          if (!itemsError && menuItems && menuItems.length > 0) {
            // Apply dynamic pricing to fallback items if enabled
            const dynamicPricingEnabled = await isDynamicPricingEnabled(businessId);
            let finalMenuItems = menuItems;

            if (dynamicPricingEnabled) {
              // Convert to format expected by dynamic pricing
              const pricingMenuItems = menuItems.map(item => ({
                id: item.id,
                name: item.name,
                category: item.category,
                price: item.current_price,
                base_price: item.current_price,
                description: item.description || '',
                image: '',
                allergies: [],
                calories: 0
              }));

              const pricedItems = await applyDynamicPricing(pricingMenuItems, businessId, true);
              finalMenuItems = menuItems.map((item, index) => ({
                ...item,
                current_price: pricedItems[index]?.price || item.current_price
              }));
            }

            // Convert menu items to recommendation format
            const fallbackRecommendations: RecommendedItem[] = finalMenuItems.map((item, index) => ({
              menu_item_id: item.id,
              name: item.name,
              category: item.category,
              current_price: item.current_price,
              recommendation_type: 'popular' as const,
              priority_score: 10 - index, // Newer items get higher priority
              chef_notes: 'Fresh and available now!',
              total_orders: 0
            }));

            return fallbackRecommendations;
          }
        } else {
          // Second attempt: Look for menu by business_id (legacy format)
          const { data: menuByBusinessId, error: menuByBusinessIdError } = await supabase
            .from('menus')
            .select('id')
            .eq('restaurant_id', businessId)
            .limit(1);

          if (!menuByBusinessIdError && menuByBusinessId && menuByBusinessId.length > 0) {
            const menu = menuByBusinessId[0];

            // Get available menu items as fallback recommendations
            const { data: menuItems, error: itemsError } = await supabase
              .from('menu_items')
              .select('id, name, category, current_price, description')
              .eq('menu_id', menu.id)
              .eq('is_available', true)
              .order('created_at', { ascending: false })
              .limit(6);

            if (itemsError) {
              return [];
            }

            // Apply dynamic pricing to fallback items if enabled
            const dynamicPricingEnabled = await isDynamicPricingEnabled(businessId);
            let finalMenuItems = menuItems || [];

            if (dynamicPricingEnabled && finalMenuItems.length > 0) {
              // Convert to format expected by dynamic pricing
              const pricingMenuItems = finalMenuItems.map(item => ({
                id: item.id,
                name: item.name,
                category: item.category,
                price: item.current_price,
                base_price: item.current_price,
                description: item.description || '',
                image: '',
                allergies: [],
                calories: 0
              }));

              const pricedItems = await applyDynamicPricing(pricingMenuItems, businessId, true);
              finalMenuItems = finalMenuItems.map((item, index) => ({
                ...item,
                current_price: pricedItems[index]?.price || item.current_price
              }));
            }

            // Convert menu items to recommendation format
            const fallbackRecommendations: RecommendedItem[] = finalMenuItems.map((item, index) => ({
              menu_item_id: item.id,
              name: item.name,
              category: item.category,
              current_price: item.current_price,
              recommendation_type: 'popular' as const,
              priority_score: 10 - index, // Newer items get higher priority
              chef_notes: 'Fresh and available now!',
              total_orders: 0
            }));

            return fallbackRecommendations;
          }
        }

        return [];
      } catch (error) {

        return [];
      }
    },
    enabled: !!businessId,
    staleTime: 2 * 60 * 1000, // 2 minutes - refresh more frequently for recent popular items
  });

  // Listen for dynamic pricing changes and refetch recommendations
  useEffect(() => {
    const handleDynamicPricingChange = () => {
      refetch();
    };

    window.addEventListener('dynamic-pricing-changed', handleDynamicPricingChange);
    window.addEventListener('dynamic-pricing-updated', handleDynamicPricingChange);

    return () => {
      window.removeEventListener('dynamic-pricing-changed', handleDynamicPricingChange);
      window.removeEventListener('dynamic-pricing-updated', handleDynamicPricingChange);
    };
  }, [refetch]);

  // Update scroll button states
  const updateScrollButtons = () => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const scrollLeft = container.scrollLeft;
    const scrollWidth = container.scrollWidth;
    const clientWidth = container.clientWidth;

    // Add small tolerance for floating point precision
    const tolerance = 1;

    setCanScrollLeft(scrollLeft > tolerance);
    setCanScrollRight(scrollLeft < scrollWidth - clientWidth - tolerance);
  };

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    container.addEventListener('scroll', updateScrollButtons);
    updateScrollButtons(); // Initial check

    return () => container.removeEventListener('scroll', updateScrollButtons);
  }, [recommendations]);

  const scroll = (direction: 'left' | 'right') => {
    const container = scrollContainerRef.current;
    if (!container) return;

    // Calculate scroll amount based on container width for better responsiveness
    const containerWidth = container.clientWidth;
    const scrollAmount = Math.min(containerWidth * 0.8, 320); // Responsive scroll amount
    const targetScroll = direction === 'left'
      ? container.scrollLeft - scrollAmount
      : container.scrollLeft + scrollAmount;

    container.scrollTo({
      left: targetScroll,
      behavior: 'smooth'
    });
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-EU', {
      style: 'currency',
      currency: 'EUR'
    }).format(price);
  };

  const getRecommendationIcon = (type: string) => {
    switch (type) {
      case 'featured':
        return <ChefHat className="w-4 h-4" />;
      case 'popular':
        return <TrendingUp className="w-4 h-4" />;
      default:
        return <Star className="w-4 h-4" />;
    }
  };

  const getRecommendationLabel = (type: string) => {
    switch (type) {
      case 'featured':
        return t('chefsPickLabel');
      case 'popular':
        return t('popularLabel');
      default:
        return t('recommendedLabel');
    }
  };

  if (isLoading) {
    return (
      <div className={`mb-4 ${className}`}>
        <div className="flex items-center justify-between mb-3">
          <div className="h-6 bg-gray-300 rounded w-48 animate-pulse" />
        </div>
        <div className="flex space-x-4 overflow-hidden">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="flex-shrink-0 w-80">
              <div className="bg-gray-200 rounded-2xl p-4 animate-pulse h-48" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error || !recommendations || recommendations.length === 0) {
    return null;
  }

  return (
    <div className={`mb-4 ${className}`}>
      {/* Section Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <div
            className="p-1.5 rounded-lg"
            style={{ backgroundColor: `${themeColors.primary}20` }}
          >
            <ChefHat
              className="w-4 h-4"
              style={{ color: themeColors.primary }}
            />
          </div>
          <h2
            className="text-lg font-semibold"
            style={{ color: themeColors.text }}
          >
            {brandSettings.featured_section_title}
          </h2>
        </div>

        {/* Navigation Buttons */}
        <div className="flex space-x-1">
          <Button
            variant="outline"
            size="sm"
            onClick={() => scroll('left')}
            disabled={!canScrollLeft}
            className="p-1.5 border-0 backdrop-blur-sm transition-all duration-200 hover:scale-105 disabled:opacity-30 disabled:cursor-not-allowed focus:ring-2 focus:ring-offset-2"
            style={{
              color: canScrollLeft ? themeColors.text : `${themeColors.text}60`,
              backgroundColor: canScrollLeft ? `${themeColors.card}CC` : `${themeColors.card}40`,
              boxShadow: canScrollLeft ? `0 2px 8px ${themeColors.primary}20` : 'none',
              '--tw-ring-color': themeColors.primary
            } as React.CSSProperties}
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => scroll('right')}
            disabled={!canScrollRight}
            className="p-1.5 border-0 backdrop-blur-sm transition-all duration-200 hover:scale-105 disabled:opacity-30 disabled:cursor-not-allowed focus:ring-2 focus:ring-offset-2"
            style={{
              color: canScrollRight ? themeColors.text : `${themeColors.text}60`,
              backgroundColor: canScrollRight ? `${themeColors.card}CC` : `${themeColors.card}40`,
              boxShadow: canScrollRight ? `0 2px 8px ${themeColors.primary}20` : 'none',
              '--tw-ring-color': themeColors.primary
            } as React.CSSProperties}
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Carousel Container */}
      <div
        ref={scrollContainerRef}
        className="flex space-x-3 overflow-x-auto pb-2 snap-x snap-mandatory"
        style={{
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
          WebkitOverflowScrolling: 'touch', // Smooth scrolling on iOS
          scrollPaddingLeft: '0.75rem',
          scrollPaddingRight: '0.75rem'
        }}
      >
        {recommendations.map((item, index) => (
          <div
            key={`${item.menu_item_id}-${index}`}
            className="flex-shrink-0 w-64 sm:w-60 md:w-64 lg:w-64 group cursor-pointer snap-start focus-within:ring-2 focus-within:ring-offset-2"
            style={{
              minWidth: '240px',
              maxWidth: '256px',
              backgroundColor: themeColors.card,
              backgroundImage: `linear-gradient(135deg, ${themeColors.card}, ${themeColors.card}F5)`,
              '--tw-ring-color': themeColors.primary
            } as React.CSSProperties}
            role="article"
            aria-label={`${item.recommendation_type === 'featured' ? t('chefsPickLabel') : t('popularLabel')} item: ${item.name}`}
          >
            <div className="rounded-xl p-3 h-full border border-white/10 shadow-md hover:shadow-lg transition-all duration-300 group-hover:scale-[1.02] backdrop-blur-sm min-h-[120px] flex flex-col">
              {/* Card Header */}
              <div className="flex items-start justify-between mb-2">
                <div
                  className="flex items-center space-x-1.5 px-2 py-0.5 rounded-full text-xs font-medium"
                  style={{
                    backgroundColor: item.recommendation_type === 'featured'
                      ? `${themeColors.primary}20`
                      : `${themeColors.secondary}20`,
                    color: item.recommendation_type === 'featured'
                      ? themeColors.primary
                      : themeColors.secondary
                  }}
                >
                  {getRecommendationIcon(item.recommendation_type)}
                  <span>{getRecommendationLabel(item.recommendation_type)}</span>
                </div>

                <div className="text-right">
                  <div
                    className="text-base font-bold"
                    style={{ color: themeColors.text }}
                  >
                    {formatPrice(item.current_price)}
                  </div>
                  <div
                    className="text-xs opacity-70"
                    style={{ color: themeColors.text }}
                  >
                    {item.category}
                  </div>
                </div>
              </div>

              {/* Item Details */}
              <div className="flex-1 mb-2">
                <h3
                  className="text-base font-semibold mb-1 line-clamp-2 leading-tight"
                  style={{ color: themeColors.text }}
                >
                  {item.name}
                </h3>

                {item.chef_notes && (
                  <p
                    className="text-xs opacity-80 mb-2 line-clamp-2 leading-relaxed"
                    style={{ color: themeColors.text }}
                  >
                    "{item.chef_notes}"
                  </p>
                )}
              </div>

              {/* Action Button */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => onAddToCart?.(item.menu_item_id)}
                className="w-full font-medium transition-all duration-300 border-0 focus:ring-2 focus:ring-offset-2 group-hover:scale-[1.02] text-sm py-2"
                style={{
                  backgroundColor: `${themeColors.primary}15`,
                  color: themeColors.primary,
                  '--tw-ring-color': themeColors.primary
                } as React.CSSProperties}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = `${themeColors.primary}25`;
                  e.currentTarget.style.transform = 'translateY(-1px)';
                  e.currentTarget.style.boxShadow = `0 4px 12px ${themeColors.primary}30`;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = `${themeColors.primary}15`;
                  e.currentTarget.style.transform = 'translateY(0px)';
                  e.currentTarget.style.boxShadow = 'none';
                }}
              >
                <Plus className="w-3 h-3 mr-1" />
                {t('addToCartButton')}
              </Button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default RecommendationsCarousel;