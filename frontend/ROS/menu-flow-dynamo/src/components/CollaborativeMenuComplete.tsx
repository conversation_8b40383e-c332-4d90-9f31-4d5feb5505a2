/**
 * Complete Collaborative Menu System
 *
 * Integrated component that combines menu display, collaborative cart,
 * chef recommendations, and participant management in one cohesive interface
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import {
  Users,
  ShoppingCart,
  ChefHat,
  Menu as MenuIcon,
  QrCode,
  Loader2,
  RefreshCw,
  Settings,
  AlertCircle
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

// Components
import Header from '@/components/Header';
import MenuSection from '@/components/MenuSection';
import { SmartCollaborativeCartComplete } from '@/components/SmartCollaborativeCartComplete';
import { ChefRecommendationsComplete } from '@/components/ChefRecommendationsComplete';
import TableParticipants from '@/components/TableParticipants';

// Hooks and Services
import { useSharedCart } from '@/hooks/useSharedCart';
import { useTableParticipantsEnhanced } from '@/hooks/useTableParticipantsEnhanced';
import { useRestaurant } from '@/contexts/RestaurantContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { fetchMenuItems } from '@/services/menuService';
import { generateSessionId } from '@/config/app';
import { MenuItemType } from '@/components/MenuItem';
import {
  getCurrentTableId,
  storeTableSession,
  getTableSession,
  clearTableSession,
  clearCollaborativeSession,
  shouldClearSessionForNewVisit
} from '@/services/tableSessionService';
import { getRestaurantId } from '@/services/restaurantDbService';
import { recordTrafficEvent } from '@/services/trafficService';

interface CollaborativeMenuProps {
  className?: string;
}

export const CollaborativeMenuComplete: React.FC<CollaborativeMenuProps> = ({
  className = ""
}) => {
  // Core state
  const [menuItems, setMenuItems] = useState<MenuItemType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [tableId, setTableId] = useState<string>('');
  const [customerSessionId] = useState(() => generateSessionId());
  const [useCollaborativeCart, setUseCollaborativeCart] = useState(false);
  const [activeTab, setActiveTab] = useState<'all' | 'food' | 'drinks'>('all');

  // Contexts and routing
  const { restaurantInfo } = useRestaurant();
  const { t } = useLanguage();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  // Collaborative features
  const sharedCart = useSharedCart({
    tableId: tableId || '',
    customerSessionId,
    restaurantId: restaurantInfo?.id || '',
    enabled: useCollaborativeCart && !!tableId && !!restaurantInfo?.id
  });

  const tableParticipants = useTableParticipantsEnhanced({
    tableId: tableId || '',
    customerSessionId,
    enabled: useCollaborativeCart && !!tableId
  });

  // Load menu items and handle table setup
  const loadMenuItems = useCallback(async () => {
    try {
      setIsLoading(true);

      // Get table ID from URL
      let currentTableId = searchParams.get('table');

      // Handle session management
      if (!currentTableId) {
        const session = getTableSession();
        if (session && session.isValid) {
          currentTableId = session.tableId;

        } else if (session && !session.isValid) {
          clearTableSession();

          toast.info('Please scan the QR code at your table again - your previous session has expired.');
        }
      } else {
        // Handle new table session
        const currentSession = getTableSession(false);
        const restaurantId = await getRestaurantId();

        if (restaurantId) {
          // Check for session cleanup
          if (shouldClearSessionForNewVisit(currentTableId, restaurantId)) {

            clearCollaborativeSession();
            toast.info('Welcome! Starting fresh for your new table visit.');
          }

          // Store new session
          storeTableSession(currentTableId, restaurantId);

          // Record QR scan event
          try {
            await recordTrafficEvent('qr_scan', restaurantId);

          } catch (e) {
            console.error('Error recording QR scan event:', e);
          }
        }
      }

      // Validate table access
      if (!currentTableId) {
        toast.error('Please scan a restaurant QR code to view the menu');
        navigate('/');
        return;
      }

      // Enable collaborative features
      setTableId(currentTableId);
      setUseCollaborativeCart(true);

      // Load menu items

      const items = await fetchMenuItems(currentTableId);
      setMenuItems(items);

    } catch (error) {
      console.error('Error loading menu items:', error);
      toast.error('Failed to load menu items. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  }, [searchParams, navigate, restaurantInfo?.id]);

  // Initialize on mount
  useEffect(() => {
    loadMenuItems();
  }, [loadMenuItems]);

  // Record menu view event
  useEffect(() => {
    if (restaurantInfo?.id) {
      recordTrafficEvent('menu_view', restaurantInfo.id);
    }
  }, [restaurantInfo?.id]);

  // Group menu items by category
  const drinksItems = menuItems.filter(item => {
    const cat = item.category?.toLowerCase() || '';
    return cat.includes('drink') || cat.includes('beverage');
  });

  const foodItems = menuItems.filter(item => {
    const cat = item.category?.toLowerCase() || '';
    return !cat.includes('drink') && !cat.includes('beverage');
  });

  // Get filtered items based on active tab
  const getFilteredItems = () => {
    switch (activeTab) {
      case 'food': return foodItems;
      case 'drinks': return drinksItems;
      default: return menuItems;
    }
  };

  // Handle adding items to cart
  const handleAddToCart = useCallback(async (item: MenuItemType) => {
    if (useCollaborativeCart && sharedCart) {
      try {
        await sharedCart.addItem(item, 1);
        toast.success(`Added ${item.name} to shared cart`);
      } catch (error) {
        console.error('Error adding item to shared cart:', error);
        toast.error('Failed to add item to shared cart');
      }
    } else {
      // Fallback for individual cart (if needed)
      toast.info('Collaborative cart not available');
    }
  }, [useCollaborativeCart, sharedCart]);

  // Handle checkout navigation
  const handleCheckout = useCallback(() => {
    if (!tableId) {
      toast.error('Table ID not found');
      return;
    }

    // Navigate to checkout with table parameter
    navigate(`/order-confirmation?table=${tableId}&mode=collaborative`);
  }, [tableId, navigate]);

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <Card className="max-w-md mx-auto">
            <CardContent className="p-8 text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Loading Menu</h3>
              <p className="text-gray-600">Setting up your collaborative dining experience...</p>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Error state - no table ID
  if (!tableId) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <Card className="max-w-md mx-auto border-orange-200 bg-orange-50">
            <CardContent className="p-8 text-center">
              <QrCode className="h-12 w-12 mx-auto mb-4 text-orange-600" />
              <h3 className="text-lg font-semibold mb-2 text-orange-800">Table Required</h3>
              <p className="text-orange-700 mb-4">
                Please scan the QR code at your restaurant table to access the menu and start collaborative ordering.
              </p>
              <Button onClick={() => navigate('/')} variant="outline">
                Go to Home
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 ${className}`}>
      <Header />

      <div className="container mx-auto px-4 py-6 space-y-6">
        {/* Header with table info and participants */}
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-2">
                  <QrCode className="h-5 w-5 text-blue-600" />
                  <span className="text-sm text-gray-600">Table</span>
                  <Badge variant="outline">{tableId.slice(-8)}</Badge>
                </div>

                {restaurantInfo && (
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600">at</span>
                    <span className="font-medium">{restaurantInfo.name}</span>
                  </div>
                )}
              </div>

              <div className="flex items-center space-x-2">
                {/* Participants count */}
                {tableParticipants.participantCount > 0 && (
                  <div className="flex items-center space-x-1">
                    <Users className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium">
                      {tableParticipants.participantCount} participant{tableParticipants.participantCount !== 1 ? 's' : ''}
                    </span>
                  </div>
                )}

                {/* Cart summary */}
                {sharedCart.cartItems.length > 0 && (
                  <div className="flex items-center space-x-1">
                    <ShoppingCart className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium">
                      {sharedCart.cartItems.reduce((sum, item) => sum + item.quantity, 0)} items
                    </span>
                  </div>
                )}
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Main content grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Menu content - takes up 2 columns on large screens */}
          <div className="lg:col-span-2 space-y-6">
            {/* Chef's Recommendations */}
            <ChefRecommendationsComplete
              mode="customer"
              onAddToCart={(menuItemId) => {
                const item = menuItems.find(m => m.id === menuItemId);
                if (item) handleAddToCart(item);
              }}
            />

            {/* Menu sections with tabs */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MenuIcon className="h-5 w-5" />
                  Menu
                </CardTitle>

                <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)}>
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="all">
                      All Items ({menuItems.length})
                    </TabsTrigger>
                    <TabsTrigger value="food">
                      Food ({foodItems.length})
                    </TabsTrigger>
                    <TabsTrigger value="drinks">
                      Drinks ({drinksItems.length})
                    </TabsTrigger>
                  </TabsList>
                </Tabs>
              </CardHeader>

              <CardContent>
                <AnimatePresence mode="wait">
                  <motion.div
                    key={activeTab}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.2 }}
                  >
                    {getFilteredItems().length > 0 ? (
                      <MenuSection
                        title=""
                        items={getFilteredItems()}
                        onAddToCart={handleAddToCart}
                      />
                    ) : (
                      <div className="text-center py-8">
                        <MenuIcon className="h-12 w-12 mx-auto text-gray-400 mb-3" />
                        <h3 className="text-lg font-medium text-gray-900 mb-1">
                          No items found
                        </h3>
                        <p className="text-gray-500">
                          No items available in this category at the moment.
                        </p>
                      </div>
                    )}
                  </motion.div>
                </AnimatePresence>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar - cart and participants */}
          <div className="space-y-6">
            {/* Collaborative Cart */}
            <SmartCollaborativeCartComplete
              tableId={tableId}
              restaurantId={restaurantInfo?.id || ''}
              onCheckout={handleCheckout}
              onAddItem={handleAddToCart}
            />

            {/* Table Participants */}
            {tableParticipants.participants.length > 0 && (
              <TableParticipants
                participants={tableParticipants.participants}
                customerSessionId={customerSessionId}
              />
            )}

            {/* Connection Status */}
            <Card className="bg-gray-50">
              <CardContent className="p-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Status:</span>
                  <div className="flex items-center space-x-2">
                    {sharedCart.isLoading ? (
                      <>
                        <RefreshCw className="h-3 w-3 animate-spin text-blue-500" />
                        <span className="text-blue-600">Connecting...</span>
                      </>
                    ) : sharedCart.cart ? (
                      <>
                        <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                        <span className="text-green-600">Connected</span>
                      </>
                    ) : (
                      <>
                        <AlertCircle className="h-3 w-3 text-orange-500" />
                        <span className="text-orange-600">Limited Mode</span>
                      </>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
      
    </div>
  );
};

export default CollaborativeMenuComplete;