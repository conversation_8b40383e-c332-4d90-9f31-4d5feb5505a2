
import React from 'react';
import MenuItem, { MenuItemType } from './MenuItem';

interface MenuSectionProps {
  title: string;
  items: MenuItemType[];
  onAddToCart: (item: MenuItemType) => void;
}

const MenuSection: React.FC<MenuSectionProps> = ({ title, items, onAddToCart }) => {
  return (
    <div className="mb-8">
      <h2 className="text-2xl font-bold mb-4 px-4">{title}</h2>
      <div className="menu-card">
        {items.map(item => (
          <MenuItem key={item.id} item={item} onAddToCart={onAddToCart} />
        ))}
      </div>
    </div>
  );
};

export default MenuSection;
