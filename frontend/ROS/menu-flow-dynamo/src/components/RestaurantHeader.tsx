import React, { useState, useEffect } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { useRestaurant } from '@/contexts/RestaurantContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { supabase } from '@/integrations/supabase/client';
import { Clock, MapPin, Star } from 'lucide-react';

interface RestaurantHeaderProps {
  className?: string;
}

interface BusinessData {
  name: string;
  logo_url?: string;
  theme_colors?: any;
  brand_settings?: any;
}

const RestaurantHeader: React.FC<RestaurantHeaderProps> = ({ className = "" }) => {
  const { themeColors, brandSettings, isLoading: themeLoading } = useTheme();
  const { restaurantInfo } = useRestaurant();
  const { t } = useLanguage();
  const [businessData, setBusinessData] = useState<BusinessData | null>(null);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [isLoading, setIsLoading] = useState(true);
  const [logoError, setLogoError] = useState(false);
  const [logoLoading, setLogoLoading] = useState(true);

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000); // Update every minute

    return () => clearInterval(timer);
  }, []);

  // Load business data
  useEffect(() => {
    const loadBusinessData = async () => {
      if (!restaurantInfo?.id) {
        setIsLoading(false);
        return;
      }

      try {
        // Since restaurantInfo.id is now always a business ID, query directly
        const { data: business, error: businessError } = await supabase
          .from('businesses')
          .select('name, logo_url, theme_colors, brand_settings')
          .eq('id', restaurantInfo.id)
          .maybeSingle();

        if (businessError || !business) {
          console.error('Error fetching business data:', businessError);
          setIsLoading(false);
          return;
        }

        setBusinessData(business);
      } catch (error) {
        console.error('Error loading business data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadBusinessData();
  }, [restaurantInfo?.id]);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    });
  };

  const getTimeBasedGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return "Good Morning";
    if (hour < 17) return "Good Afternoon";
    return "Good Evening";
  };

  if (isLoading || themeLoading) {
    return (
      <div className="relative overflow-hidden rounded-lg p-3 mb-3 animate-pulse">
        <div 
          className="absolute inset-0 bg-gradient-to-br from-slate-800 to-slate-900"
          style={{
            background: `linear-gradient(135deg, ${themeColors.background}E6, ${themeColors.card})`
          }}
        />
        <div className="relative z-10">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gray-300 rounded-lg animate-pulse" />
              <div>
                <div className="h-4 bg-gray-300 rounded w-24 mb-1 animate-pulse" />
                <div className="h-3 bg-gray-300 rounded w-20 animate-pulse" />
              </div>
            </div>
            <div className="text-right">
              <div className="h-3 bg-gray-300 rounded w-16 mb-1 animate-pulse" />
              <div className="h-2 bg-gray-300 rounded w-12 animate-pulse" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative overflow-hidden rounded-lg p-3 mb-3 ${className}`}>
      {/* Animated Background */}
      <div 
        className="absolute inset-0 bg-gradient-to-br opacity-95"
        style={{
          background: `linear-gradient(135deg, ${themeColors.background}E6, ${themeColors.card})`
        }}
      />
      
      {/* Glass morphism overlay */}
      <div className="absolute inset-0 backdrop-blur-sm bg-white/5" />

      {/* Content */}
      <div className="relative z-10">
        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3">
          {/* Restaurant Info */}
          <div className="flex items-center space-x-3 flex-1 min-w-0">
            {/* Logo */}
            <div className="relative">
              {businessData?.logo_url && !logoError ? (
                <div className="relative w-20 h-20">
                  {logoLoading && (
                    <div
                      className="absolute inset-0 rounded-lg flex items-center justify-center shadow-md ring-1 ring-white/20 animate-pulse"
                      style={{ backgroundColor: `${themeColors.primary}40` }}
                    >
                      <div className="w-6 h-6 rounded-full border-2 border-white/30 border-t-white animate-spin" />
                    </div>
                  )}
                  <img
                    src={businessData.logo_url}
                    alt={`${businessData.name} logo`}
                    className={`w-20 h-20 rounded-lg object-contain shadow-md ring-1 ring-white/20 transition-opacity duration-300 ${logoLoading ? 'opacity-0' : 'opacity-100'} bg-white`}
                    onLoad={() => setLogoLoading(false)}
                    onError={() => {
                      setLogoError(true);
                      setLogoLoading(false);
                    }}
                  />
                </div>
              ) : (
                <div
                  className="w-20 h-20 rounded-lg flex items-center justify-center shadow-md ring-1 ring-white/20"
                  style={{ backgroundColor: themeColors.primary }}
                >
                  <span
                    className="text-sm font-bold"
                    style={{ color: 'white' }}
                  >
                    {businessData?.name?.charAt(0) || 'R'}
                  </span>
                </div>
              )}
              
              {/* Status indicator */}
              <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border border-white shadow-sm">
                <div className="w-full h-full bg-green-500 rounded-full animate-pulse" />
              </div>
            </div>

            {/* Restaurant Details */}
            <div className="flex-1 min-w-0">
              <h1
                className="text-sm sm:text-base font-bold mb-1 truncate"
                style={{ color: themeColors.text }}
              >
                {businessData?.name || 'Restaurant'}
              </h1>

              <div className="flex flex-wrap items-center gap-1.5 text-xs">
                <div className="flex items-center space-x-1 px-1.5 py-0.5 rounded-full" style={{ backgroundColor: `${themeColors.secondary}20` }}>
                  <MapPin className="w-2.5 h-2.5" style={{ color: themeColors.secondary }} />
                  <span style={{ color: themeColors.text }} className="opacity-90 font-medium">
                    {t('dineInAvailable')}
                  </span>
                </div>

                <div className="flex items-center space-x-1 px-1.5 py-0.5 rounded-full" style={{ backgroundColor: `${themeColors.accent}20` }}>
                  <Star className="w-2.5 h-2.5 fill-current" style={{ color: themeColors.accent }} />
                  <span style={{ color: themeColors.text }} className="opacity-90 font-medium">
                    Premium
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Time and Status */}
          <div className="text-left sm:text-right sm:flex-shrink-0">
            <div className="flex items-center space-x-1.5 mb-1 sm:justify-end justify-start">
              <Clock className="w-3 h-3" style={{ color: themeColors.secondary }} />
              <span
                className="text-sm font-medium"
                style={{ color: themeColors.text }}
              >
                {formatTime(currentTime)}
              </span>
            </div>

            <div className="flex items-center sm:justify-end justify-start space-x-1.5">
              <div className="flex items-center space-x-1 px-2 py-0.5 rounded-full bg-green-500/20 border border-green-500/30">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse" />
                <span
                  className="text-xs font-medium text-green-700"
                >
                  {t('openNow')}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RestaurantHeader; 