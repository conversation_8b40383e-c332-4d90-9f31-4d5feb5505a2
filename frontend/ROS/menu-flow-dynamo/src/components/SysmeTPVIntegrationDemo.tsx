import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { toast } from 'sonner';
import { 
  Wifi, 
  WifiOff, 
  Database, 
  Receipt, 
  Menu as MenuIcon, 
  ShoppingCart,
  Building,
  Settings,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';
import {
  initializeSysmeTPV,
  syncMenuToSysmeTPV,
  pushOrderToSysmeTPV,
  fetchSysmeTPVInventory,
  updateSysmeTPVTableStatus,
  generateSysmeTPVSalesReport,
  syncSysmeTPVInventoryToMenuFlow,
  syncDynamicPricingToSysmeTPV,
  syncAIRecommendationsToSysmeTPV,
  syncTableManagementToSysmeTPV,
  syncAnalyticsInsightsToSysmeTPV,
  syncCompleteMenuFlowToSysmeTPV,
  getSysmeTPVStatus
} from '@/integrations/integrationOrchestrator';
import { MiddlewareMenuItem, MiddlewareOrder, MiddlewareTableStatus } from '@/integrations/middlewareIntegration';

/**
 * Demo component for Sysme TPV integration
 * Shows how MenuFlow can integrate with Sysme TPV for Spanish restaurants
 */
const SysmeTPVIntegrationDemo: React.FC = () => {
  // Connection state
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);

  // Configuration state
  const [config, setConfig] = useState({
    serverUrl: 'http://192.168.1.100:8080',
    apiKey: '',
    restaurantId: 'valencia-restaurant-001',
    fiscalPrinterEnabled: true,
    companyCode: '*********',
    databasePath: ''
  });

  // Demo data state
  const [lastTicketNumber, setLastTicketNumber] = useState<string>('');
  const [lastFiscalReference, setLastFiscalReference] = useState<string>('');
  const [inventoryCount, setInventoryCount] = useState<number>(0);
  const [salesReportData, setSalesReportData] = useState<any>(null);
  const [syncedInventoryItems, setSyncedInventoryItems] = useState<number>(0);
  const [operationsLog, setOperationsLog] = useState<string[]>([]);

  // Sample menu items for demo
  const sampleMenuItems: MiddlewareMenuItem[] = [
    {
      id: 'item-001',
      name: 'Paella Valenciana',
      description: 'Arroz con pollo, conejo, judías verdes y azafrán',
      price: 18.50,
      category: 'platos_principales',
      available: true,
      dynamicPricing: false
    },
    {
      id: 'item-002',
      name: 'Sangría',
      description: 'Sangría tradicional española',
      price: 4.50,
      category: 'bebidas',
      available: true,
      dynamicPricing: false
    },
    {
      id: 'item-003',
      name: 'Tortilla Española',
      description: 'Tortilla de patatas casera',
      price: 8.90,
      category: 'tapas',
      available: true,
      dynamicPricing: false
    }
  ];

  // Sample order for demo
  const sampleOrder: MiddlewareOrder = {
    id: `order-${Date.now()}`,
    tableId: 'mesa-05',
    items: [
      {
        id: 'item-001',
        name: 'Paella Valenciana',
        price: 18.50,
        quantity: 2,
        notes: 'Sin marisco por alergia'
      },
      {
        id: 'item-002',
        name: 'Sangría',
        price: 4.50,
        quantity: 2
      }
    ],
    totalAmount: 46.00,
    createdAt: new Date().toISOString(),
    customerName: 'Mesa 5',
    specialInstructions: 'Cliente con alergia al marisco'
  };

  const addToLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setOperationsLog(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 9)]);
  };

  const handleConnect = async () => {
    setIsConnecting(true);
    setConnectionError(null);
    addToLog('Iniciando conexión con Sysme TPV...');

    try {
      const success = await initializeSysmeTPV(config);
      
      if (success) {
        setIsConnected(true);
        addToLog('✅ Conexión establecida con Sysme TPV');
        toast.success('Conectado a Sysme TPV');
      } else {
        setConnectionError('No se pudo conectar a Sysme TPV. Verificar configuración.');
        addToLog('❌ Error en la conexión con Sysme TPV');
        toast.error('Error de conexión con Sysme TPV');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
      setConnectionError(errorMessage);
      addToLog(`❌ Error: ${errorMessage}`);
      toast.error('Error de conexión');
    } finally {
      setIsConnecting(false);
    }
  };

  const handleSyncMenu = async () => {
    if (!isConnected) {
      toast.error('Debe conectarse primero a Sysme TPV');
      return;
    }

    addToLog('Sincronizando carta con Sysme TPV...');
    
    try {
      const result = await syncMenuToSysmeTPV(sampleMenuItems);
      
      if (result.success) {
        addToLog(`✅ Carta sincronizada: ${sampleMenuItems.length} artículos`);
        toast.success('Carta sincronizada con Sysme TPV');
      } else {
        addToLog(`❌ Error en sincronización: ${result.error}`);
        toast.error('Error al sincronizar carta');
      }
    } catch (error) {
      addToLog(`❌ Error: ${error instanceof Error ? error.message : 'Error desconocido'}`);
      toast.error('Error en sincronización de carta');
    }
  };

  const handlePushOrder = async () => {
    if (!isConnected) {
      toast.error('Debe conectarse primero a Sysme TPV');
      return;
    }

    addToLog(`Enviando pedido ${sampleOrder.id} a Sysme TPV...`);
    
    try {
      const result = await pushOrderToSysmeTPV(sampleOrder);
      
      if (result.success) {
        setLastTicketNumber(result.ticketNumber || '');
        setLastFiscalReference(result.fiscalReference || '');
        addToLog(`✅ Pedido enviado - Ticket: ${result.ticketNumber}`);
        toast.success(`Pedido enviado - Ticket: ${result.ticketNumber}`);
      } else {
        addToLog(`❌ Error enviando pedido: ${result.error}`);
        toast.error('Error al enviar pedido');
      }
    } catch (error) {
      addToLog(`❌ Error: ${error instanceof Error ? error.message : 'Error desconocido'}`);
      toast.error('Error al enviar pedido');
    }
  };

  const handleFetchInventory = async () => {
    if (!isConnected) {
      toast.error('Debe conectarse primero a Sysme TPV');
      return;
    }

    addToLog('Obteniendo datos de inventario...');
    
    try {
      const result = await fetchSysmeTPVInventory();
      
      if (result.success) {
        const count = result.inventory?.length || 0;
        setInventoryCount(count);
        addToLog(`✅ Inventario obtenido: ${count} artículos`);
        toast.success(`Inventario: ${count} artículos`);
      } else {
        addToLog(`❌ Error obteniendo inventario: ${result.error}`);
        toast.error('Error al obtener inventario');
      }
    } catch (error) {
      addToLog(`❌ Error: ${error instanceof Error ? error.message : 'Error desconocido'}`);
      toast.error('Error al obtener inventario');
    }
  };

  const handleUpdateTable = async () => {
    if (!isConnected) {
      toast.error('Debe conectarse primero a Sysme TPV');
      return;
    }

    const tableStatus: MiddlewareTableStatus = {
      tableId: 'mesa-05',
      status: 'occupied',
      occupiedSince: new Date().toISOString(),
      occupantCount: 4
    };

    addToLog('Actualizando estado de mesa...');
    
    try {
      const result = await updateSysmeTPVTableStatus(tableStatus);
      
      if (result.success) {
        addToLog('✅ Estado de mesa actualizado');
        toast.success('Estado de mesa actualizado');
      } else {
        addToLog(`❌ Error actualizando mesa: ${result.error}`);
        toast.error('Error al actualizar mesa');
      }
    } catch (error) {
      addToLog(`❌ Error: ${error instanceof Error ? error.message : 'Error desconocido'}`);
      toast.error('Error al actualizar mesa');
    }
  };

  const handleGenerateSalesReport = async () => {
    if (!isConnected) {
      toast.error('Debe conectarse primero a Sysme TPV');
      return;
    }

    const endDate = new Date().toISOString().split('T')[0];
    const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    addToLog(`Generando informe de ventas (${startDate} a ${endDate})...`);
    
    try {
      const result = await generateSysmeTPVSalesReport(startDate, endDate);
      
      if (result.success && result.report) {
        setSalesReportData(result.report);
        addToLog(`✅ Informe generado - Ventas: €${result.report.totalSales || 0}`);
        toast.success(`Informe generado - Ventas: €${result.report.totalSales || 0}`);
      } else {
        addToLog(`❌ Error generando informe: ${result.error}`);
        toast.error('Error al generar informe');
      }
    } catch (error) {
      addToLog(`❌ Error: ${error instanceof Error ? error.message : 'Error desconocido'}`);
      toast.error('Error al generar informe');
    }
  };

  const handleSyncInventoryToMenuFlow = async () => {
    if (!isConnected) {
      toast.error('Debe conectarse primero a Sysme TPV');
      return;
    }

    addToLog('Sincronizando inventario a MenuFlow...');
    
    try {
      const result = await syncSysmeTPVInventoryToMenuFlow();
      
      if (result.success) {
        const itemCount = result.syncedItems || 0;
        setSyncedInventoryItems(itemCount);
        addToLog(`✅ Inventario sincronizado: ${itemCount} artículos`);
        toast.success(`Inventario sincronizado: ${itemCount} artículos`);
      } else {
        addToLog(`❌ Error sincronizando inventario: ${result.error}`);
        toast.error('Error al sincronizar inventario');
      }
    } catch (error) {
      addToLog(`❌ Error: ${error instanceof Error ? error.message : 'Error desconocido'}`);
      toast.error('Error al sincronizar inventario');
    }
  };

  const handleSyncDynamicPricing = async () => {
    if (!isConnected) {
      toast.error('Debe conectarse primero a Sysme TPV');
      return;
    }

    // Sample dynamic pricing data
    const pricingData = [
      {
        itemId: 'item-001',
        basePrice: 18.50,
        currentPrice: 20.35, // 10% increase due to high demand
        priceMultiplier: 1.1,
        demandLevel: 'high' as const,
        timeBasedAdjustment: 0.85,
        aiRecommendedPrice: 19.90
      },
      {
        itemId: 'item-002',
        basePrice: 4.50,
        currentPrice: 4.50, // No change for beverages
        priceMultiplier: 1.0,
        demandLevel: 'medium' as const,
        timeBasedAdjustment: 0,
        aiRecommendedPrice: 4.50
      }
    ];

    addToLog('Sincronizando precios dinámicos...');
    
    try {
      const result = await syncDynamicPricingToSysmeTPV(pricingData);
      
      if (result.success) {
        addToLog(`✅ Precios actualizados: ${result.updatedItems} artículos`);
        toast.success(`Precios dinámicos sincronizados: ${result.updatedItems} artículos`);
      } else {
        addToLog(`❌ Error sincronizando precios: ${result.error}`);
        toast.error('Error al sincronizar precios dinámicos');
      }
    } catch (error) {
      addToLog(`❌ Error: ${error instanceof Error ? error.message : 'Error desconocido'}`);
      toast.error('Error al sincronizar precios dinámicos');
    }
  };

  const handleSyncAIRecommendations = async () => {
    if (!isConnected) {
      toast.error('Debe conectarse primero a Sysme TPV');
      return;
    }

    // Sample AI recommendations data
    const aiRecommendations = [
      {
        itemId: 'item-001',
        recommendationType: 'chef' as const,
        confidence: 0.92,
        predictedDemand: 150,
        suggestedPromotion: 'Plato recomendado por el chef',
        optimalTime: '19:00-21:00',
        targetAudience: 'familias'
      },
      {
        itemId: 'item-003',
        recommendationType: 'demand' as const,
        confidence: 0.85,
        predictedDemand: 89,
        suggestedPromotion: 'Popular entre nuestros clientes',
        optimalTime: '12:00-15:00',
        targetAudience: 'trabajadores'
      }
    ];

    addToLog('Sincronizando recomendaciones de IA...');
    
    try {
      const result = await syncAIRecommendationsToSysmeTPV(aiRecommendations);
      
      if (result.success) {
        addToLog(`✅ IA sincronizada: ${result.syncedRecommendations} recomendaciones`);
        toast.success(`Recomendaciones IA sincronizadas: ${result.syncedRecommendations}`);
      } else {
        addToLog(`❌ Error sincronizando IA: ${result.error}`);
        toast.error('Error al sincronizar recomendaciones IA');
      }
    } catch (error) {
      addToLog(`❌ Error: ${error instanceof Error ? error.message : 'Error desconocido'}`);
      toast.error('Error al sincronizar recomendaciones IA');
    }
  };

  const handleSyncAllSystems = async () => {
    if (!isConnected) {
      toast.error('Debe conectarse primero a Sysme TPV');
      return;
    }

    addToLog('🚀 Iniciando sincronización completa de todos los sistemas...');
    
    try {
      const syncData = {
        menu: sampleMenuItems,
        orders: [sampleOrder],
        dynamicPricing: [
          {
            itemId: 'item-001',
            basePrice: 18.50,
            currentPrice: 20.35,
            priceMultiplier: 1.1,
            demandLevel: 'high' as const,
            timeBasedAdjustment: 0.85,
            aiRecommendedPrice: 19.90
          }
        ],
        aiRecommendations: [
          {
            itemId: 'item-001',
            recommendationType: 'chef' as const,
            confidence: 0.92,
            predictedDemand: 150,
            suggestedPromotion: 'Plato recomendado por el chef',
            optimalTime: '19:00-21:00',
            targetAudience: 'familias'
          }
        ],
        tableManagement: [
          {
            tableId: 'mesa-05',
            tableNumber: '5',
            status: 'occupied' as const,
            capacity: 4,
            currentOccupancy: 3,
            reservationId: 'res-123',
            reservationTime: '19:30',
            estimatedTurnover: 90,
            serviceQuality: 4.5
          }
        ],
        analytics: {
          period: '2025-06-01_2025-06-30',
          totalRevenue: 12500.75,
          orderCount: 245,
          averageOrderValue: 51.02,
          customerSatisfaction: 4.6,
          peakHours: ['12:00-14:00', '19:00-21:00'],
          topPerformingItems: [
            { itemId: 'item-001', revenue: 3500.50, orders: 89 },
            { itemId: 'item-002', revenue: 1800.00, orders: 245 }
          ],
          customerBehavior: {
            averageSessionTime: 1800, // 30 minutes
            bounceRate: 0.12,
            conversionRate: 0.78,
            repeatCustomerRate: 0.34
          },
          operationalEfficiency: {
            averagePreparationTime: 18.5,
            kitchenEfficiency: 0.89,
            tableUtilization: 0.73,
            staffPerformance: 4.2
          }
        }
      };

      const result = await syncCompleteMenuFlowToSysmeTPV(syncData);
      
      if (result.success) {
        addToLog('✅ Sincronización completa exitosa');
        addToLog(`📊 Sistemas sincronizados: ${Object.keys(result.results).length}`);
        toast.success('Sincronización completa de todos los sistemas exitosa');
      } else {
        addToLog(`⚠️ Sincronización completada con ${result.errors?.length || 0} errores`);
        result.errors?.forEach(error => addToLog(`❌ ${error}`));
        toast.warning(`Sincronización completada con algunos errores`);
      }
    } catch (error) {
      addToLog(`❌ Error: ${error instanceof Error ? error.message : 'Error desconocido'}`);
      toast.error('Error en sincronización completa');
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Integración Sysme TPV
        </h1>
        <p className="text-gray-600">
          Demostración de integración entre MenuFlow y Sysme TPV para restaurantes españoles
        </p>
      </div>

      {/* Connection Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {isConnected ? (
              <Wifi className="h-5 w-5 text-green-500" />
            ) : (
              <WifiOff className="h-5 w-5 text-red-500" />
            )}
            Estado de Conexión
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Badge variant={isConnected ? "default" : "destructive"}>
                {isConnected ? "Conectado" : "Desconectado"}
              </Badge>
              {lastTicketNumber && (
                <Badge variant="outline">
                  Último Ticket: {lastTicketNumber}
                </Badge>
              )}
              {lastFiscalReference && (
                <Badge variant="outline">
                  Ref. Fiscal: {lastFiscalReference}
                </Badge>
              )}
            </div>
            <Button 
              onClick={handleConnect} 
              disabled={isConnecting}
              variant={isConnected ? "outline" : "default"}
            >
              {isConnecting ? (
                <><Loader2 className="h-4 w-4 mr-2 animate-spin" /> Conectando...</>
              ) : (
                <>Conectar a Sysme TPV</>
              )}
            </Button>
          </div>
          {connectionError && (
            <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
              {connectionError}
            </div>
          )}
        </CardContent>
      </Card>

      <div className="grid md:grid-cols-2 gap-6">
        {/* Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Configuración
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="serverUrl">URL del Servidor Sysme TPV</Label>
              <Input
                id="serverUrl"
                value={config.serverUrl}
                onChange={(e) => setConfig(prev => ({ ...prev, serverUrl: e.target.value }))}
                placeholder="http://192.168.1.100:8080"
              />
            </div>
            
            <div>
              <Label htmlFor="restaurantId">ID del Restaurante</Label>
              <Input
                id="restaurantId"
                value={config.restaurantId}
                onChange={(e) => setConfig(prev => ({ ...prev, restaurantId: e.target.value }))}
                placeholder="valencia-restaurant-001"
              />
            </div>
            
            <div>
              <Label htmlFor="companyCode">Código Fiscal (CIF/NIF)</Label>
              <Input
                id="companyCode"
                value={config.companyCode}
                onChange={(e) => setConfig(prev => ({ ...prev, companyCode: e.target.value }))}
                placeholder="*********"
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch
                id="fiscalPrinter"
                checked={config.fiscalPrinterEnabled}
                onCheckedChange={(checked) => setConfig(prev => ({ ...prev, fiscalPrinterEnabled: checked }))}
              />
              <Label htmlFor="fiscalPrinter">Impresora Fiscal Habilitada</Label>
            </div>
          </CardContent>
        </Card>

        {/* Operations */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Operaciones
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button 
              onClick={handleSyncMenu} 
              disabled={!isConnected}
              className="w-full justify-start"
              variant="outline"
            >
              <MenuIcon className="h-4 w-4 mr-2" />
              Sincronizar Carta ({sampleMenuItems.length} artículos)
            </Button>
            
            <Button 
              onClick={handlePushOrder} 
              disabled={!isConnected}
              className="w-full justify-start"
              variant="outline"
            >
              <ShoppingCart className="h-4 w-4 mr-2" />
              Enviar Pedido (€{sampleOrder.totalAmount})
            </Button>
            
            <Button 
              onClick={handleFetchInventory} 
              disabled={!isConnected}
              className="w-full justify-start"
              variant="outline"
            >
              <Database className="h-4 w-4 mr-2" />
              Obtener Inventario {inventoryCount > 0 && `(${inventoryCount})`}
            </Button>
            
            <Button 
              onClick={handleUpdateTable} 
              disabled={!isConnected}
              className="w-full justify-start"
              variant="outline"
            >
              <Building className="h-4 w-4 mr-2" />
              Actualizar Mesa 5
            </Button>
            
            <Button 
              onClick={handleGenerateSalesReport} 
              disabled={!isConnected}
              className="w-full justify-start"
              variant="outline"
            >
              <Receipt className="h-4 w-4 mr-2" />
              Generar Informe de Ventas
            </Button>
            
            <Button 
              onClick={handleSyncInventoryToMenuFlow} 
              disabled={!isConnected}
              className="w-full justify-start"
              variant="outline"
            >
              <Database className="h-4 w-4 mr-2" />
              Sincronizar Inventario a MenuFlow
            </Button>
            
            <Button 
              onClick={handleSyncDynamicPricing} 
              disabled={!isConnected}
              className="w-full justify-start"
              variant="outline"
            >
              <Loader2 className="h-4 w-4 mr-2" />
              Sincronizar Precios Dinámicos
            </Button>
            
            <Button 
              onClick={handleSyncAIRecommendations} 
              disabled={!isConnected}
              className="w-full justify-start"
              variant="outline"
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Sincronizar Recomendaciones IA
            </Button>
            
            <Button 
              onClick={handleSyncAllSystems} 
              disabled={!isConnected}
              className="w-full justify-start bg-blue-600 hover:bg-blue-700 text-white"
            >
              <Database className="h-4 w-4 mr-2" />
              Sincronización Completa
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Sample Data */}
      <div className="grid md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Carta de Ejemplo</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {sampleMenuItems.map((item) => (
                <div key={item.id} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                  <div>
                    <p className="font-medium">{item.name}</p>
                    <p className="text-sm text-gray-600">{item.category}</p>
                  </div>
                  <Badge variant="outline">€{item.price}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Pedido de Ejemplo</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="font-medium">Mesa:</span>
                <span>{sampleOrder.tableId}</span>
              </div>
              <div className="border-t pt-2">
                {sampleOrder.items.map((item, index) => (
                  <div key={index} className="flex justify-between items-center">
                    <span>{item.quantity}× {item.name}</span>
                    <span>€{(item.price * item.quantity).toFixed(2)}</span>
                  </div>
                ))}
              </div>
              <div className="border-t pt-2 font-bold flex justify-between">
                <span>Total:</span>
                <span>€{sampleOrder.totalAmount}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Sales Report */}
      {salesReportData && (
        <Card>
          <CardHeader>
            <CardTitle>Informe de Ventas (Últimos 30 días)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-4">
              <div className="bg-blue-50 p-4 rounded">
                <h4 className="font-semibold text-blue-700">Ventas Totales</h4>
                <p className="text-2xl font-bold text-blue-900">€{salesReportData.totalSales}</p>
                <p className="text-sm text-blue-600">{salesReportData.orderCount} pedidos</p>
              </div>
              <div className="bg-green-50 p-4 rounded">
                <h4 className="font-semibold text-green-700">Ticket Promedio</h4>
                <p className="text-2xl font-bold text-green-900">€{salesReportData.averageOrderValue}</p>
                <p className="text-sm text-green-600">Por pedido</p>
              </div>
              <div className="bg-purple-50 p-4 rounded">
                <h4 className="font-semibold text-purple-700">IVA Total</h4>
                <p className="text-2xl font-bold text-purple-900">€{salesReportData.fiscal?.totalIVA}</p>
                <p className="text-sm text-purple-600">{salesReportData.fiscal?.ticketsGenerated} tickets</p>
              </div>
            </div>
            
            {salesReportData.topProducts && (
              <div className="mt-4">
                <h4 className="font-semibold mb-2">Productos Más Vendidos</h4>
                <div className="space-y-2">
                  {salesReportData.topProducts.map((product: any, index: number) => (
                    <div key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                      <div>
                        <span className="font-medium">{product.name}</span>
                        <span className="text-sm text-gray-600 ml-2">({product.count} vendidos)</span>
                      </div>
                      <Badge variant="outline">€{product.sales}</Badge>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Inventory Sync Status */}
      {syncedInventoryItems > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Estado de Sincronización de Inventario</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between p-4 bg-green-50 rounded">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="font-medium">Inventario sincronizado con MenuFlow</span>
              </div>
              <Badge variant="outline">{syncedInventoryItems} artículos</Badge>
            </div>
            <p className="text-sm text-gray-600 mt-2">
              Los datos de inventario de Sysme TPV están ahora disponibles en MenuFlow Analytics
              para análisis avanzado de costos, márgenes y rendimiento de productos.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Operations Log */}
      <Card>
        <CardHeader>
          <CardTitle>Registro de Operaciones</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-gray-50 rounded p-4 h-48 overflow-y-auto">
            {operationsLog.length === 0 ? (
              <p className="text-gray-500 text-center">No hay operaciones registradas</p>
            ) : (
              <div className="space-y-1">
                {operationsLog.map((log, index) => (
                  <div key={index} className="text-sm font-mono">
                    {log}
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Integration Benefits */}
      <Card>
        <CardHeader>
          <CardTitle>Beneficios de la Integración</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-semibold flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                Para MenuFlow
              </h4>
              <ul className="text-sm space-y-1 ml-6">
                <li>• Cumplimiento fiscal automático en España</li>
                <li>• Sincronización de inventario en tiempo real</li>
                <li>• Integración con TPV existente</li>
                <li>• Gestión unificada de pedidos</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                Para Sysme TPV
              </h4>
              <ul className="text-sm space-y-1 ml-6">
                <li>• Pedidos online automáticos</li>
                <li>• Menús digitales actualizados</li>
                <li>• Analytics avanzados de MenuFlow</li>
                <li>• Mejor experiencia del cliente</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SysmeTPVIntegrationDemo;