import React from 'react';
import { useFeatureAccess } from '@/hooks/useFeatureAccess';
import { useSubscription } from '@/hooks/useSubscription';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Lock, 
  Crown, 
  Zap, 
  ArrowRight,
  Clock,
  AlertTriangle 
} from 'lucide-react';

interface FeatureGateProps {
  feature: keyof ReturnType<typeof useFeatureAccess>;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  upgradePrompt?: boolean;
  inline?: boolean;
  title?: string;
  description?: string;
}

export const FeatureGate: React.FC<FeatureGateProps> = ({
  feature,
  children,
  fallback,
  upgradePrompt = true,
  inline = false,
  title,
  description,
}) => {
  const featureAccess = useFeatureAccess();
  const { 
    subscription, 
    isTrialing, 
    daysRemainingInTrial, 
    createCheckoutSession,
    isCreatingCheckout 
  } = useSubscription();
  
  const hasAccess = featureAccess[feature];

  // If user has access, render children
  if (hasAccess) {
    return <>{children}</>;
  }

  // If fallback is provided and no upgrade prompt needed, show fallback
  if (fallback && !upgradePrompt) {
    return <>{fallback}</>;
  }

  // Determine the reason for restriction
  const getRestrictionReason = () => {
    if (!subscription) {
      return {
        type: 'no_subscription',
        title: 'Start Your Free Trial',
        description: 'Sign up for a 7-day free trial to access this feature',
        icon: Clock,
        action: 'Start Free Trial',
        variant: 'blue'
      };
    }

    if (isTrialing) {
      // Feature might be enterprise-only
      const enterpriseFeatures = [
        'canUseDynamicPricing',
        'canAccessAdvancedAnalytics',
        'canManageMultipleRestaurants',
        'canIntegratePOS',
        'canAccessSentimentAnalysis',
        'canUseTrafficAnalytics'
      ];

      if (enterpriseFeatures.includes(feature)) {
        return {
          type: 'enterprise_only',
          title: 'Enterprise Feature',
          description: 'This feature is available in the Enterprise plan',
          icon: Crown,
          action: 'Upgrade to Enterprise',
          variant: 'purple'
        };
      }

      return {
        type: 'trial_limitation',
        title: 'Trial Limitation',
        description: `This feature has limitations during the trial period. ${daysRemainingInTrial} days remaining.`,
        icon: Clock,
        action: 'Subscribe Now',
        variant: 'blue'
      };
    }

    if (subscription.status === 'active' && subscription.plan_id === 'professional') {
      return {
        type: 'enterprise_only',
        title: 'Enterprise Feature',
        description: 'Upgrade to Enterprise for advanced features like dynamic pricing and multi-location management',
        icon: Crown,
        action: 'Upgrade to Enterprise',
        variant: 'purple'
      };
    }

    // Subscription expired or has issues
    return {
      type: 'subscription_expired',
      title: 'Subscription Required',
      description: 'Your subscription has expired. Renew to continue using this feature.',
      icon: AlertTriangle,
      action: 'Renew Subscription',
      variant: 'red'
    };
  };

  const restriction = getRestrictionReason();

  // Handle different pricing for enterprise features
  const handleUpgrade = () => {
    if (restriction.type === 'enterprise_only') {
      createCheckoutSession('price_enterprise'); // Enterprise price ID
    } else {
      createCheckoutSession(); // Professional price ID
    }
  };

  // Inline version for smaller restrictions
  if (inline) {
    return (
      <Alert className="my-2">
        <restriction.icon className="h-4 w-4" />
        <AlertDescription className="flex items-center justify-between">
          <span className="text-sm">
            {title || restriction.title}: {description || restriction.description}
          </span>
          {upgradePrompt && (
            <Button 
              size="sm" 
              variant="outline"
              onClick={handleUpgrade}
              disabled={isCreatingCheckout}
            >
              {isCreatingCheckout ? 'Creating...' : restriction.action}
            </Button>
          )}
        </AlertDescription>
      </Alert>
    );
  }

  // Full card version
  return (
    <Card className="border-dashed border-2 border-gray-300">
      <CardHeader className="text-center pb-4">
        <div className={`mx-auto mb-4 p-3 rounded-full w-fit ${
          restriction.variant === 'blue' ? 'bg-blue-100' :
          restriction.variant === 'purple' ? 'bg-purple-100' :
          restriction.variant === 'red' ? 'bg-red-100' :
          'bg-gray-100'
        }`}>
          <restriction.icon className={`h-6 w-6 ${
            restriction.variant === 'blue' ? 'text-blue-600' :
            restriction.variant === 'purple' ? 'text-purple-600' :
            restriction.variant === 'red' ? 'text-red-600' :
            'text-gray-600'
          }`} />
        </div>
        <CardTitle className="text-lg">
          {title || restriction.title}
        </CardTitle>
        {restriction.type === 'enterprise_only' && (
          <Badge variant="outline" className="w-fit mx-auto mt-2">
            <Crown className="h-3 w-3 mr-1" />
            Enterprise Only
          </Badge>
        )}
      </CardHeader>
      <CardContent className="text-center space-y-4">
        <p className="text-gray-600">
          {description || restriction.description}
        </p>

        {/* Feature benefits for enterprise upgrades */}
        {restriction.type === 'enterprise_only' && (
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 text-left">
            <h4 className="font-medium text-purple-900 mb-2">Enterprise Plan Includes:</h4>
            <ul className="text-sm text-purple-700 space-y-1">
              <li className="flex items-center">
                <Zap className="h-3 w-3 mr-2" />
                AI-powered dynamic pricing
              </li>
              <li className="flex items-center">
                <Zap className="h-3 w-3 mr-2" />
                Advanced analytics & insights
              </li>
              <li className="flex items-center">
                <Zap className="h-3 w-3 mr-2" />
                Multi-location management
              </li>
              <li className="flex items-center">
                <Zap className="h-3 w-3 mr-2" />
                POS system integrations
              </li>
              <li className="flex items-center">
                <Zap className="h-3 w-3 mr-2" />
                Priority support
              </li>
            </ul>
          </div>
        )}

        {/* Pricing information */}
        <div className="border-t pt-4">
          <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
            <span>
              {restriction.type === 'enterprise_only' ? '€99.99/month' : '€39.99/month'}
            </span>
            {restriction.type === 'no_subscription' && (
              <Badge variant="secondary" className="text-xs">
                7-day free trial
              </Badge>
            )}
          </div>
        </div>

        {upgradePrompt && (
          <Button 
            onClick={handleUpgrade}
            disabled={isCreatingCheckout}
            className="w-full"
            size="lg"
          >
            {isCreatingCheckout ? (
              'Creating...'
            ) : (
              <>
                {restriction.action}
                <ArrowRight className="h-4 w-4 ml-2" />
              </>
            )}
          </Button>
        )}

        {/* Alternative action for trial users */}
        {isTrialing && restriction.type === 'enterprise_only' && (
          <p className="text-xs text-gray-500">
            Finish your trial with Professional features, then upgrade anytime
          </p>
        )}
      </CardContent>
    </Card>
  );
};

// Convenience component for specific feature types
export const MenuEditGate: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <FeatureGate 
    feature="canEditMenu" 
    title="Menu Editing"
    description="Edit your menu items, categories, and pricing"
  >
    {children}
  </FeatureGate>
);

export const AnalyticsGate: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <FeatureGate 
    feature="canAccessAnalytics" 
    title="Analytics Dashboard"
    description="View detailed insights about your restaurant performance"
  >
    {children}
  </FeatureGate>
);

export const DynamicPricingGate: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <FeatureGate 
    feature="canUseDynamicPricing" 
    title="Dynamic Pricing"
    description="AI-powered pricing optimization based on demand and traffic"
  >
    {children}
  </FeatureGate>
);

export const MultiLocationGate: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <FeatureGate 
    feature="canManageMultipleRestaurants" 
    title="Multi-Location Management"
    description="Manage multiple restaurant locations from one dashboard"
  >
    {children}
  </FeatureGate>
);