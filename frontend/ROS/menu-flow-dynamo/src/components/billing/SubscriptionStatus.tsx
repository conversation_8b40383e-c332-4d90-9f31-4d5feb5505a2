import React from 'react';
import { useSubscription } from '@/hooks/useSubscription';
import { useFeatureAccess } from '@/hooks/useFeatureAccess';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Crown, 
  Calendar, 
  CreditCard, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  Settings
} from 'lucide-react';

interface SubscriptionStatusProps {
  showUpgradePrompt?: boolean;
  compact?: boolean;
}

export const SubscriptionStatus: React.FC<SubscriptionStatusProps> = ({ 
  showUpgradePrompt = true,
  compact = false 
}) => {
  const {
    subscription,
    isLoading,
    isTrialing,
    isActive,
    isPastDue,
    needsAction,
    daysRemainingInTrial,
    statusDisplay,
    createCheckoutSession,
    openBillingPortal,
    isCreatingCheckout,
    isOpeningPortal,
  } = useSubscription();

  const { subscriptionTier } = useFeatureAccess();

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span className="text-sm text-gray-600">Loading subscription...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!subscription) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center space-y-4">
            <div className="mx-auto w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
              <Crown className="h-6 w-6 text-gray-400" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">No Subscription</h3>
              <p className="text-sm text-gray-600">Start your free trial to access all features</p>
            </div>
            <Button 
              onClick={() => createCheckoutSession()}
              disabled={isCreatingCheckout}
              className="w-full"
            >
              {isCreatingCheckout ? 'Creating...' : 'Start Free Trial'}
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Get status color and icon
  const getStatusBadge = () => {
    switch (subscription.status) {
      case 'trialing':
        return (
          <Badge variant="secondary" className="bg-blue-100 text-blue-800">
            <Clock className="h-3 w-3 mr-1" />
            Free Trial
          </Badge>
        );
      case 'active':
        return (
          <Badge variant="default" className="bg-green-100 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            Active
          </Badge>
        );
      case 'past_due':
        return (
          <Badge variant="destructive">
            <AlertTriangle className="h-3 w-3 mr-1" />
            Past Due
          </Badge>
        );
      case 'canceled':
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-800">
            Canceled
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {statusDisplay}
          </Badge>
        );
    }
  };

  const getPlanBadge = () => {
    const isEnterprise = subscription.plan_id === 'enterprise';
    return (
      <Badge variant={isEnterprise ? "default" : "secondary"}>
        <Crown className="h-3 w-3 mr-1" />
        {subscription.plan_id === 'enterprise' ? 'Premium' : 'Basic'}
      </Badge>
    );
  };

  // Compact view for headers/sidebars
  if (compact) {
    return (
      <div className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
        <div className="flex items-center space-x-2">
          {getStatusBadge()}
          {getPlanBadge()}
        </div>
        {needsAction && (
          <Button size="sm" variant="outline" onClick={() => openBillingPortal()}>
            <Settings className="h-3 w-3" />
          </Button>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Action needed alert */}
      {needsAction && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {isPastDue ? (
              <>
                Your payment failed. Update your payment method to restore access.
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="ml-2"
                  onClick={() => openBillingPortal()}
                  disabled={isOpeningPortal}
                >
                  {isOpeningPortal ? 'Opening...' : 'Update Payment'}
                </Button>
              </>
            ) : isTrialing && daysRemainingInTrial <= 2 ? (
              <>
                Your trial expires in {daysRemainingInTrial} day{daysRemainingInTrial !== 1 ? 's' : ''}. 
                Subscribe to continue using all features.
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="ml-2"
                  onClick={() => createCheckoutSession()}
                  disabled={isCreatingCheckout}
                >
                  {isCreatingCheckout ? 'Creating...' : 'Subscribe Now'}
                </Button>
              </>
            ) : null}
          </AlertDescription>
        </Alert>
      )}

      {/* Main status card */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Subscription</CardTitle>
            <div className="flex space-x-2">
              {getStatusBadge()}
              {getPlanBadge()}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Trial countdown */}
          {isTrialing && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-blue-900">Free Trial Active</h4>
                  <p className="text-sm text-blue-700">
                    {daysRemainingInTrial} day{daysRemainingInTrial !== 1 ? 's' : ''} remaining
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-xs text-blue-600">Trial ends</p>
                  <p className="text-sm font-medium text-blue-900">
                    {subscription.trial_end && new Date(subscription.trial_end).toLocaleDateString()}
                  </p>
                </div>
              </div>
              {showUpgradePrompt && daysRemainingInTrial <= 3 && (
                <Button 
                  className="w-full mt-3"
                  onClick={() => createCheckoutSession()}
                  disabled={isCreatingCheckout}
                >
                  {isCreatingCheckout ? 'Creating...' : 'Subscribe for €14.99/month'}
                </Button>
              )}
            </div>
          )}

          {/* Active subscription info */}
          {isActive && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-green-900">Active Subscription</h4>
                  <p className="text-sm text-green-700">
                    {subscription.plan_id === 'enterprise' ? '€69.99' : '€14.99'}/month
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-xs text-green-600">Next billing</p>
                  <p className="text-sm font-medium text-green-900">
                    {subscription.current_period_end && 
                      new Date(subscription.current_period_end).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Action buttons */}
          <div className="flex space-x-2">
            <Button 
              variant="outline" 
              className="flex-1"
              onClick={() => openBillingPortal()}
              disabled={isOpeningPortal}
            >
              <CreditCard className="h-4 w-4 mr-2" />
              {isOpeningPortal ? 'Opening...' : 'Manage Billing'}
            </Button>
            
            {showUpgradePrompt && subscription.plan_id === 'professional' && (
              <Button 
                variant="default"
                className="flex-1"
                onClick={() => createCheckoutSession('price_enterprise')}
                disabled={isCreatingCheckout}
              >
                <Crown className="h-4 w-4 mr-2" />
                {isCreatingCheckout ? 'Creating...' : 'Upgrade'}
              </Button>
            )}
          </div>

          {/* Feature summary */}
          <div className="border-t pt-4">
            <h5 className="font-medium text-sm text-gray-900 mb-2">Your Plan Includes</h5>
            <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
              <div className="flex items-center">
                <CheckCircle className="h-3 w-3 mr-1 text-green-500" />
                Menu Management
              </div>
              <div className="flex items-center">
                <CheckCircle className="h-3 w-3 mr-1 text-green-500" />
                Order Processing
              </div>
              <div className="flex items-center">
                <CheckCircle className="h-3 w-3 mr-1 text-green-500" />
                Analytics Dashboard
              </div>
              <div className="flex items-center">
                <CheckCircle className="h-3 w-3 mr-1 text-green-500" />
                QR Code Generation
              </div>
              {subscription.plan_id === 'enterprise' && (
                <>
                  <div className="flex items-center">
                    <CheckCircle className="h-3 w-3 mr-1 text-green-500" />
                    Dynamic Pricing
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="h-3 w-3 mr-1 text-green-500" />
                    Multi-location
                  </div>
                </>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};