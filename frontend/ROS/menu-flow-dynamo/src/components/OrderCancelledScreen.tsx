import React, { useEffect } from 'react';
import { AlertTriangle, Clock, Phone, MessageSquare, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useLanguage } from '@/contexts/LanguageContext';
import { Link, useNavigate } from 'react-router-dom';
import StaffRequestButton from '@/components/StaffRequestButton';
import { CollaborativeOrderTrackingService } from '@/services/collaborativeOrderTrackingService';

interface OrderCancelledScreenProps {
  orderId: string;
  orderNumber?: string;
  tableId?: string;
  restaurantId?: string;
  cancelledAt?: string;
  totalAmount?: number;
  onBackToMenu?: () => void;
}

const OrderCancelledScreen: React.FC<OrderCancelledScreenProps> = ({
  orderId,
  orderNumber,
  tableId,
  restaurantId,
  cancelledAt,
  totalAmount,
  onBackToMenu
}) => {
  const { t } = useLanguage();
  const navigate = useNavigate();

  /**
   * Clear all session storage data related to the cancelled order
   * This prevents the cancelled order's tracking info from showing up when placing new orders
   */
  const clearCancelledOrderData = () => {
    try {
      // Clear order-specific session storage
      sessionStorage.removeItem('currentOrderId');
      sessionStorage.removeItem('orderNumber');
      sessionStorage.removeItem('trackingNumber');
      sessionStorage.removeItem('estimatedTime');
      sessionStorage.removeItem('restaurantId');

      // Clear collaborative order tracking data
      CollaborativeOrderTrackingService.clearFromSessionStorage();

      // Clear cart data to ensure clean state
      sessionStorage.removeItem('cartItems');
      sessionStorage.removeItem('smartCartData');
      sessionStorage.removeItem('cartData');
      sessionStorage.removeItem('smartCart_individual');
      sessionStorage.removeItem('orderComment');

      console.log('🧹 Cleared cancelled order data from session storage');
    } catch (error) {
      console.error('Error clearing cancelled order data:', error);
    }
  };

  // Clear cancelled order data when component mounts to prevent interference with new orders
  useEffect(() => {
    clearCancelledOrderData();
  }, []);

  const handleBackToMenu = () => {
    // Clear all order-related session storage to prevent cancelled order data from interfering with new orders
    clearCancelledOrderData();

    if (onBackToMenu) {
      onBackToMenu();
    } else {
      navigate(`/menu${tableId ? `?table=${tableId}` : ''}`);
    }
  };

  const formatCancelTime = (timestamp?: string) => {
    if (!timestamp) return '';
    try {
      return new Date(timestamp).toLocaleString();
    } catch {
      return '';
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-restaurant-background">
      <div className="flex-1 container mx-auto px-4 py-6">
        <div className="max-w-lg mx-auto">
          {/* Header with back button */}
          <div className="flex items-center mb-6">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBackToMenu}
              className="mr-3"
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to Menu
            </Button>
          </div>

          {/* Cancellation Notice */}
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
            <h1 className="text-2xl font-bold text-restaurant-text mb-2">
              Order Cancelled
            </h1>
            <p className="text-lg font-medium mb-1">
              Order #{orderNumber || orderId.substring(0, 8)}
            </p>
            {cancelledAt && (
              <p className="text-sm text-restaurant-muted">
                Cancelled on {formatCancelTime(cancelledAt)}
              </p>
            )}
          </div>

          {/* Cancellation Information Card */}
          <Card className="mb-6 border-red-200 bg-red-50">
            <CardContent className="p-6">
              <div className="flex items-start space-x-3">
                <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-red-800 mb-2">
                    Your order has been cancelled by the restaurant
                  </h3>
                  <p className="text-sm text-red-700 mb-3">
                    We apologize for any inconvenience. This could be due to ingredient availability,
                    kitchen capacity, or other operational reasons.
                  </p>
                  <p className="text-sm text-red-700">
                    <strong>Note:</strong> This order will not be added to your final bill when you pay at the restaurant.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* What happens next */}
          <Card className="mb-6">
            <CardContent className="p-6">
              <h3 className="font-semibold text-restaurant-text mb-4 flex items-center">
                <Clock className="h-5 w-5 mr-2 text-restaurant-primary" />
                What happens next?
              </h3>
              <div className="space-y-3 text-sm text-restaurant-muted">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-restaurant-primary rounded-full mt-2 flex-shrink-0"></div>
                  <p>This cancelled order will not appear on your final bill when you pay at the restaurant</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-restaurant-primary rounded-full mt-2 flex-shrink-0"></div>
                  <p>You can place a new order anytime by browsing our menu</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-restaurant-primary rounded-full mt-2 flex-shrink-0"></div>
                  <p>Our staff is available to help you with alternative menu options</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="space-y-3 mb-6">
            <Button 
              onClick={handleBackToMenu}
              className="w-full bg-restaurant-primary hover:bg-restaurant-primary/90"
            >
              Browse Menu Again
            </Button>

            {tableId && restaurantId && (
              <StaffRequestButton
                tableId={tableId}
                restaurantId={restaurantId}
                className="w-full bg-orange-600 hover:bg-orange-700 text-white"
                customMessage="I need assistance regarding my cancelled order"
              >
                <MessageSquare className="h-4 w-4 mr-2" />
                Request Staff Assistance
              </StaffRequestButton>
            )}
          </div>

          {/* Contact Information */}
          <Card className="border-gray-200 bg-gray-50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold text-gray-800 mb-2 flex items-center">
                    <Phone className="h-5 w-5 mr-2" />
                    Need immediate help?
                  </h3>
                  <p className="text-sm text-gray-600">
                    Contact our staff directly for assistance or alternative menu options
                  </p>
                </div>
                <Button variant="outline" size="sm">
                  Call Restaurant
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Footer message */}
          <div className="text-center mt-6">
            <p className="text-sm text-restaurant-muted">
              We appreciate your understanding and look forward to serving you again.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderCancelledScreen;
