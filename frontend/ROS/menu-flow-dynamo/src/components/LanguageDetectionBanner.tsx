import React, { useState, useEffect } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { X, Globe, Check } from 'lucide-react';
import { motion, AnimatePresence } from "motion/react";

const LanguageDetectionBanner: React.FC = () => {
  const { language, autoDetectedLanguage, isLanguageDetected, setLanguage, t } = useLanguage();
  const [isVisible, setIsVisible] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);

  useEffect(() => {
    // Check if user has already dismissed this banner
    const dismissed = localStorage.getItem('language_banner_dismissed');
    if (dismissed) {
      setIsDismissed(true);
      return;
    }

    // Show banner after language detection is complete
    if (isLanguageDetected && autoDetectedLanguage) {
      // Small delay to ensure smooth appearance
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [isLanguageDetected, autoDetectedLanguage]);

  const handleLanguageChange = (newLanguage: 'en' | 'es') => {
    setLanguage(newLanguage);
    setIsVisible(false);
    // Store dismissal so banner doesn't show again
    localStorage.setItem('language_banner_dismissed', 'true');
  };

  const handleDismiss = () => {
    setIsVisible(false);
    setIsDismissed(true);
    localStorage.setItem('language_banner_dismissed', 'true');
  };

  const getLanguageName = (lang: 'en' | 'es') => {
    return lang === 'en' ? 'English' : 'Español';
  };

  const getBannerText = () => {
    if (language === 'en') {
      return {
        detected: `We detected your language as ${getLanguageName(language)}`,
        question: "Is this correct?",
        confirm: "Yes, continue in English",
        change: "Switch to Español",
        dismiss: "Dismiss"
      };
    } else {
      return {
        detected: `Detectamos tu idioma como ${getLanguageName(language)}`,
        question: "¿Es correcto?",
        confirm: "Sí, continúar en Español",
        change: "Cambiar a English",
        dismiss: "Descartar"
      };
    }
  };

  if (isDismissed || !isVisible || !autoDetectedLanguage) {
    return null;
  }

  const text = getBannerText();

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -100 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -100 }}
          transition={{ duration: 0.5, ease: "easeOut" }}
          className="fixed top-0 left-0 right-0 z-50 bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg"
        >
          <div className="container mx-auto px-4 py-3">
            <div className="flex items-center justify-between gap-4">
              <div className="flex items-center gap-3">
                <Globe className="w-5 h-5 text-blue-200" />
                <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                  <span className="font-medium text-sm">
                    {text.detected}
                  </span>
                  <span className="text-blue-200 text-sm">
                    {text.question}
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-2">
                {/* Confirm current language */}
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => handleLanguageChange(language)}
                  className="flex items-center gap-2 px-3 py-1.5 bg-green-500 hover:bg-green-600 text-white rounded-lg font-medium text-sm transition-colors"
                >
                  <Check className="w-4 h-4" />
                  <span className="hidden sm:inline">{text.confirm}</span>
                  <span className="sm:hidden">✓</span>
                </motion.button>

                {/* Switch language */}
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => handleLanguageChange(language === 'en' ? 'es' : 'en')}
                  className="px-3 py-1.5 bg-white/20 hover:bg-white/30 text-white rounded-lg font-medium text-sm transition-colors"
                >
                  <span className="hidden sm:inline">{text.change}</span>
                  <span className="sm:hidden">
                    {language === 'en' ? 'ES' : 'EN'}
                  </span>
                </motion.button>

                {/* Dismiss button */}
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleDismiss}
                  className="p-1.5 hover:bg-white/20 rounded-lg transition-colors"
                  aria-label={text.dismiss}
                >
                  <X className="w-4 h-4" />
                </motion.button>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default LanguageDetectionBanner; 