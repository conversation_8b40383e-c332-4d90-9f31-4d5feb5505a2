import React from 'react';
import { Table } from 'lucide-react';

interface TableInfoBadgeProps {
  tableId: string | undefined;
  className?: string;
}

/**
 * Small component to display table information consistently across the app
 */
export const TableInfoBadge: React.FC<TableInfoBadgeProps> = ({ tableId, className = '' }) => {
  if (!tableId) return null;
  
  // Format table ID for display - take last segment if UUID format
  const displayId = tableId.includes('-') 
    ? tableId.split('-').pop()?.substring(0, 3) 
    : tableId.substring(0, 3);
  
  return (
    <div className={`inline-flex items-center gap-1 px-2 py-0.5 bg-sky-50 text-sky-700 rounded-full text-xs ${className}`}>
      <Table size={12} />
      <span>Table #{displayId}</span>
    </div>
  );
};

export default TableInfoBadge;
