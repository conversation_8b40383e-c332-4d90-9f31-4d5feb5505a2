/**
 * Simple Real-time Status Indicator
 * Shows connection status with a small dot
 */

import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

interface RealtimeStatusIndicatorProps {
  className?: string;
  showText?: boolean;
}

export function RealtimeStatusIndicator({ 
  className = '', 
  showText = false 
}: RealtimeStatusIndicatorProps) {
  const [status, setStatus] = useState<'connected' | 'disconnected' | 'testing'>('testing');
  const [lastUpdate, setLastUpdate] = useState<string>('');

  useEffect(() => {
    let mounted = true;

    // Test connection
    const testConnection = async () => {
      try {
        const { error } = await supabase.from('notifications').select('count').limit(1);
        if (mounted) {
          setStatus(error ? 'disconnected' : 'connected');
          setLastUpdate(new Date().toLocaleTimeString());
        }
      } catch (error) {
        if (mounted) {
          setStatus('disconnected');
        }
      }
    };

    // Test immediately
    testConnection();

    // Test every 30 seconds
    const interval = setInterval(testConnection, 30000);

    return () => {
      mounted = false;
      clearInterval(interval);
    };
  }, []);

  const getStatusColor = () => {
    switch (status) {
      case 'connected':
        return 'bg-green-500';
      case 'disconnected':
        return 'bg-red-500';
      default:
        return 'bg-yellow-500';
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'connected':
        return 'Real-time Connected';
      case 'disconnected':
        return 'Real-time Disconnected';
      default:
        return 'Testing Connection';
    }
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <div 
        className={`w-2 h-2 rounded-full ${getStatusColor()}`}
        title={`${getStatusText()}${lastUpdate ? ` (${lastUpdate})` : ''}`}
      />
      {showText && (
        <span className="text-xs text-gray-500">
          {getStatusText()}
        </span>
      )}
    </div>
  );
}