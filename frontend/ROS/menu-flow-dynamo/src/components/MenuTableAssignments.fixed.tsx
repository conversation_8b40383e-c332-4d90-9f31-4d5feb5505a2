import React, { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { fetchTables as fetchTablesFromDb, assignMenusToTable } from '@/services/tableDbService';
import { useAuth } from '@/contexts/AuthContext';
import { Loader2, CheckCircle } from 'lucide-react';
import { useRestaurant } from '@/contexts/RestaurantContext';

interface MenuTableAssignmentsProps {
  isOpen: boolean;
  onClose: () => void;
  menuId: string | null;
}

interface RestaurantTable {
  id: string;
  restaurant_id: string;
  table_number: string;
  capacity?: number;
  location?: string;
  is_occupied?: boolean;
  status?: string;
  qr_code_url?: string;
  created_at?: string;
  updated_at?: string;
}

// Fetch tables from database
const fetchTables = async (restaurantId: string) => {
  try {

    const tables = await fetchTablesFromDb();

    // Filter tables for this restaurant and sort by table number
    const filteredTables = tables
      .filter((table: RestaurantTable) => table.restaurant_id === restaurantId)
      .sort((a: RestaurantTable, b: RestaurantTable) => {
        const numA = parseInt(a.table_number.toString().replace(/\D/g, '')) || 0;
        const numB = parseInt(b.table_number.toString().replace(/\D/g, '')) || 0;
        return numA - numB;
      });

    return filteredTables;
  } catch (error) {
    console.error('Error fetching tables from database:', error);
    return [];
  }
};

// Fetch assigned tables from database
const fetchAssignedTables = async (menuId: string | null) => {
  if (!menuId) return [];

  try {
    const { data, error } = await supabase
      .from('menu_table_assignments')
      .select('table_id')
      .eq('menu_id', menuId);

    if (error) throw error;

    return data.map(assignment => assignment.table_id);
  } catch (error) {
    console.error('Error fetching assigned tables from database:', error);
    return [];
  }
};

const MenuTableAssignments: React.FC<MenuTableAssignmentsProps> = ({ isOpen, onClose, menuId }) => {
  const { toast } = useToast();
  const { user } = useAuth();
  const { restaurantInfo } = useRestaurant();
  const [selectedTables, setSelectedTables] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Get tables for this restaurant
  const { data: tables, isLoading: tablesLoading } = useQuery({
    queryKey: ['tables_for_assignment', restaurantInfo?.id],
    queryFn: () => fetchTables(restaurantInfo?.id || ''),
    enabled: isOpen && !!restaurantInfo?.id
  });

  // Fetch assigned tables when dialog opens
  useEffect(() => {
    if (isOpen && menuId) {
      const loadAssignedTables = async () => {
        setIsLoading(true);
        try {
          const assignedTables = await fetchAssignedTables(menuId);
          setSelectedTables(assignedTables);
        } catch (error) {
          console.error('Error loading assigned tables:', error);
        } finally {
          setIsLoading(false);
        }
      };

      loadAssignedTables();
    } else {
      // Clear selected tables when dialog closes
      setSelectedTables([]);
    }
  }, [isOpen, menuId]);

  const toggleTableSelection = (tableId: string) => {
    setSelectedTables(prev => {
      if (prev.includes(tableId)) {
        return prev.filter(id => id !== tableId);
      } else {
        return [...prev, tableId];
      }
    });
  };

  const handleSubmit = async () => {
    if (!menuId) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "No menu selected for assignment.",
      });
      return;
    }

    try {
      setIsSubmitting(true);
      await assignMenusToTable(menuId, selectedTables);
      toast({
        title: "Success",
        description: "Table assignments have been updated.",
      });
      onClose();
    } catch (error) {
      console.error('Error saving table assignments:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update table assignments.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Assign Menu to Tables</DialogTitle>
          <p className="text-sm text-muted-foreground">
            Select which tables should have access to this menu item
          </p>
        </DialogHeader>

        {(isLoading || tablesLoading) ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 text-primary animate-spin" />
            <span className="ml-2">Loading tables...</span>
          </div>
        ) : (
          <div className="grid gap-4 py-4">
            <p>Select the tables that should have access to this menu:</p>

            <div className="grid grid-cols-2 gap-2 max-h-[300px] overflow-y-auto">
              {tables && tables.length > 0 ? (
                tables.map((table: RestaurantTable) => (
                  <label
                    key={table.id}
                    className={`flex items-center space-x-2 border rounded-md p-3 hover:bg-slate-50 cursor-pointer ${selectedTables.includes(table.id) ? 'bg-primary/5 border-primary/30' : ''}`}
                  >
                    <input
                      type="checkbox"
                      checked={selectedTables.includes(table.id)}
                      onChange={() => toggleTableSelection(table.id)}
                      className="h-4 w-4 text-primary"
                    />
                    <div className="flex flex-col flex-1">
                      <span className="font-medium">Table {table.table_number}</span>
                      {table.location && (
                        <span className="text-xs text-slate-500">{table.location}</span>
                      )}
                    </div>
                    {selectedTables.includes(table.id) && (
                      <CheckCircle className="h-4 w-4 text-primary" />
                    )}
                  </label>
                ))
              ) : (
                <div className="col-span-2 p-6 text-center bg-slate-50 rounded-md">
                  <p className="text-slate-500">No tables found for this restaurant. Please create tables first.</p>
                </div>
              )}
            </div>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting || isLoading || tablesLoading}
            className="bg-primary hover:bg-primary/90"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              'Save Assignments'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default MenuTableAssignments;
