
import React, { useEffect, useState } from 'react';
import { format } from 'date-fns';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Progress } from "@/components/ui/progress";
import { ArrowUpIcon, ArrowDownIcon } from "@radix-ui/react-icons";
import { Skeleton } from "@/components/ui/skeleton";
import { useQuery } from '@tanstack/react-query';
import { BarChart } from "@/components/ui/chart";
import { fetchActivityData } from "@/services/analyticsService";
import { useAuth } from "@/contexts/AuthContext";
import { formatCurrency } from '@/utils/currencyUtils';
import { 
  CalendarClock,
  Users,
  CircleDollarSign,
  ShoppingCart,
  TrendingUp,
  TrendingDown,
  Clock,
  Activity,
  Loader2
} from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { fetchRestaurantActivity } from '@/services/restaurantActivityService';

interface ActivityMetric {
  label: string;
  value: string | number;
  previousValue?: string | number;
  change?: number;
  icon: React.ReactNode;
  color: string;
}

export function RestaurantActivityDashboard() {
  const { t } = useLanguage();
  const { user } = useAuth();
  const now = new Date();
  
  // Fetch real restaurant activity data from database
  const { data: activityData, isLoading } = useQuery({
    queryKey: ['restaurantActivity', user?.id],
    queryFn: () => fetchRestaurantActivity(user),
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
    enabled: !!user
  });
  
  // Using centralized formatCurrency utility from currencyUtils
  // which properly formats with Euro (€) symbol for Spain

  // Calculate percentage change
  const calculatePercentChange = (current: number, previous: number): number => {
    if (previous === 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  };
  
  // Prepare metrics data with real data or fallbacks
  const metricsData: ActivityMetric[] = [
    {
      label: t('occupancyRate'),
      value: activityData ? `${Math.round(activityData.occupancyRate)}%` : '-',
      previousValue: activityData ? `${Math.round(activityData.previousOccupancyRate)}%` : '-',
      change: activityData ? calculatePercentChange(activityData.occupancyRate, activityData.previousOccupancyRate) : 0,
      icon: <Users className="h-4 w-4" />,
      color: 'text-sky-500'
    },
    {
      label: t('totalSales'),
      value: activityData ? formatCurrency(activityData.totalSales) : '-',
      previousValue: activityData ? formatCurrency(activityData.previousTotalSales) : '-',
      change: activityData ? calculatePercentChange(activityData.totalSales, activityData.previousTotalSales) : 0,
      icon: <CircleDollarSign className="h-4 w-4" />,
      color: 'text-emerald-500'
    },
    {
      label: t('ordersToday'),
      value: activityData ? activityData.ordersCount : '-',
      previousValue: activityData ? activityData.previousOrdersCount : '-',
      change: activityData ? calculatePercentChange(activityData.ordersCount, activityData.previousOrdersCount) : 0,
      icon: <ShoppingCart className="h-4 w-4" />,
      color: 'text-amber-500'
    },
    {
      label: t('avgOrderValue'),
      value: activityData ? formatCurrency(activityData.avgOrderValue) : '-',
      previousValue: activityData ? formatCurrency(activityData.previousAvgOrderValue) : '-',
      change: activityData ? calculatePercentChange(activityData.avgOrderValue, activityData.previousAvgOrderValue) : 0,
      icon: <Activity className="h-4 w-4" />,
      color: 'text-indigo-500'
    }
  ];
  
  // Calculate business hours progress
  const businessHours = {
    open: 11, // 11 AM
    close: 23, // 11 PM
  };
  
  const currentHour = now.getHours() + (now.getMinutes() / 60);
  const totalBusinessHours = businessHours.close - businessHours.open;
  const elapsedHours = Math.max(0, Math.min(totalBusinessHours, currentHour - businessHours.open));
  const businessDayProgress = (elapsedHours / totalBusinessHours) * 100;
  
  // Format for display
  const formatBusinessHours = (hour: number) => {
    const period = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 === 0 ? 12 : hour % 12;
    return `${displayHour} ${period}`;
  };
  
  const isRestaurantOpen = currentHour >= businessHours.open && currentHour <= businessHours.close;
  
  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-md flex items-center gap-2">
            <CalendarClock className="h-5 w-5 text-sky-500" />
            {t('todaysActivity')} | {format(now, 'MMMM d, yyyy')}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-gray-500" />
            <span className="text-sm">{format(now, 'h:mm a')}</span>
            <Badge variant={isRestaurantOpen ? "outline" : "destructive"} className={isRestaurantOpen ? "bg-green-100 text-green-600 hover:bg-green-100" : ""}>
              {isRestaurantOpen ? t('open') : t('closed')}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="mb-4">
          <div className="flex justify-between items-center mb-1 text-sm">
            <div>
              <span className="text-gray-500">{t('businessHours')}: </span>
              <span className="font-medium">{formatBusinessHours(businessHours.open)} - {formatBusinessHours(businessHours.close)}</span>
            </div>
            <div>
              <span className="text-gray-500">{t('dayProgress')}: </span>
              <span className="font-medium">{Math.round(businessDayProgress)}%</span>
            </div>
          </div>
          <Progress value={businessDayProgress} className="h-2 bg-gray-100" />
        </div>
        
        {isLoading ? (
          <div className="flex justify-center items-center py-6">
            <Loader2 className="h-6 w-6 text-sky-500 animate-spin" />
            <span className="ml-2 text-sm text-muted-foreground">Loading activity data...</span>
          </div>
        ) : (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {metricsData.map((metric, index) => (
              <div key={index} className="p-3 bg-white rounded-lg border border-gray-100 shadow-sm hover:shadow-md transition-shadow">
                <div className="flex items-center gap-2 mb-2">
                  <div className={`${metric.color} p-1.5 rounded-full bg-opacity-10`}>
                    {metric.icon}
                  </div>
                  <span className="text-xs text-gray-500">{metric.label}</span>
                </div>
                <div className="flex items-end justify-between">
                  <span className="text-xl font-semibold">{metric.value}</span>
                  {typeof metric.change === 'number' && (
                    <div className={`flex items-center text-xs ${metric.change >= 0 ? 'text-emerald-500' : 'text-rose-500'}`}>
                      {metric.change >= 0 ? (
                        <TrendingUp className="h-3 w-3 mr-1" />
                      ) : (
                        <TrendingDown className="h-3 w-3 mr-1" />
                      )}
                      <span>{Math.abs(parseFloat(metric.change.toFixed(1)))}%</span>
                    </div>
                  )}
                </div>
                {metric.previousValue && (
                  <div className="text-xs text-gray-400 mt-1">
                    {t('previousDay')}: {metric.previousValue}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
