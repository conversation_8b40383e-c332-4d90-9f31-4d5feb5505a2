/**
 * ThemeAwareCard Component
 * A card component that automatically applies restaurant theme styling
 */

import React from 'react';
import { useThemeStyles } from '@/hooks/useRestaurantTheme';
import { cn } from '@/lib/utils';

interface ThemeAwareCardProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'menu-item' | 'featured';
  padding?: 'sm' | 'md' | 'lg';
  style?: React.CSSProperties;
}

export const ThemeAwareCard: React.FC<ThemeAwareCardProps> = ({
  children,
  className = '',
  variant = 'default',
  padding = 'md',
  style = {}
}) => {
  const { getThemeClassName, getComponentStyles, getThemeTailwindClasses } = useThemeStyles();

  // Get base theme styles
  const themeStyles = getComponentStyles('card');
  
  // Generate responsive padding
  const paddingClasses = {
    sm: 'p-2',
    md: 'p-4',
    lg: 'p-6'
  };

  // Variant-specific styling
  const variantStyles: Record<string, React.CSSProperties> = {
    default: {},
    'menu-item': {
      transition: 'all 0.2s ease-in-out',
      cursor: 'pointer',
      ...themeStyles
    },
    featured: {
      transform: 'scale(1.02)',
      boxShadow: `0 4px 12px rgba(0,0,0,0.15)`,
      ...themeStyles
    }
  };

  const combinedClassName = cn(
    getThemeClassName('theme-card'),
    getThemeTailwindClasses('card'),
    paddingClasses[padding],
    'transition-colors duration-200',
    variant === 'menu-item' && 'hover:shadow-lg hover:scale-[1.02]',
    variant === 'featured' && 'shadow-xl',
    className
  );

  const combinedStyles = {
    ...themeStyles,
    ...variantStyles[variant],
    ...style
  };

  return (
    <div 
      className={combinedClassName}
      style={combinedStyles}
    >
      {children}
    </div>
  );
};

export default ThemeAwareCard;
