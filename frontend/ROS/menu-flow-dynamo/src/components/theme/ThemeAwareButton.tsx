/**
 * ThemeAwareButton Component
 * A button component that automatically applies restaurant theme styling
 */

import React from 'react';
import { useThemeStyles } from '@/hooks/useRestaurantTheme';
import { cn } from '@/lib/utils';

interface ThemeAwareButtonProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'primary' | 'secondary' | 'accent' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
  style?: React.CSSProperties;
}

export const ThemeAwareButton: React.FC<ThemeAwareButtonProps> = ({
  children,
  className = '',
  variant = 'primary',
  size = 'md',
  onClick,
  disabled = false,
  type = 'button',
  style = {}
}) => {
  const { getThemeClassName, getComponentStyles, getColorValue, theme } = useThemeStyles();

  // Get base theme styles
  const baseStyles = getComponentStyles('button');
  
  // Size-specific styling
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  };

  // Variant-specific styling based on theme colors
  const getVariantStyles = (): React.CSSProperties => {
    if (!theme) return {};

    const variantMap = {
      primary: {
        backgroundColor: getColorValue('primary'),
        color: '#ffffff',
        border: 'none'
      },
      secondary: {
        backgroundColor: getColorValue('secondary'),
        color: getColorValue('textPrimary'),
        border: `1px solid ${getColorValue('secondary')}`
      },
      accent: {
        backgroundColor: getColorValue('accent'),
        color: '#ffffff',
        border: 'none'
      },
      outline: {
        backgroundColor: 'transparent',
        color: getColorValue('primary'),
        border: `2px solid ${getColorValue('primary')}`
      },
      ghost: {
        backgroundColor: 'transparent',
        color: getColorValue('textPrimary'),
        border: 'none'
      }
    };

    return variantMap[variant] || variantMap.primary;
  };

  // Hover and focus states
  const getHoverStyles = (): string => {
    const hoverMap = {
      primary: 'hover:opacity-90 focus:ring-4 focus:ring-restaurant-primary/30',
      secondary: 'hover:bg-restaurant-secondary/80 focus:ring-4 focus:ring-restaurant-secondary/30',
      accent: 'hover:opacity-90 focus:ring-4 focus:ring-restaurant-accent/30',
      outline: 'hover:bg-restaurant-primary hover:text-white focus:ring-4 focus:ring-restaurant-primary/30',
      ghost: 'hover:bg-restaurant-secondary/10 focus:ring-4 focus:ring-restaurant-secondary/30'
    };

    return hoverMap[variant];
  };

  const combinedClassName = cn(
    getThemeClassName('theme-button'),
    'inline-flex items-center justify-center',
    'font-medium rounded-md',
    'transition-all duration-200 ease-in-out',
    'focus:outline-none focus:ring-offset-2',
    sizeClasses[size],
    getHoverStyles(),
    disabled && 'opacity-50 cursor-not-allowed',
    !disabled && 'active:scale-95',
    className
  );

  const combinedStyles = {
    ...baseStyles,
    ...getVariantStyles(),
    ...style
  };

  return (
    <button
      type={type}
      className={combinedClassName}
      style={combinedStyles}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
};

export default ThemeAwareButton;
