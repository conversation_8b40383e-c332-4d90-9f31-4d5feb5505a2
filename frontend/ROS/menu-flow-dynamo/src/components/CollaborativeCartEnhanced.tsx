/**
 * Enhanced Collaborative Order Cart - Improved Version
 * 
 * ENHANCEMENTS OVER ORIGINAL:
 * - Visual item attribution improvements  
 * - Better real-time feedback
 * - Enhanced UX for collaborative features
 * - Clearer leadership/checkout flow
 * - Improved mobile responsiveness
 */

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useLanguage } from '@/contexts/LanguageContext';
import { 
  ShoppingCart, 
  Plus, 
  Minus, 
  Trash2, 
  Users, 
  Lock, 
  Unlock,
  User,
  Clock,
  Crown,
  CheckCircle,
  AlertCircle,
  Eye,
  UserCheck,
  Wifi,
  WifiOff
} from 'lucide-react';
import { SharedCartItem } from '@/services/sharedCartService';
import { TableParticipant } from '@/services/sharedCartService';

interface CollaborativeOrderCartEnhancedProps {
  // Cart data
  items: SharedCartItem[];
  totalAmount: number;
  isLocked: boolean;
  lockedByMe: boolean;
  
  // Participants
  participants: TableParticipant[];
  customerSessionId: string;
  getParticipantName: (sessionId: string) => string;
  
  // Actions
  onIncreaseQuantity: (itemId: string) => void;
  onDecreaseQuantity: (itemId: string) => void;
  onRemoveItem: (itemId: string) => void;
  onCheckout: () => void;
  onLockCart: () => void;
  onUnlockCart: () => void;
  
  // UI state
  isOpen: boolean;
  onClose: () => void;
  canModifyItem: (item: SharedCartItem) => boolean;
  
  // Enhanced props
  isConnected?: boolean;
  lastUpdate?: Date;
  onPromptAsLead?: () => void;
}

const CollaborativeOrderCartEnhanced: React.FC<CollaborativeOrderCartEnhancedProps> = ({
  items,
  totalAmount,
  isLocked,
  lockedByMe,
  participants,
  customerSessionId,
  getParticipantName,
  onIncreaseQuantity,
  onDecreaseQuantity,
  onRemoveItem,
  onCheckout,
  onLockCart,
  onUnlockCart,
  isOpen,
  onClose,
  canModifyItem,
  isConnected = true,
  lastUpdate,
  onPromptAsLead
}) => {
  const { t } = useLanguage();
  const [showParticipants, setShowParticipants] = useState(false);
  const [recentActivity, setRecentActivity] = useState<string[]>([]);

  // Track recent activity for live feedback
  useEffect(() => {
    if (items.length > 0) {
      const latestItem = items.reduce((latest, item) => 
        new Date(item.added_at) > new Date(latest.added_at) ? item : latest
      );
      
      if (latestItem.added_by_session !== customerSessionId) {
        const customerName = getParticipantName(latestItem.added_by_session);
        const activity = `${customerName} added ${latestItem.name}`;
        
        setRecentActivity(prev => [activity, ...prev.slice(0, 2)]);
        
        // Clear after 5 seconds
        setTimeout(() => {
          setRecentActivity(prev => prev.filter(a => a !== activity));
        }, 5000);
      }
    }
  }, [items]);

  if (!isOpen) return null;

  // Group items by customer with enhanced data
  const itemsByCustomer = items.reduce((acc, item) => {
    const sessionId = item.added_by_session;
    if (!acc[sessionId]) {
      acc[sessionId] = {
        items: [],
        total: 0,
        participant: participants.find(p => p.customer_session_id === sessionId)
      };
    }
    acc[sessionId].items.push(item);
    acc[sessionId].total += item.price * item.quantity;
    return acc;
  }, {} as Record<string, { items: SharedCartItem[]; total: number; participant?: TableParticipant }>);

  const participantCount = participants.length;
  const myItems = items.filter(item => item.added_by_session === customerSessionId);
  const myTotal = myItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
  const lockedBy = isLocked ? participants.find(p => p.customer_session_id === items.find(i => i.added_by_session)?.added_by_session) : null;

  // Enhanced participant colors for better visual attribution
  const getParticipantColor = (sessionId: string): string => {
    const index = participants.findIndex(p => p.customer_session_id === sessionId);
    const colors = ['blue', 'green', 'purple', 'orange', 'pink', 'indigo'];
    return colors[index % colors.length];
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-end backdrop-blur-sm">
      <div className="bg-white w-full max-w-md flex flex-col h-full shadow-2xl transform transition-all duration-300 ease-out animate-in slide-in-from-right">
        
        {/* Enhanced Header with connection status and activity */}
        <div className="px-4 py-3 border-b bg-gradient-to-r from-blue-50 to-purple-50">
          <div className="flex justify-between items-center mb-2">
            <div className="flex items-center">
              <ShoppingCart className="h-5 w-5 mr-2 text-blue-600" />
              <h2 className="text-xl font-semibold text-gray-900">Shared Cart</h2>
              
              {/* Connection status indicator */}
              <div className="ml-2 flex items-center">
                {isConnected ? (
                  <Wifi className="h-4 w-4 text-green-500" />
                ) : (
                  <WifiOff className="h-4 w-4 text-red-500" />
                )}
              </div>
            </div>
            
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              aria-label="Close cart"
              className="hover:bg-white/50 focus:ring-2 focus:ring-blue-300 transition-colors"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M18 6 6 18" />
                <path d="m6 6 12 12" />
              </svg>
            </Button>
          </div>
          
          {/* Enhanced participants info with better visual layout */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <button
                onClick={() => setShowParticipants(!showParticipants)}
                className="flex items-center text-sm text-gray-600 hover:text-gray-800 transition-colors"
              >
                <Users className="h-4 w-4 mr-1" />
                <span>{participantCount} {participantCount === 1 ? 'person' : 'people'} at table</span>
                <Eye className="h-3 w-3 ml-1" />
              </button>
              
              {/* Lock status with enhanced visual */}
              {isLocked && (
                <Badge variant={lockedByMe ? "default" : "secondary"} className="flex items-center gap-1">
                  {lockedByMe ? <Crown className="h-3 w-3" /> : <Lock className="h-3 w-3" />}
                  {lockedByMe ? 'You\'re leading' : `Locked by ${getParticipantName(items.find(i => i.added_by_session)?.added_by_session || '')}`}
                </Badge>
              )}
            </div>

            {/* Participants list when expanded */}
            {showParticipants && (
              <div className="bg-white/70 rounded-lg p-2 space-y-1">
                {participants.map((participant, index) => {
                  const isMe = participant.customer_session_id === customerSessionId;
                  const color = getParticipantColor(participant.customer_session_id);
                  const itemCount = itemsByCustomer[participant.customer_session_id]?.items.length || 0;
                  
                  return (
                    <div key={participant.id} className="flex items-center justify-between text-xs">
                      <div className="flex items-center gap-2">
                        <div className={`w-2 h-2 rounded-full bg-${color}-500`} />
                        <span className={isMe ? 'font-medium text-blue-600' : 'text-gray-600'}>
                          {isMe ? 'You' : participant.customer_display_name}
                        </span>
                        {isMe && <UserCheck className="h-3 w-3 text-blue-500" />}
                      </div>
                      <span className="text-gray-500">{itemCount} items</span>
                    </div>
                  );
                })}
              </div>
            )}

            {/* Recent activity feed */}
            {recentActivity.length > 0 && (
              <div className="bg-green-50 rounded-lg p-2">
                <div className="flex items-center gap-1 text-xs text-green-700">
                  <CheckCircle className="h-3 w-3" />
                  <span className="font-medium">Recent activity:</span>
                </div>
                {recentActivity.map((activity, index) => (
                  <div key={index} className="text-xs text-green-600 mt-1 animate-fade-in">
                    {activity}
                  </div>
                ))}
              </div>
            )}

            {/* Last update indicator */}
            {lastUpdate && isConnected && (
              <div className="flex items-center gap-1 text-xs text-gray-400">
                <Clock className="h-3 w-3" />
                <span>Updated {lastUpdate.toLocaleTimeString()}</span>
              </div>
            )}
          </div>
        </div>

        {items.length === 0 ? (
          <div className="flex-1 flex flex-col items-center justify-center p-8">
            <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-full p-6 mb-6">
              <ShoppingCart className="h-16 w-16 text-blue-400" />
            </div>
            <h3 className="text-xl font-semibold mb-3 text-gray-900">Shared cart is empty</h3>
            <p className="text-gray-500 text-center leading-relaxed max-w-sm">
              Add items to start your collaborative order. Everyone at your table can contribute!
            </p>
            <div className="mt-6 text-center space-y-2">
              <div className="inline-flex items-center text-sm text-gray-400">
                <span>💡 Everyone at your table can add items</span>
              </div>
              <div className="inline-flex items-center text-sm text-gray-400">
                <span>🔒 Only one person can checkout to avoid duplicates</span>
              </div>
            </div>
          </div>
        ) : (
          <>
            <ScrollArea className="flex-1">
              <div className="p-4">
                {/* Enhanced items grouped by customer with better visuals */}
                {Object.entries(itemsByCustomer).map(([sessionId, customerData]) => {
                  const customerName = getParticipantName(sessionId);
                  const isMe = sessionId === customerSessionId;
                  const color = getParticipantColor(sessionId);
                  const customerTotal = customerData.total;
                  
                  return (
                    <div key={sessionId} className="mb-6 last:mb-0">
                      {/* Enhanced customer header with visual identity */}
                      <div className="flex items-center justify-between mb-3 pb-2 border-b border-gray-100">
                        <div className="flex items-center gap-2">
                          <div className={`w-3 h-3 rounded-full bg-${color}-500 shadow-sm`} />
                          <div className="flex items-center gap-2">
                            <User className={`h-4 w-4 ${isMe ? 'text-blue-600' : 'text-gray-400'}`} />
                            <span className={`font-medium ${isMe ? 'text-blue-600' : 'text-gray-600'}`}>
                              {isMe ? 'Your items' : customerName}
                            </span>
                            {isMe && <Badge variant="outline" className="text-xs">You</Badge>}
                            {isLocked && lockedByMe && isMe && (
                              <Badge variant="default" className="text-xs">
                                <Crown className="h-3 w-3 mr-1" />
                                Leading checkout
                              </Badge>
                            )}
                          </div>
                        </div>
                        <div className="text-right">
                          <span className="text-sm font-medium text-gray-700">
                            €{customerTotal.toFixed(2)}
                          </span>
                          <div className="text-xs text-gray-500">
                            {customerData.items.length} {customerData.items.length === 1 ? 'item' : 'items'}
                          </div>
                        </div>
                      </div>
                      
                      {/* Customer's items with enhanced visual attribution */}
                      {customerData.items.map((item) => {
                        const canModify = canModifyItem(item);
                        const timeSinceAdded = Date.now() - new Date(item.added_at).getTime();
                        const isRecentlyAdded = timeSinceAdded < 10000; // 10 seconds
                        
                        return (
                          <div 
                            key={`${item.id}-${sessionId}`} 
                            className={`py-3 border-b last:border-b-0 transition-all rounded-lg px-2 -mx-2 ${
                              canModify ? 'hover:bg-blue-50/50' : 'hover:bg-gray-50/30'
                            } ${isRecentlyAdded ? 'bg-green-50 animate-pulse' : ''}`}
                          >
                            <div className="flex justify-between">
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2">
                                  <div className="font-semibold text-gray-900 truncate">{item.name}</div>
                                  {isRecentlyAdded && <Badge variant="outline" className="text-xs bg-green-100 text-green-700">New!</Badge>}
                                </div>
                                <div className="text-sm text-gray-600 mt-1 flex items-center gap-2 flex-wrap">
                                  <span className="font-medium">€{item.price.toFixed(2)}</span>
                                  {item.category && (
                                    <span className="text-xs bg-gray-100 px-2 py-1 rounded-full capitalize">
                                      {item.category}
                                    </span>
                                  )}
                                  {item.added_at && (
                                    <span className="text-xs text-gray-400 flex items-center gap-1">
                                      <Clock className="h-3 w-3" />
                                      {new Date(item.added_at).toLocaleTimeString([], { 
                                        hour: '2-digit', 
                                        minute: '2-digit' 
                                      })}
                                    </span>
                                  )}
                                  <div className={`w-2 h-2 rounded-full bg-${color}-400`} title={`Added by ${customerName}`} />
                                </div>
                              </div>
                              
                              {/* Enhanced quantity controls */}
                              <div className={`flex items-center space-x-1 rounded-lg p-1 ${
                                canModify ? `bg-${color}-100` : 'bg-gray-100'
                              }`}>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className={`h-7 w-7 transition-all ${
                                    canModify 
                                      ? 'hover:bg-white hover:shadow-sm' 
                                      : 'opacity-50 cursor-not-allowed'
                                  }`}
                                  onClick={() => canModify && onDecreaseQuantity(item.id)}
                                  disabled={!canModify}
                                  aria-label={`Decrease quantity ${item.name}`}
                                >
                                  <Minus className="h-3 w-3" />
                                </Button>
                                <span className="w-8 text-center font-medium text-sm">
                                  {item.quantity}
                                </span>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className={`h-7 w-7 transition-all ${
                                    canModify 
                                      ? 'hover:bg-white hover:shadow-sm' 
                                      : 'opacity-50 cursor-not-allowed'
                                  }`}
                                  onClick={() => canModify && onIncreaseQuantity(item.id)}
                                  disabled={!canModify}
                                  aria-label={`Increase quantity ${item.name}`}
                                >
                                  <Plus className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                            
                            <div className="mt-3 flex justify-between items-center">
                              <div className="text-sm font-medium text-gray-700">
                                Subtotal: <span className="text-gray-900">€{(item.price * item.quantity).toFixed(2)}</span>
                              </div>
                              {canModify && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full transition-all"
                                  onClick={() => onRemoveItem(item.id)}
                                  aria-label={`Remove ${item.name}`}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  );
                })}
              </div>
            </ScrollArea>
            
            {/* Enhanced footer with better collaborative messaging */}
            <div className="border-t bg-gradient-to-r from-blue-50/50 to-purple-50/50 p-6">
              {/* Enhanced totals section */}
              <div className="mb-4 space-y-2">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-600 flex items-center gap-1">
                    <User className="h-3 w-3" />
                    Your contribution:
                  </span>
                  <span className="font-medium text-gray-900">€{myTotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between items-center border-t pt-2">
                  <span className="text-lg font-bold text-gray-900 flex items-center gap-1">
                    <Users className="h-4 w-4" />
                    Table total:
                  </span>
                  <span className="text-2xl font-bold text-gray-900">€{totalAmount.toFixed(2)}</span>
                </div>
              </div>
              
              {/* Enhanced action buttons with better messaging */}
              <div className="space-y-3">
                {!isLocked ? (
                  <div className="space-y-2">
                    <Button
                      className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]"
                      onClick={onLockCart}
                      disabled={items.length === 0}
                    >
                      <div className="flex items-center justify-center gap-2">
                        <Crown className="h-4 w-4" />
                        <span>Take Lead & Lock Cart</span>
                        <span className="text-sm opacity-90">→</span>
                      </div>
                    </Button>
                    {items.length > 0 && (
                      <p className="text-xs text-center text-gray-500">
                        💡 This prevents others from adding/removing items during checkout
                      </p>
                    )}
                  </div>
                ) : lockedByMe ? (
                  <div className="space-y-2">
                    <Button
                      className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]"
                      onClick={onCheckout}
                    >
                      <div className="flex items-center justify-center gap-2">
                        <CheckCircle className="h-4 w-4" />
                        <span>Send Order to Kitchen</span>
                        <span className="text-sm opacity-90">→</span>
                      </div>
                    </Button>
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={onUnlockCart}
                    >
                      <div className="flex items-center justify-center gap-2">
                        <Unlock className="h-4 w-4" />
                        <span>Unlock Cart</span>
                      </div>
                    </Button>
                    <p className="text-xs text-center text-green-700 bg-green-50 p-2 rounded">
                      🎯 You're leading the checkout! Everyone can see their items and total.
                    </p>
                  </div>
                ) : (
                  <div className="text-center space-y-3">
                    <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                      <Crown className="h-6 w-6 text-yellow-600 mx-auto mb-2" />
                      <p className="text-sm text-yellow-800 font-medium mb-1">
                        {getParticipantName(items.find(i => i.added_by_session)?.added_by_session || '')} is leading checkout
                      </p>
                      <p className="text-xs text-yellow-700">
                        Cart is locked to prevent changes during ordering
                      </p>
                    </div>
                    
                    {onPromptAsLead && (
                      <Button
                        variant="outline"
                        className="w-full text-blue-600 border-blue-200 hover:bg-blue-50"
                        onClick={onPromptAsLead}
                      >
                        <div className="flex items-center justify-center gap-2">
                          <AlertCircle className="h-4 w-4" />
                          <span>Ask to Take Lead?</span>
                        </div>
                      </Button>
                    )}
                  </div>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default CollaborativeOrderCartEnhanced;

/**
 * ENHANCEMENTS ADDED:
 * 
 * 🎨 VISUAL IMPROVEMENTS:
 * - Color-coded participants for better item attribution
 * - "Recently added" animations and badges
 * - Enhanced connection status indicators
 * - Better visual hierarchy and layout
 * 
 * 🔄 REAL-TIME FEATURES:
 * - Live activity feed showing recent actions
 * - Connection status indicator (WiFi/offline)
 * - Last update timestamps
 * - Animated feedback for new items
 * 
 * 👥 COLLABORATIVE UX:
 * - Expandable participants list with item counts
 * - "Take Lead" vs "Lock Cart" clearer messaging
 * - Better explanation of collaborative features
 * - Visual indicators for who's leading checkout
 * 
 * 🎯 ENHANCED MESSAGING:
 * - Clearer collaborative benefits explanation
 * - Better onboarding for empty cart state
 * - More intuitive leadership transfer
 * - Contextual help and tips
 * 
 * 📱 MOBILE OPTIMIZED:
 * - Better touch targets
 * - Responsive participant layout
 * - Improved scrolling and animations
 * - Better use of screen space
 */