import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import { useLanguage } from '@/contexts/LanguageContext';
import { 
  Star, 
  TrendingUp, 
  Share2, 
  Search, 
  BookOpen, 
  Bot,
  ExternalLink,
  Plus,
  Settings,
  BarChart3,
  Eye,
  MessageSquare,
  Globe,
  Zap,
  Target,
  Users,
  Award
} from 'lucide-react';
import {
  VisibilityAnalyticsService,
  PublicReviewService,
  SEOService,
  ContentService,
  AIIntegrationService
} from '@/services/visibilityService';
import type { VisibilityDashboardData } from '@/types/visibility';

interface EnhancedVisibilityDashboardProps {
  restaurantId: string;
}

export const EnhancedVisibilityDashboard: React.FC<EnhancedVisibilityDashboardProps> = ({
  restaurantId
}) => {
  const [dashboardData, setDashboardData] = useState<VisibilityDashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const { toast } = useToast();
  const { t } = useLanguage();

  useEffect(() => {
    loadDashboardData();
  }, [restaurantId]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const data = await VisibilityAnalyticsService.getDashboardData(restaurantId);
      setDashboardData(data);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      // Graceful fallback - set null instead of showing error toast
      setDashboardData(null);
    } finally {
      setLoading(false);
    }
  };

  const runSEOAudit = async () => {
    try {
      await SEOService.runSEOAudit({
        restaurant_id: restaurantId,
        audit_type: 'comprehensive'
      });
      
      toast({
        title: t('seoAuditStarted'),
        description: t('Your SEO audit is running. Results will be available shortly.'),
      });
      
      // Reload data after a short delay
      setTimeout(loadDashboardData, 2000);
    } catch (error) {
      console.error('Error running SEO audit:', error);
      // Graceful fallback - do nothing instead of showing error toast
    }
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-96 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="p-6 text-center">
        <p className="text-gray-500">{t('failedToLoadDashboard')}</p>
        <Button onClick={loadDashboardData} className="mt-4">
          {t('retry')}
        </Button>
      </div>
    );
  }

  // Ensure all required properties exist before rendering
  if (!dashboardData.overview || !dashboardData.platform_status) {
    return (
      <div className="p-6 text-center">
        <p className="text-gray-500">{t('loadingDashboardData')}</p>
        <Button onClick={loadDashboardData} className="mt-4">
          {t('refresh')}
        </Button>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">{t('enhancedVisibilityDashboard')}</h1>
          <p className="text-gray-600">{t('boostOnlinePresence')}</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={runSEOAudit} variant="outline" className="flex items-center gap-2">
            <Search className="w-4 h-4" />
            {t('runSEOAudit')}
          </Button>
          <Button onClick={loadDashboardData} variant="outline" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            {t('refresh')}
          </Button>
        </div>
      </div>

      {/* Overview Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('publishedReviews')}</CardTitle>
            <Star className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.overview.total_published_reviews}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">+{dashboardData.overview.monthly_growth.reviews}%</span> {t('fromLastMonth')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('averageRating')}</CardTitle>
            <Award className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.overview.average_rating}/5</div>
            <div className="flex items-center mt-1">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`w-3 h-3 ${
                    i < Math.floor(dashboardData.overview.average_rating)
                      ? 'text-yellow-500 fill-current'
                      : 'text-gray-300'
                  }`}
                />
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('seoScore')}</CardTitle>
            <Search className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.overview.seo_score}/100</div>
            <Progress value={dashboardData.overview.seo_score} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-1">
              <span className="text-green-600">+{dashboardData.overview.monthly_growth.seo}%</span> {t('thisMonth')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('aiVisibility')}</CardTitle>
            <Bot className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.overview.ai_visibility_score}/100</div>
            <Progress value={dashboardData.overview.ai_visibility_score} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-1">
              <span className="text-green-600">+{dashboardData.overview.monthly_growth.ai_mentions}%</span> {t('aiMentions')}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">{t('overview')}</TabsTrigger>
          <TabsTrigger value="reviews">{t('customerReviews')}</TabsTrigger>
          <TabsTrigger value="seo">{t('seoTools')}</TabsTrigger>
          <TabsTrigger value="content">{t('contentHub')}</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Platform Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Share2 className="w-5 h-5" />
                  {t('platformIntegrations')}
                </CardTitle>
                <CardDescription>{t('yourVisibilityAcrossPlatforms')}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {dashboardData?.platform_status?.ai_platforms?.map((platform) => (
                  <div key={platform.name} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`w-3 h-3 rounded-full ${
                        platform.status === 'active' ? 'bg-green-500' :
                        platform.status === 'pending' ? 'bg-yellow-500' : 'bg-gray-300'
                      }`} />
                      <span className="font-medium capitalize">
                        {platform.name.replace('_', ' ')}
                      </span>
                    </div>
                    <Badge variant={platform.status === 'active' ? 'default' : 'secondary'}>
                      {platform.status}
                    </Badge>
                  </div>
                )) || (
                  <div className="text-center text-gray-500 py-4">
                    {t('noPlatformIntegrationsConfiguredYet')}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Performance Trends */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="w-5 h-5" />
                  {t('performanceTrends')}
                </CardTitle>
                <CardDescription>{t('yourVisibilityGrowthOverTime')}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">{t('searchVisibility')}</span>
                    <span className="text-sm font-medium">+{dashboardData.overview.monthly_growth.search_visibility}%</span>
                  </div>
                  <Progress value={75} />
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm">{t('reviewEngagement')}</span>
                    <span className="text-sm font-medium">+{dashboardData.overview.monthly_growth.reviews}%</span>
                  </div>
                  <Progress value={68} />
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm">{t('aiMentions')}</span>
                    <span className="text-sm font-medium">+{dashboardData.overview.monthly_growth.ai_mentions}%</span>
                  </div>
                  <Progress value={82} />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="w-5 h-5" />
                {t('quickActions')}
              </CardTitle>
              <CardDescription>{t('boostYourVisibilityWithTheseRecommendedActions')}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button 
                  variant="outline" 
                  className="h-auto min-h-[120px] p-4 flex flex-col items-start gap-2 text-wrap"
                  onClick={async () => {
                    try {
                      const count = await PublicReviewService.autoPublishQualifyingReviews(restaurantId);
                      toast({
                        title: t('success'),
                        description: t('published {{count}} qualifying reviews automatically', { count }),
                      });
                      loadDashboardData();
                    } catch (error) {
                      console.error('Error auto-publishing reviews:', error);
                      // Graceful fallback - do nothing instead of showing error toast
                    }
                  }}
                >
                  <MessageSquare className="w-6 h-6 text-blue-500 flex-shrink-0" />
                  <div className="text-left w-full">
                    <div className="font-medium text-wrap break-words">{t('autoPublishReviews')}</div>
                    <div className="text-sm text-gray-600 text-wrap break-words leading-relaxed">{t('shareCustomerFeedbackWithAIAndGoogle')}</div>
                  </div>
                </Button>

                <Button 
                  variant="outline" 
                  className="h-auto min-h-[120px] p-4 flex flex-col items-start gap-2 text-wrap"
                  onClick={runSEOAudit}
                >
                  <Search className="w-6 h-6 text-green-500 flex-shrink-0" />
                  <div className="text-left w-full">
                    <div className="font-medium text-wrap break-words">{t('seoHealthCheck')}</div>
                    <div className="text-sm text-gray-600 text-wrap break-words leading-relaxed">{t('analyzeAndImproveSearchRankings')}</div>
                  </div>
                </Button>

                <Button 
                  variant="outline" 
                  className="h-auto min-h-[120px] p-4 flex flex-col items-start gap-2 text-wrap"
                  onClick={() => setActiveTab('content')}
                >
                  <BookOpen className="w-6 h-6 text-purple-500 flex-shrink-0" />
                  <div className="text-left w-full">
                    <div className="font-medium text-wrap break-words">{t('createContent')}</div>
                    <div className="text-sm text-gray-600 text-wrap break-words leading-relaxed">{t('generateAIoptimizedContent')}</div>
                  </div>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Customer Reviews Tab */}
        <TabsContent value="reviews" className="space-y-4">
          <CustomerReviewsManager restaurantId={restaurantId} onDataUpdate={loadDashboardData} />
        </TabsContent>

        {/* SEO Tab */}
        <TabsContent value="seo" className="space-y-4">
          <SEOToolsManager restaurantId={restaurantId} />
        </TabsContent>

        {/* Content Tab */}
        <TabsContent value="content" className="space-y-4">
          <ContentManager restaurantId={restaurantId} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

// Customer Reviews Management Component
const CustomerReviewsManager: React.FC<{
  restaurantId: string;
  onDataUpdate: () => void;
}> = ({ restaurantId, onDataUpdate }) => {
  const [eligibleFeedback, setEligibleFeedback] = useState<any[]>([]);
  const [publishedReviews, setPublishedReviews] = useState<any[]>([]);
  const [reviewSettings, setReviewSettings] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const { t } = useLanguage();

  useEffect(() => {
    loadReviewData();
  }, [restaurantId]);

  const loadReviewData = async () => {
    try {
      setLoading(true);
      const [feedback, reviews, settings] = await Promise.all([
        PublicReviewService.getEligibleCustomerFeedback(restaurantId, {
          notPublished: true,
          hasComments: true,
          minRating: 3,
          limit: 20
        }),
        PublicReviewService.getPublishedReviews(restaurantId, { limit: 10 }),
        PublicReviewService.getReviewSettings(restaurantId)
      ]);

      setEligibleFeedback(feedback || []);
      setPublishedReviews(reviews || []);
      setReviewSettings(settings);
    } catch (error) {
      console.error('Error loading review data:', error);
      // Graceful fallback - set empty arrays instead of showing error toast
      setEligibleFeedback([]);
      setPublishedReviews([]);
      setReviewSettings(null);
    } finally {
      setLoading(false);
    }
  };

  const publishReview = async (feedbackId: string) => {
    try {
      const feedback = eligibleFeedback.find(f => f.id === feedbackId);
      if (!feedback) return;

      const avgRating = PublicReviewService.calculateAverageRating(feedback);
      
      await PublicReviewService.createPublicReview({
        business_id: restaurantId, // Fixed: use business_id instead of restaurant_id
        feedback_id: feedbackId,
        display_name: feedback.customer_email ? 
          feedback.customer_email.split('@')[0] : 'Anonymous Customer',
        review_title: PublicReviewService.generateReviewTitle(feedback, avgRating),
        review_excerpt: feedback.comments || `Excellent ${avgRating}-star experience`, // Added required field
        is_featured: avgRating >= 4.5
      });

      toast({
        title: t('reviewPublished'),
        description: t('customerReviewHasBeenPublishedSuccessfully'),
      });

      loadReviewData();
      onDataUpdate();
    } catch (error) {
      console.error('Error publishing review:', error);
      // Graceful fallback - do nothing instead of showing error toast
    }
  };

  const shareWithAI = async (reviewIds: string[]) => {
    try {
      await PublicReviewService.shareWithAIPlatforms(restaurantId, reviewIds);
      
      toast({
        title: t('success'),
        description: t('shared {{count}} reviews with AI platforms for enhanced visibility', { count: reviewIds.length }),
      });

      loadReviewData();
    } catch (error) {
      console.error('Error sharing with AI platforms:', error);
      // Graceful fallback - do nothing instead of showing error toast
    }
  };

  const shareWithGoogle = async (reviewIds: string[]) => {
    try {
      await PublicReviewService.shareWithGoogleMyBusiness(restaurantId, reviewIds);
      
      toast({
        title: t('success'),
        description: t('shared {{count}} reviews with Google My Business', { count: reviewIds.length }),
      });

      loadReviewData();
    } catch (error) {
      console.error('Error sharing with Google:', error);
      // Graceful fallback - do nothing instead of showing error toast
    }
  };

  if (loading) {
    return <div className="space-y-4">
      {[...Array(3)].map((_, i) => (
        <div key={i} className="h-32 bg-gray-200 rounded animate-pulse"></div>
      ))}
    </div>;
  }

  return (
    <div className="space-y-6">
      {/* Review Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            {t('reviewSharingSettings')}
          </CardTitle>
          <CardDescription>{t('configureHowYourReviewsAreSharedAcrossPlatforms')}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">{t('autoPublishThreshold')}</label>
              <p className="text-sm text-gray-600">
                {t('reviewsWith {{threshold}}+ stars are automatically shared', { threshold: reviewSettings?.auto_publish_threshold || 4 })}
              </p>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">{t('moderation')}</label>
              <p className="text-sm text-gray-600">
                {reviewSettings?.moderation_required ? t('required') : t('automatic')} {t('reviewApproval')}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Eligible Customer Feedback */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                {t('customerFeedbackReadyToPublish')}
              </CardTitle>
              <CardDescription>
                {t('recentCustomerFeedbackThatCanBeSharedToBoostYourVisibility')}
              </CardDescription>
            </div>
            <Button 
              onClick={async () => {
                const count = await PublicReviewService.autoPublishQualifyingReviews(restaurantId);
                toast({
                  title: t('success'),
                  description: t('published {{count}} qualifying reviews', { count }),
                });
                loadReviewData();
                onDataUpdate();
              }}
              className="flex items-center gap-2"
            >
              <Zap className="w-4 h-4" />
              {t('autoPublishAll')}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {eligibleFeedback.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <MessageSquare className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>{t('noEligibleCustomerFeedbackFound')}</p>
              <p className="text-sm">{t('customerReviewsWillAppearHereOnceReceived')}</p>
            </div>
          ) : (
            <div className="space-y-4">
              {eligibleFeedback.slice(0, 5).map((feedback) => {
                const avgRating = PublicReviewService.calculateAverageRating(feedback);
                return (
                  <div key={feedback.id} className="border rounded-lg p-4 space-y-3">
                    <div className="flex justify-between items-start">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <div className="flex items-center">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`w-4 h-4 ${
                                  i < Math.floor(avgRating)
                                    ? 'text-yellow-500 fill-current'
                                    : 'text-gray-300'
                                }`}
                              />
                            ))}
                          </div>
                          <span className="text-sm font-medium">{avgRating.toFixed(1)}/5</span>
                          <Badge variant="outline" className="text-xs">
                            {new Date(feedback.created_at).toLocaleDateString()}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-700 line-clamp-2">{feedback.comments}</p>
                        <div className="text-xs text-gray-500 space-x-4">
                          <span>{t('food {{rating}}/5', { rating: feedback.food_rating })}</span>
                          <span>{t('service {{rating}}/5', { rating: feedback.service_rating })}</span>
                          <span>{t('app {{rating}}/5', { rating: feedback.app_rating })}</span>
                          {feedback.sentiment_score && (
                            <span>{t('sentiment {{score}}%', { score: (feedback.sentiment_score * 100).toFixed(0) })}</span>
                          )}
                        </div>
                      </div>
                      <Button
                        size="sm"
                        onClick={() => publishReview(feedback.id)}
                        className="flex items-center gap-1"
                      >
                        <Share2 className="w-3 h-3" />
                        {t('publish')}
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Published Reviews */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Globe className="w-5 h-5" />
                {t('publishedReviews')}
              </CardTitle>
              <CardDescription>
                {t('reviewsCurrentlySharedAcrossPlatformsForAISearchVisibility')}
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const publishedIds = publishedReviews
                    .filter(r => r.publication_status === 'published')
                    .map(r => r.id);
                  shareWithAI(publishedIds);
                }}
                className="flex items-center gap-1"
              >
                <Bot className="w-3 h-3" />
                {t('shareWithAI')}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const publishedIds = publishedReviews
                    .filter(r => r.publication_status === 'published')
                    .map(r => r.id);
                  shareWithGoogle(publishedIds);
                }}
                className="flex items-center gap-1"
              >
                <ExternalLink className="w-3 h-3" />
                {t('shareWithGoogle')}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {publishedReviews.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Globe className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>{t('noPublishedReviewsYet')}</p>
              <p className="text-sm">{t('publishedReviewsWillAppearHereForPlatformSharing')}</p>
            </div>
          ) : (
            <div className="space-y-4">
              {publishedReviews.map((review) => (
                <div key={review.id} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium">{review.review_title}</h4>
                        <Badge 
                          variant={review.publication_status === 'published' ? 'default' : 'secondary'}
                        >
                          {review.publication_status}
                        </Badge>
                        {review.is_featured && (
                          <Badge variant="outline" className="text-yellow-600">
                            <Award className="w-3 h-3 mr-1" />
                            {t('featured')}
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-700">{review.review_excerpt}</p>
                      <div className="text-xs text-gray-500 flex items-center gap-4">
                        <span>{t('by {{name}}', { name: review.display_name })}</span>
                        <span>{t('published {{date}}', { date: new Date(review.published_at || review.created_at).toLocaleDateString() })}</span>
                        <span>{t('views {{count}}', { count: review.view_count || 0 })}</span>
                      </div>
                      <div className="flex items-center gap-2 text-xs">
                        {review.google_my_business_posted && (
                          <Badge variant="outline" className="text-blue-600">
                            <ExternalLink className="w-3 h-3 mr-1" />
                            {t('google')}
                          </Badge>
                        )}
                        {review.social_media_posted && (
                          <Badge variant="outline" className="text-purple-600">
                            <Share2 className="w-3 h-3 mr-1" />
                            {t('social')}
                          </Badge>
                        )}
                        <Badge variant="outline" className="text-green-600">
                          <Bot className="w-3 h-3 mr-1" />
                          {t('aiReady')}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

// SEO Tools Management Component
const SEOToolsManager: React.FC<{
  restaurantId: string;
}> = ({ restaurantId }) => {
  const [seoData, setSeoData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const { t } = useLanguage();

  useEffect(() => {
    loadSEOData();
  }, [restaurantId]);

  const loadSEOData = async () => {
    try {
      setLoading(true);
      const [settings, latestAudit] = await Promise.all([
        SEOService.getSEOSettings(restaurantId),
        SEOService.getLatestAudit(restaurantId)
      ]);

      setSeoData({ settings, latestAudit });
    } catch (error) {
      console.error('Error loading SEO data:', error);
      // Graceful fallback - set null instead of showing error toast
      setSeoData(null);
    } finally {
      setLoading(false);
    }
  };

  const runSEOAudit = async () => {
    try {
      await SEOService.runSEOAudit({
        restaurant_id: restaurantId,
        audit_type: 'comprehensive'
      });
      
      toast({
        title: t('seoAuditStarted'),
        description: t('yourComprehensiveSEOAuditIsRunning'),
      });
      
      setTimeout(loadSEOData, 3000);
    } catch (error) {
      console.error('Error running SEO audit:', error);
      // Graceful fallback - do nothing instead of showing error toast
    }
  };

  if (loading) {
    return <div className="space-y-4">
      {[...Array(2)].map((_, i) => (
        <div key={i} className="h-32 bg-gray-200 rounded animate-pulse"></div>
      ))}
    </div>;
  }

  return (
    <div className="space-y-6">
      {/* SEO Overview */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Search className="w-5 h-5" />
                {t('seoPerformance')}
              </CardTitle>
              <CardDescription>{t('yourSearchEngineOptimizationStatus')}</CardDescription>
            </div>
            <Button onClick={runSEOAudit} className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4" />
              {t('runFullAudit')}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <label className="text-sm font-medium">{t('overallSEOScore')}</label>
              <div className="text-3xl font-bold">{seoData?.latestAudit?.overall_score || 0}/100</div>
              <Progress value={seoData?.latestAudit?.overall_score || 0} />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">{t('technicalSEO')}</label>
              <div className="text-2xl font-bold">{seoData?.latestAudit?.technical_seo_score || 0}/100</div>
              <Progress value={seoData?.latestAudit?.technical_seo_score || 0} />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">{t('localSEO')}</label>
              <div className="text-2xl font-bold">{seoData?.latestAudit?.local_seo_score || 0}/100</div>
              <Progress value={seoData?.latestAudit?.local_seo_score || 0} />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* SEO Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="w-5 h-5" />
            {t('seoRecommendations')}
          </CardTitle>
          <CardDescription>{t('actionsToImproveYourSearchRanking')}</CardDescription>
        </CardHeader>
        <CardContent>
          {seoData?.latestAudit?.recommendations?.length > 0 ? (
            <div className="space-y-4">
              {seoData.latestAudit.recommendations.slice(0, 5).map((rec: any, index: number) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-medium">{rec.title}</h4>
                    <Badge 
                      variant={rec.priority === 'critical' ? 'destructive' : 
                        rec.priority === 'high' ? 'default' : 'secondary'}
                    >
                      {rec.priority}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{rec.description}</p>
                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    <span>{t('impact {{score}}/100', { score: rec.impact_score })}</span>
                    <span>{t('effort {{effort}}', { effort: rec.effort_required })}</span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Search className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>{t('runAnSEOAuditToGetPersonalizedRecommendations')}</p>
              <Button onClick={runSEOAudit} className="mt-4">
                <BarChart3 className="w-4 h-4 mr-2" />
                {t('startSEOAudit')}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Schema Markup */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            {t('structuredDataAndSchema')}
          </CardTitle>
          <CardDescription>{t('helpSearchEnginesUnderstandYourRestaurant')}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm">{t('restaurantSchema')}</span>
              <Badge variant="default">Active</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">{t('reviewSchema')}</span>
              <Badge variant="default">Active</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">{t('menuSchema')}</span>
              <Badge variant="secondary">Pending</Badge>
            </div>
            <Button 
              variant="outline" 
              className="w-full"
              onClick={async () => {
                try {
                  await SEOService.generateSchemaMarkup(restaurantId);
                  toast({
                    title: t('success'),
                    description: t('schemaMarkupHasBeenUpdated'),
                  });
                } catch (error) {
                  console.error('Error updating schema markup:', error);
                  // Graceful fallback - do nothing instead of showing error toast
                }
              }}
            >
              <Settings className="w-4 h-4 mr-2" />
              {t('updateSchemaMarkup')}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Content Management Component
const ContentManager: React.FC<{
  restaurantId: string;
}> = ({ restaurantId }) => {
  const [content, setContent] = useState<any[]>([]);
  const [contentIdeas, setContentIdeas] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const { t } = useLanguage();

  useEffect(() => {
    loadContentData();
  }, [restaurantId]);

  const loadContentData = async () => {
    try {
      setLoading(true);
      const [publishedContent, ideas] = await Promise.all([
        ContentService.getContent(restaurantId, { status: 'published', limit: 10 }),
        ContentService.getContentIdeas(restaurantId)
      ]);

      setContent(publishedContent || []);
      setContentIdeas(ideas || []);
    } catch (error) {
      console.error('Error loading content data:', error);
      // Graceful fallback - set empty arrays instead of showing error toast
      setContent([]);
      setContentIdeas([]);
    } finally {
      setLoading(false);
    }
  };

  const createContent = async (idea: any) => {
    try {
      await ContentService.createContent({
        restaurant_id: restaurantId,
        content_type: idea.content_type || 'blog_post',
        title: idea.title,
        content: idea.description,
        excerpt: idea.description.substring(0, 200) + '...',
        tags: idea.suggested_keywords || [],
        status: 'draft'
      });

      toast({
        title: t('contentCreated'),
        description: t('newContentHasBeenCreatedAsADraft'),
      });

      loadContentData();
    } catch (error) {
      console.error('Error creating content:', error);
      // Graceful fallback - do nothing instead of showing error toast
    }
  };

  if (loading) {
    return <div className="space-y-4">
      {[...Array(2)].map((_, i) => (
        <div key={i} className="h-32 bg-gray-200 rounded animate-pulse"></div>
      ))}
    </div>;
  }

  return (
    <div className="space-y-6">
      {/* Published Content */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="w-5 h-5" />
                {t('publishedContent')}
              </CardTitle>
              <CardDescription>{t('yourLatestContentForAISearchVisibility')}</CardDescription>
            </div>
            <Button className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              {t('createNew')}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {content.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <BookOpen className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>{t('noPublishedContentYet')}</p>
              <p className="text-sm">{t('createContentToImproveYourOnlinePresence')}</p>
            </div>
          ) : (
            <div className="space-y-4">
              {content.map((item) => (
                <div key={item.id} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-medium">{item.title}</h4>
                    <Badge variant="outline">{item.content_type}</Badge>
                  </div>
                  {item.excerpt && (
                    <p className="text-sm text-gray-600 mb-2">{item.excerpt}</p>
                  )}
                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    <span className="flex items-center gap-1">
                      <Eye className="w-3 h-3" />
                      {item.view_count || 0} {t('views')}
                    </span>
                    <span>{t('published {{date}}', { date: new Date(item.published_at || '').toLocaleDateString() })}</span>
                    <span>{t('engagement {{score}}%', { score: item.engagement_score || 0 })}</span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Content Ideas */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="w-5 h-5" />
            {t('aiGeneratedContentIdeas')}
          </CardTitle>
          <CardDescription>{t('smartContentSuggestionsToBoostYourVisibility')}</CardDescription>
        </CardHeader>
        <CardContent>
          {contentIdeas.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Target className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>{t('noContentIdeasAvailable')}</p>
              <p className="text-sm">{t('aiWillGenerateIdeasBasedOnYourRestaurantData')}</p>
            </div>
          ) : (
            <div className="space-y-4">
              {contentIdeas.slice(0, 5).map((idea, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-medium text-sm">{idea.title}</h4>
                    <div className="flex gap-2">
                      <Badge 
                        variant={idea.estimated_impact === 'high' ? 'default' : 
                          idea.estimated_impact === 'medium' ? 'secondary' : 'outline'}
                        className="text-xs"
                      >
                        {idea.estimated_impact} {t('impact')}
                      </Badge>
                      <Button 
                        size="sm" 
                        onClick={() => createContent(idea)}
                        className="text-xs px-2 py-1 h-auto"
                      >
                        {t('create')}
                      </Button>
                    </div>
                  </div>
                  <p className="text-xs text-gray-600 mb-2">{idea.description}</p>
                  <div className="flex flex-wrap gap-1 mt-2">
                    {idea.suggested_keywords?.slice(0, 3).map((keyword: string, idx: number) => (
                      <Badge key={idx} variant="outline" className="text-xs">
                        {keyword}
                      </Badge>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}; 