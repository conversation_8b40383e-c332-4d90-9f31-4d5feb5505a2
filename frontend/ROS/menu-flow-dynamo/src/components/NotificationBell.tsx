import React, { useState, useEffect, useRef } from 'react';
import { Bell } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { useRestaurant } from '@/contexts/RestaurantContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { fetchNotifications, setupPushNotifications, checkNotificationsTableExists, resetNotificationErrorFlag, autoCleanupNotifications } from '@/services/notificationService';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import ErrorBoundary from './ErrorBoundary';
import { useNavigate } from 'react-router-dom';
import { markAsReadNotification } from '@/services/notificationService';
import { RealtimeChannel } from '@supabase/supabase-js';
import { useRobustRealtime } from '@/hooks/useRobustRealtime';
import { realtimeDebugger } from '@/services/realtimeDebugger';
import { RealtimeStatusIndicator } from '@/components/RealtimeStatusIndicator';
// import ConnectionManager from '@/services/connectionManager'; // DISABLED during emergency fix

export function NotificationBell() {
  return (
    <ErrorBoundary>
      <NotificationBellComponent />
    </ErrorBoundary>
  );
}

function NotificationBellComponent() {
  const { user } = useAuth();
  const { restaurantInfo } = useRestaurant();
  const { t } = useLanguage();
  const queryClient = useQueryClient();
  const [showDropdown, setShowDropdown] = useState(false);
  const navigate = useNavigate();
  const orderAudio = useRef(new Audio('/sounds/new-order.mp3'));

  // Initialize audio settings and enable on user interaction
  useEffect(() => {
    // Configure audio
    orderAudio.current.volume = 0.5;
    orderAudio.current.preload = 'auto';

    // Enable audio permissions silently on first user interaction (Safari-compatible)
    const enableAudio = async () => {
      console.log('🔔 🎯 USER INTERACTION DETECTED - Enabling audio for Safari');
      try {
        // For Safari: Need to actually play and pause to unlock audio
        orderAudio.current.volume = 0; // Silent
        orderAudio.current.currentTime = 0;
        await orderAudio.current.play();
        orderAudio.current.pause();
        orderAudio.current.volume = 0.5; // Restore volume
        orderAudio.current.currentTime = 0;
        console.log('🔔 ✅ SAFARI AUDIO PERMISSIONS ENABLED');
      } catch (error) {
        console.log('🔔 ❌ Safari audio permission failed:', error);
        // Fallback: just load
        orderAudio.current.load();
      }
    };

    // Add interaction listeners (once only)
    document.addEventListener('click', enableAudio, { once: true });
    document.addEventListener('keydown', enableAudio, { once: true });
    document.addEventListener('touchstart', enableAudio, { once: true });

    return () => {
      // Cleanup listeners
      document.removeEventListener('click', enableAudio);
      document.removeEventListener('keydown', enableAudio);
      document.removeEventListener('touchstart', enableAudio);
    };
  }, []);

  // 🚨 EMERGENCY RECURSION PREVENTION - Check at the beginning of component logic
  if ((window as any).emergencyMode) {

    return <div className="relative"><button className="h-9 w-9 rounded-full bg-red-100" disabled title="Emergency Mode">🚨</button></div>;
  }

  useEffect(() => {
    orderAudio.current.volume = 0.5;
    orderAudio.current.load();

    let audioEnabled = false;

    // Safari-compatible audio enablement without any sound
    const enableAudio = () => {
      if (audioEnabled) return; // Only enable once

      try {
        // Create a silent audio context to enable audio without playing sound
        // This works better in Safari than playing at volume 0
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();

        // Create a silent buffer (0.1 seconds of silence)
        const buffer = audioContext.createBuffer(1, audioContext.sampleRate * 0.1, audioContext.sampleRate);
        const source = audioContext.createBufferSource();
        source.buffer = buffer;
        source.connect(audioContext.destination);

        // Play the silent audio to unlock the audio context
        source.start(0);

        audioEnabled = true;

        // Close the context to free resources
        setTimeout(() => {
          audioContext.close();
        }, 200);
      } catch (error) {
        // Fallback for browsers that don't support AudioContext

        // Only as last resort, and only if not Safari
        const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
        if (!isSafari) {
          const originalVolume = orderAudio.current.volume;
          orderAudio.current.volume = 0;
          orderAudio.current.play().then(() => {
            orderAudio.current.pause();
            orderAudio.current.currentTime = 0;
            orderAudio.current.volume = originalVolume;
            audioEnabled = true;

          }).catch(() => {

          });
        } else {
          // For Safari, just mark as enabled without playing anything
          audioEnabled = true;

        }
      }
    };

    // Listen for any user interaction to enable audio (only once)
    const events = ['click', 'touchstart', 'keydown'];
    events.forEach(event => {
      document.addEventListener(event, enableAudio, { once: true });
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, enableAudio);
      });
    };
  }, []);

  // Removed auto-read on dropdown open - notifications should only be marked as read when clicked

  // Setup notification permissions and auto-cleanup when component mounts
  useEffect(() => {
    if (user && 'Notification' in window && Notification.permission === 'default') {
      resetNotificationErrorFlag();
      setupPushNotifications()
        .then(granted => {

        });
    }

    // Run auto-cleanup once when component mounts
    if (user?.id) {
      autoCleanupNotifications(user.id)
        .then(() => {
          // Auto-cleanup completed silently
        })
        .catch(err => console.error('Auto-cleanup failed:', err));
    }
  }, [user]);

  // Fetch notifications from the database
  const { data: notifications = [], refetch } = useQuery({
    queryKey: ['notifications', user?.id, restaurantInfo?.id],
    queryFn: async () => {
      const notifs = await fetchNotifications(user);

      return notifs;
    },
    refetchInterval: false, // CRITICAL FIX: Disable polling, use realtime instead
    staleTime: 0, // CRITICAL FIX: No cache staleness - always fresh data
    enabled: !!user && !!restaurantInfo?.id
  });

  // Get count of unread notifications (all types)
  const unreadCount = notifications.filter(n => !n.read).length;

  // REMOVED: Problematic useEffect sound logic that was playing on every count change
  // Sound should ONLY play from real-time listener for truly NEW notifications

  // Enhanced audio play function with better error handling
  const playNotificationSound = async () => {
    try {
      console.log('🔔 Attempting to play notification sound...');

      // Reset audio to beginning
      orderAudio.current.currentTime = 0;

      // Try to play the audio
      await orderAudio.current.play();
      console.log('🔔 ✅ Notification sound played successfully');

    } catch (error) {
      console.log('🔔 ❌ Audio play failed:', error);

      // Check if it's an interaction error
      if (error instanceof DOMException && error.name === 'NotAllowedError') {
        console.log('🔔 Audio blocked - user interaction required');

        // Show a visual notification that sound is blocked
        if ('Notification' in window && Notification.permission === 'granted') {
          new Notification('New Order Received!', {
            body: 'Sound is blocked. Click to enable audio notifications.',
            icon: '/favicon.ico'
          });
        }
      }
    }
  };

  // 🚨 ROBUST REAL-TIME: Enhanced notification system with fallback
  const { isConnected, usePolling, testConnection, forcePolling } = useRobustRealtime({
    table: 'notifications',
    filter: restaurantInfo?.id ? `restaurant_id=eq.${restaurantInfo.id}` : undefined,
    pollInterval: 3000, // 3 second fallback polling
    onUpdate: (payload) => {
      console.log('🔔 ✅ ROBUST NOTIFICATION UPDATE:', payload);

      // CRITICAL FIX: Instant cache invalidation and refetch
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      queryClient.refetchQueries({ queryKey: ['notifications'] });
      refetch();

      // Play sound for order and staff request notifications
      if (payload.new && (payload.new.type === 'order' || payload.new.type === 'staff_request')) {
        console.log('🔔 🎵 PLAYING SOUND for notification type:', payload.new.type);

        // Force audio play with multiple attempts for Safari
        const playAudio = async () => {
          try {
            // Ensure audio is ready
            if (orderAudio.current.readyState < 2) {
              console.log('🔔 Audio not ready, loading...');
              orderAudio.current.load();
              await new Promise(resolve => {
                orderAudio.current.addEventListener('canplaythrough', resolve, { once: true });
              });
            }

            orderAudio.current.currentTime = 0;
            await orderAudio.current.play();
            console.log('🔔 ✅ SOUND PLAYED SUCCESSFULLY');

          } catch (error) {
            console.log('🔔 ❌ Primary audio failed:', error);

            // Safari fallback: Create new audio instance
            try {
              const fallbackAudio = new Audio('/sounds/new-order.mp3');
              fallbackAudio.volume = 0.5;
              await fallbackAudio.play();
              console.log('🔔 ✅ FALLBACK SOUND PLAYED');
            } catch (fallbackError) {
              console.log('🔔 ❌ Fallback audio also failed:', fallbackError);

              // Show visual notification as last resort
              if ('Notification' in window && Notification.permission === 'granted') {
                new Notification('New Order Received!', {
                  body: payload.new?.message || 'A new order has been placed',
                  icon: '/favicon.ico'
                });
              }
            }
          }
        };

        playAudio();
      }
    },
    onError: (error) => {
      console.error('🔔 ❌ ROBUST NOTIFICATION ERROR:', error);
    }
  });

  // Debug panel for development
  useEffect(() => {
    if (import.meta.env.DEV) {
      console.log('🔔 🔧 NOTIFICATION SYSTEM STATUS:', {
        isConnected,
        usePolling,
        restaurantId: restaurantInfo?.id,
        userId: user?.id
      });
    }
  }, [isConnected, usePolling, restaurantInfo?.id, user?.id]);

  // DISABLED EMERGENCY CODE:
  /*
  // DISABLED: Real-time notification listener causing stack overflow
  useEffect(() => {
    if (!user || !restaurantInfo?.id) return;

    const connectionManager = ConnectionManager.getInstance();
    let channel: RealtimeChannel | null = null;
    let isSetupComplete = false;

    const setupNotificationChannel = () => {
      // Prevent multiple setups
      if (isSetupComplete) {

        return;
      }

      try {
        // Create a stable channel reference without timestamp
        const channelName = `notifications-${restaurantInfo.id}`;

        // Check if channel is disabled by circuit breaker
        if (connectionManager.isChannelDisabled(channelName)) {

          return;
        }

        channel = connectionManager.getChannel(channelName)
          .on(
            'postgres_changes',
            {
              event: 'INSERT',
              schema: 'public',
              table: 'notifications',
              filter: `restaurant_id=eq.${restaurantInfo.id}`
            },
            (payload) => {

              if (payload.new) {

                // Play sound for order and staff request notifications
                if (payload.new.type === 'order' || payload.new.type === 'staff_request') {

                  // Force audio play with user interaction check
                  setTimeout(() => {
                    playNotificationSound().catch(err => {

                      // Fallback: try to play with a new audio instance
                      const audio = new Audio('/sounds/new-order.mp3');
                      audio.volume = 0.5;
                      audio.play().catch(e => console.log('🔔 ❌ Fallback audio also failed:', e));
                    });
                  }, 100);
                }

                // Refresh notifications list with debouncing

                setTimeout(() => refetch(), 500); // Debounce the refetch
              }
            }
          )
          .subscribe((status) => {

            if (status === 'SUBSCRIBED') {
              isSetupComplete = true;

            }

            // Handle connection errors - RESPECT CIRCUIT BREAKER
            if (status === 'CHANNEL_ERROR' || status === 'CLOSED') {

              // Mark setup as incomplete so it can be retried manually
              isSetupComplete = false;
            }
          });

      } catch (error) {

        // Do NOT retry automatically to prevent infinite connection spam

        console.error('🔔 🔧 To manually retry: window.connectionManager.forceResetCircuitBreaker("notifications-[restaurant-id]")');

        // Let the circuit breaker handle retry logic instead of creating our own timers
      }
    };

    // Initial setup
    setupNotificationChannel();

    return () => {

      if (channel) {
        try {
          const channelName = `notifications-${restaurantInfo.id}`;
          connectionManager.removeChannel(channelName);
        } catch (error) {

        }
      }
    };
  }, [user?.id, restaurantInfo?.id]); // Removed refetch from dependencies to prevent recreation
  */

  const handleNotificationClick = async (notif: any) => {
    try {

      // Only mark as read if it's not already read
      if (!notif.read) {

        // Optimistic update: immediately update the local state
        const updatedNotifications = notifications.map(n =>
          n.id === notif.id ? { ...n, read: true } : n
        );

        // Update the query cache optimistically for instant UI feedback
        queryClient.setQueryData(['notifications', user?.id, restaurantInfo?.id], updatedNotifications);

        // Then update the database
        const success = await markAsReadNotification(notif.id);
        if (success) {

          // Invalidate and refetch to ensure consistency across all components
          await queryClient.invalidateQueries({ queryKey: ['notifications'] });

        } else {

          // Revert optimistic update on failure by refetching original data
          await queryClient.invalidateQueries({ queryKey: ['notifications'] });
          await refetch();

        }
      } else {
        console.log('🔔 Notification already read');
      }

      setShowDropdown(false);

      // Navigate based on notification type
      if (notif.type === 'staff_request') {

        navigate('/admin/staff-requests');
      } else if (notif.type === 'order') {
        // Derive order ID from payload or action_url
        let orderId = notif.payload?.order_id || notif.order_id;
        if (!orderId && notif.action_url) {
          const parts = notif.action_url.split('?');
          if (parts[1]) {
            const params = new URLSearchParams(parts[1]);
            orderId = params.get('orderId');
          }
        }

        navigate(`/admin/orders${orderId ? `?orderId=${orderId}` : ''}`);
      } else if (notif.action_url) {

        navigate(notif.action_url);
      } else {

        navigate('/admin/notifications');
      }
    } catch (error) {
      console.error('Error handling notification click:', error);
    }
  };

  // Test function for audio
  const testAudio = async () => {
    try {
      await playNotificationSound();
    } catch (err) {
      console.error('Test audio error:', err);
    }
  };

  // Test real-time connection
  const testRealtime = async () => {
    console.log('🔔 🧪 TESTING REAL-TIME CONNECTION...');
    
    if (restaurantInfo?.id) {
      // Insert a test notification
      const success = await realtimeDebugger.insertTestNotification(restaurantInfo.id);
      if (success) {
        console.log('🔔 ✅ TEST NOTIFICATION INSERTED');
      }
      
      // Test connection
      const health = await testConnection();
      console.log('🔔 🧪 CONNECTION HEALTH:', health);
    }
  };

  return (
    <div className="relative">
      <Button
        variant="ghost"
        size="icon"
        className="relative h-9 w-9 rounded-full hover:bg-sky-100 dark:hover:bg-sky-900/30"
        onClick={() => setShowDropdown(!showDropdown)}
        onDoubleClick={testAudio} // Double-click to test audio
        onContextMenu={(e) => { // Right-click to test real-time
          e.preventDefault();
          testRealtime();
        }}
        aria-label="Notifications"
        title={`Notifications ${isConnected ? '(Connected)' : usePolling ? '(Polling)' : '(Offline)'}`}
      >
        <Bell className="h-5 w-5 text-gray-700 dark:text-gray-300" />
        
        {/* Connection status indicator */}
        <RealtimeStatusIndicator className="absolute top-0 left-0" />
        
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-sky-600 text-[10px] text-white font-medium">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </Button>

      {/* Notification dropdown */}
      {showDropdown && (
        <div className="absolute right-0 mt-2 w-80 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-50">
          <div className="p-3 border-b border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-center">
              <h3 className="text-sm font-medium">{t('notificationsBellTitle')}</h3>
              {unreadCount > 0 && (
                <span className="text-xs bg-sky-100 text-sky-800 dark:bg-sky-900 dark:text-sky-200 px-2 py-0.5 rounded-full">
                  {unreadCount} {t('newNotifications')}
                </span>
              )}
            </div>
          </div>

          <div className="max-h-72 overflow-y-auto">
            {notifications.length > 0 ? (
              <div className="py-1">
                {notifications.slice(0, 5).map((notification) => (
                  <div
                    key={notification.id}
                    onClick={() => handleNotificationClick(notification)}
                    className={`cursor-pointer px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700 ${!notification.read ? 'bg-sky-50 dark:bg-sky-900/20' : ''}`}
                  >
                    <div className="flex items-start">
                      <div className="ml-3 w-0 flex-1">
                        <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {notification.title}
                        </p>
                        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                          {notification.message}
                        </p>
                        <div className="mt-1 flex space-x-1">
                          <p className="text-xs text-gray-400">
                            {new Date(notification.timestamp).toLocaleTimeString()}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="px-4 py-6 text-center text-sm text-gray-500 dark:text-gray-400">
                {t('noNotifications')}
              </div>
            )}
          </div>

          <div className="border-t border-gray-200 dark:border-gray-700 p-2">
            <button
              className="w-full px-4 py-2 text-xs text-center text-sky-600 dark:text-sky-400 hover:text-sky-800 dark:hover:text-sky-300"
              onClick={() => {
                setShowDropdown(false);
                navigate('/admin/notifications');
              }}
            >
              {t('viewAllNotifications')}
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
