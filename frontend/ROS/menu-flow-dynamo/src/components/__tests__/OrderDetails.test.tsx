import React from 'react';
import { render, screen } from '@testing-library/react';
import { vi } from 'vitest';
import OrderDetails from '../OrderDetails';

// Mock dependencies
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn(() => Promise.resolve({
            data: null,
            error: null
          }))
        }))
      })),
      update: vi.fn(() => ({
        eq: vi.fn(() => Promise.resolve({
          data: null,
          error: null
        }))
      }))
    }))
  }
}));

vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn()
  }
}));

const mockOrder = {
  id: 'test-order-id',
  restaurant_id: 'test-restaurant-id',
  table_id: 'test-table-id',
  status: 'pending',
  total_amount: 25.50,
  created_at: '2025-07-07T12:00:00Z',
  updated_at: '2025-07-07T12:00:00Z',
  restaurant_tables: {
    table_number: '5'
  },
  order_items: [
    {
      id: 'item-1',
      quantity: 2,
      unit_price: 12.75,
      menu_items: {
        id: 'menu-item-1',
        name: 'Test Item',
        description: 'Test Description',
        category: 'food',
        current_price: 12.75,
        image_url: null
      }
    }
  ]
};

describe('OrderDetails - Special Instructions Parsing', () => {
  it('should display special instructions when present', () => {
    const orderWithInstructions = {
      ...mockOrder,
      special_requests: 'ORDER_NUMBER:123\n\nSPECIAL INSTRUCTIONS:\nmuy hecho... porfavor!'
    };

    render(
      <OrderDetails
        orderId="test-order-id"
        isOpen={true}
        onClose={() => {}}
        order={orderWithInstructions}
      />
    );

    expect(screen.getByText('Special Instructions')).toBeInTheDocument();
    expect(screen.getByText('muy hecho... porfavor!')).toBeInTheDocument();
  });

  it('should display special instructions with tracking metadata filtered out', () => {
    const orderWithTrackingMetadata = {
      ...mockOrder,
      special_requests: 'ORDER_NUMBER:123\n\nSPECIAL INSTRUCTIONS:\nmuy hecho... porfavor!\n\n[TRACKING:RT-20250707-123|MODE:individual|SESSION:test-session]'
    };

    render(
      <OrderDetails
        orderId="test-order-id"
        isOpen={true}
        onClose={() => {}}
        order={orderWithTrackingMetadata}
      />
    );

    expect(screen.getByText('Special Instructions')).toBeInTheDocument();
    expect(screen.getByText('muy hecho... porfavor!')).toBeInTheDocument();
    expect(screen.queryByText(/TRACKING:/)).not.toBeInTheDocument();
  });

  it('should show "No special instructions provided" when only order number exists', () => {
    const orderWithoutInstructions = {
      ...mockOrder,
      special_requests: 'ORDER_NUMBER:123'
    };

    render(
      <OrderDetails
        orderId="test-order-id"
        isOpen={true}
        onClose={() => {}}
        order={orderWithoutInstructions}
      />
    );

    expect(screen.getByText('Special Instructions')).toBeInTheDocument();
    expect(screen.getByText('No special instructions provided')).toBeInTheDocument();
  });

  it('should show "No special instructions provided" when special instructions section is empty', () => {
    const orderWithEmptyInstructions = {
      ...mockOrder,
      special_requests: 'ORDER_NUMBER:123\n\nSPECIAL INSTRUCTIONS:\n   \n\n[TRACKING:RT-20250707-123]'
    };

    render(
      <OrderDetails
        orderId="test-order-id"
        isOpen={true}
        onClose={() => {}}
        order={orderWithEmptyInstructions}
      />
    );

    expect(screen.getByText('Special Instructions')).toBeInTheDocument();
    expect(screen.getByText('No special instructions provided')).toBeInTheDocument();
  });

  it('should display raw content when no system metadata is present', () => {
    const orderWithRawInstructions = {
      ...mockOrder,
      special_requests: 'Please make it extra spicy!'
    };

    render(
      <OrderDetails
        orderId="test-order-id"
        isOpen={true}
        onClose={() => {}}
        order={orderWithRawInstructions}
      />
    );

    expect(screen.getByText('Special Instructions')).toBeInTheDocument();
    expect(screen.getByText('Please make it extra spicy!')).toBeInTheDocument();
  });

  it('should not display special instructions section when special_requests is null', () => {
    const orderWithoutSpecialRequests = {
      ...mockOrder,
      special_requests: null
    };

    render(
      <OrderDetails
        orderId="test-order-id"
        isOpen={true}
        onClose={() => {}}
        order={orderWithoutSpecialRequests}
      />
    );

    expect(screen.queryByText('Special Instructions')).not.toBeInTheDocument();
  });
});
