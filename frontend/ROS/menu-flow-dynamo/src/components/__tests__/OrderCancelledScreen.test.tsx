import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { vi } from 'vitest';
import OrderCancelledScreen from '../OrderCancelledScreen';

// Mock the language context
const mockLanguageContext = {
  t: (key: string) => key,
  language: 'en',
  setLanguage: vi.fn(),
};

vi.mock('@/contexts/LanguageContext', () => ({
  useLanguage: () => mockLanguageContext,
}));

// Mock the navigate hook
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

// Mock StaffRequestButton
vi.mock('../StaffRequestButton', () => ({
  default: ({ children, onClick }: { children: React.ReactNode; onClick?: () => void }) => (
    <button onClick={onClick} data-testid="staff-request-button">
      {children}
    </button>
  ),
}));

// Mock the collaborative order tracking service
vi.mock('@/services/collaborativeOrderTrackingService', () => ({
  CollaborativeOrderTrackingService: {
    clearFromSessionStorage: vi.fn()
  }
}));

// Mock sessionStorage
const mockSessionStorage = {
  removeItem: vi.fn(),
  getItem: vi.fn(),
  setItem: vi.fn(),
  clear: vi.fn()
};

Object.defineProperty(window, 'sessionStorage', {
  value: mockSessionStorage
});

const defaultProps = {
  orderId: 'test-order-123',
  orderNumber: '123',
  tableId: 'table-456',
  restaurantId: 'restaurant-789',
  cancelledAt: '2025-07-07T12:00:00Z',
  totalAmount: 25.50,
};

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('OrderCancelledScreen', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Session Storage Cleanup', () => {
    it('should clear session storage when component mounts', () => {
      renderWithRouter(<OrderCancelledScreen {...defaultProps} />);

      // Verify that session storage items are cleared
      expect(mockSessionStorage.removeItem).toHaveBeenCalledWith('currentOrderId');
      expect(mockSessionStorage.removeItem).toHaveBeenCalledWith('orderNumber');
      expect(mockSessionStorage.removeItem).toHaveBeenCalledWith('trackingNumber');
      expect(mockSessionStorage.removeItem).toHaveBeenCalledWith('estimatedTime');
      expect(mockSessionStorage.removeItem).toHaveBeenCalledWith('restaurantId');
      expect(mockSessionStorage.removeItem).toHaveBeenCalledWith('cartItems');
      expect(mockSessionStorage.removeItem).toHaveBeenCalledWith('smartCartData');
      expect(mockSessionStorage.removeItem).toHaveBeenCalledWith('cartData');
      expect(mockSessionStorage.removeItem).toHaveBeenCalledWith('smartCart_individual');
      expect(mockSessionStorage.removeItem).toHaveBeenCalledWith('orderComment');
    });

    it('should clear session storage when "Browse Menu Again" button is clicked', () => {
      renderWithRouter(<OrderCancelledScreen {...defaultProps} />);

      // Clear the mock calls from component mount
      vi.clearAllMocks();

      // Click the "Browse Menu Again" button
      const browseMenuButton = screen.getByText('Browse Menu Again');
      fireEvent.click(browseMenuButton);

      // Verify that session storage is cleared again
      expect(mockSessionStorage.removeItem).toHaveBeenCalledWith('currentOrderId');
      expect(mockSessionStorage.removeItem).toHaveBeenCalledWith('orderNumber');
      expect(mockSessionStorage.removeItem).toHaveBeenCalledWith('trackingNumber');
      expect(mockSessionStorage.removeItem).toHaveBeenCalledWith('estimatedTime');
      expect(mockSessionStorage.removeItem).toHaveBeenCalledWith('restaurantId');

      // Verify navigation occurs
      expect(mockNavigate).toHaveBeenCalledWith('/menu?table=table-456');
    });
  });

  it('renders cancellation notice with order details', () => {
    renderWithRouter(<OrderCancelledScreen {...defaultProps} />);
    
    expect(screen.getByText('Order Cancelled')).toBeInTheDocument();
    expect(screen.getByText('Order #123')).toBeInTheDocument();
    expect(screen.getByText(/Cancelled on/)).toBeInTheDocument();
  });

  it('displays refund amount when provided', () => {
    renderWithRouter(<OrderCancelledScreen {...defaultProps} />);
    
    expect(screen.getByText('Refund Amount:')).toBeInTheDocument();
    expect(screen.getByText('€25.50')).toBeInTheDocument();
  });

  it('shows cancellation information and next steps', () => {
    renderWithRouter(<OrderCancelledScreen {...defaultProps} />);
    
    expect(screen.getByText('Your order has been cancelled by the restaurant')).toBeInTheDocument();
    expect(screen.getByText('What happens next?')).toBeInTheDocument();
    expect(screen.getByText(/refund will be processed automatically/)).toBeInTheDocument();
  });

  it('renders action buttons', () => {
    renderWithRouter(<OrderCancelledScreen {...defaultProps} />);
    
    expect(screen.getByText('Browse Menu Again')).toBeInTheDocument();
    expect(screen.getByTestId('staff-request-button')).toBeInTheDocument();
  });

  it('calls onBackToMenu when back button is clicked', () => {
    const mockOnBackToMenu = vi.fn();
    renderWithRouter(
      <OrderCancelledScreen {...defaultProps} onBackToMenu={mockOnBackToMenu} />
    );
    
    const backButton = screen.getByText('Back to Menu');
    fireEvent.click(backButton);
    
    expect(mockOnBackToMenu).toHaveBeenCalledTimes(1);
  });

  it('navigates to menu when Browse Menu Again is clicked', () => {
    renderWithRouter(<OrderCancelledScreen {...defaultProps} />);
    
    const browseMenuButton = screen.getByText('Browse Menu Again');
    fireEvent.click(browseMenuButton);
    
    expect(mockNavigate).toHaveBeenCalledWith('/menu?table=table-456');
  });

  it('handles missing optional props gracefully', () => {
    const minimalProps = {
      orderId: 'test-order-123',
    };
    
    renderWithRouter(<OrderCancelledScreen {...minimalProps} />);
    
    expect(screen.getByText('Order Cancelled')).toBeInTheDocument();
    expect(screen.getByText(/Order #test-ord/)).toBeInTheDocument();
  });

  it('formats cancellation time correctly', () => {
    renderWithRouter(<OrderCancelledScreen {...defaultProps} />);
    
    // Check that the date is formatted (exact format may vary by locale)
    expect(screen.getByText(/Cancelled on/)).toBeInTheDocument();
  });

  it('shows staff request button with custom message when table and restaurant IDs are provided', () => {
    renderWithRouter(<OrderCancelledScreen {...defaultProps} />);
    
    const staffButton = screen.getByTestId('staff-request-button');
    expect(staffButton).toBeInTheDocument();
    expect(screen.getByText('Request Staff Assistance')).toBeInTheDocument();
  });

  it('does not show staff request button when table or restaurant ID is missing', () => {
    const propsWithoutTable = {
      ...defaultProps,
      tableId: undefined,
    };
    
    renderWithRouter(<OrderCancelledScreen {...propsWithoutTable} />);
    
    expect(screen.queryByTestId('staff-request-button')).not.toBeInTheDocument();
  });
});
