
import React, { useEffect } from 'react';
import AdminSidebar from './AdminSidebar';
import { setupPushNotifications } from '@/services/notificationService';
import { NotificationBell } from './NotificationBell';
import { useAuth } from '@/contexts/AuthContext';

interface AdminLayoutProps {
  children: React.ReactNode;
  title: string;
  description?: string;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children, title, description }) => {
  const { user } = useAuth();

  // Initialize push notifications when admin layout mounts
  useEffect(() => {
    if (user && 'Notification' in window && Notification.permission === 'default') {
      setupPushNotifications()
        .then(granted => {
          // Push notifications setup completed
        });
    }
  }, [user]);
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex">
        <AdminSidebar />

        <main className="flex-1 relative">
          <div className="absolute top-4 right-4 z-10">
            <NotificationBell />
          </div>
          <div className="p-6">
            <header className="mb-6">
              <h1 className="text-2xl font-bold mb-2">{title}</h1>
              {description && <p className="text-restaurant-muted">{description}</p>}
            </header>

            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
