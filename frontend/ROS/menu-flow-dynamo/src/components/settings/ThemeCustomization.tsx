import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { Palette, Eye, Save, RotateCcw, Sparkles } from 'lucide-react';
import { useRestaurant } from '@/contexts/RestaurantContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { themeService } from '@/services/themeService';
import { ThemeAwareCard } from '@/components/theme/ThemeAwareCard';
import { ThemeAwareButton } from '@/components/theme/ThemeAwareButton';
import { useRestaurantTheme } from '@/hooks/useRestaurantTheme';
import { LogoUploader } from '@/components/LogoUploader';
import type { RestaurantTheme } from '@/types/theme';
import { supabase } from '@/integrations/supabase/client';

// Preset themes for easy selection
const PRESET_THEMES = [
  {
    id: 'modern',
    name: 'Modern',
    description: 'Clean and contemporary with sky blue accents',
    colors: {
      primary: '#0ea5e9',
      secondary: '#64748b',
      accent: '#f59e0b',
      background: '#ffffff',
      cardBackground: '#f8fafc',
      textPrimary: '#1e293b',
      textSecondary: '#64748b'
    }
  },
  {
    id: 'classic',
    name: 'Classic',
    description: 'Timeless elegance with warm earth tones',
    colors: {
      primary: '#8b5a3c',
      secondary: '#6b7280',
      accent: '#d97706',
      background: '#fefefe',
      cardBackground: '#f9fafb',
      textPrimary: '#374151',
      textSecondary: '#6b7280'
    }
  },
  {
    id: 'vibrant',
    name: 'Vibrant',
    description: 'Bold and energetic with bright accents',
    colors: {
      primary: '#e11d48',
      secondary: '#7c3aed',
      accent: '#f59e0b',
      background: '#ffffff',
      cardBackground: '#fef2f2',
      textPrimary: '#1f2937',
      textSecondary: '#6b7280'
    }
  },
  {
    id: 'dark',
    name: 'Dark',
    description: 'Sophisticated dark theme with premium feel',
    colors: {
      primary: '#3b82f6',
      secondary: '#6366f1',
      accent: '#10b981',
      background: '#1f2937',
      cardBackground: '#374151',
      textPrimary: '#f9fafb',
      textSecondary: '#d1d5db'
    }
  },
  {
    id: 'minimalist',
    name: 'Minimalist',
    description: 'Clean and simple with subtle accents',
    colors: {
      primary: '#374151',
      secondary: '#9ca3af',
      accent: '#059669',
      background: '#ffffff',
      cardBackground: '#fafafa',
      textPrimary: '#111827',
      textSecondary: '#6b7280'
    }
  }
];

export const ThemeCustomization: React.FC = () => {
  const { toast } = useToast();
  const { t } = useLanguage();
  const { restaurantInfo } = useRestaurant();
  const { theme: currentTheme, updateTheme } = useRestaurantTheme();

  const [selectedPreset, setSelectedPreset] = useState<string | null>(null);
  const [customColors, setCustomColors] = useState({
    primary: '#0ea5e9',
    secondary: '#64748b',
    accent: '#f59e0b',
    background: '#ffffff',
    cardBackground: '#f8fafc',
    textPrimary: '#1e293b',
    textSecondary: '#64748b'
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [logoUrl, setLogoUrl] = useState<string | null>(null);

  // Initialize logo URL from current theme or restaurant info
  useEffect(() => {
    if (currentTheme?.branding?.logoUrl) {
      setLogoUrl(currentTheme.branding.logoUrl);
    } else if (restaurantInfo?.logo_url) {
      setLogoUrl(restaurantInfo.logo_url);
    }
  }, [currentTheme, restaurantInfo]);

  // Helper function to get restaurant_details.id from business.id
  const getRestaurantDetailsId = async (businessId: string): Promise<string | null> => {
    try {
      const { data, error } = await supabase
        .from('restaurant_details')
        .select('id')
        .eq('business_id', businessId)
        .single();

      if (error) {
        console.error('Error fetching restaurant details ID:', error);
        return null;
      }

      return data?.id || null;
    } catch (error) {
      console.error('Error in getRestaurantDetailsId:', error);
      return null;
    }
  };

  // Load current theme on component mount
  useEffect(() => {
    if (currentTheme?.colors) {
      setCustomColors(currentTheme.colors);
      setSelectedPreset(currentTheme.presetTheme || null);
    }
  }, [currentTheme]);

  // Security check: Ensure user can only modify their own restaurant theme
  if (!restaurantInfo?.id) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Theme Customization
          </CardTitle>
          <CardDescription>
            You must be logged in and have a restaurant to customize themes.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  const handlePresetSelect = async (presetId: string) => {
    setIsLoading(true);
    try {
      // Get the correct restaurant_details.id from business.id
      const restaurantDetailsId = await getRestaurantDetailsId(restaurantInfo.id);

      if (!restaurantDetailsId) {
        throw new Error('Could not find restaurant details for theme creation');
      }

      // Apply preset theme (scoped to current restaurant only) using restaurant_details.id
      await themeService.applyPresetTheme(restaurantDetailsId, presetId);

      // If a logo is uploaded, also update the business logo_url
      if (logoUrl && logoUrl !== restaurantInfo.logo_url) {
        await updateBusinessLogo(logoUrl);
      }

      const preset = PRESET_THEMES.find(p => p.id === presetId)!;
      setSelectedPreset(presetId);
      setCustomColors(preset.colors);

      // Force theme context to reload
      window.location.reload();

      toast({
        title: "Theme Applied",
        description: `${preset.name} theme has been applied to your restaurant.`,
      });
    } catch (error) {
      console.error('Error applying preset theme:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to apply theme. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCustomColorSave = async () => {
    setIsSaving(true);
    try {
      // Get the correct restaurant_details.id from business.id
      const restaurantDetailsId = await getRestaurantDetailsId(restaurantInfo.id);

      if (!restaurantDetailsId) {
        throw new Error('Could not find restaurant details for theme creation');
      }

      // Create custom theme using CreateThemeRequest interface
      const customThemeRequest = {
        restaurantId: restaurantDetailsId,
        themeName: 'Custom Theme',
        presetTheme: undefined, // Custom theme has no preset
        colors: customColors,
        layout: {
          style: 'grid' as const,
          columns: 3,
          spacing: 'normal' as const,
          cardStyle: 'detailed' as const
        },
        typography: {
          fontFamily: 'Inter',
          fontSize: 'medium' as const,
          headingStyle: 'normal' as const
        },
        branding: {
          logoUrl: logoUrl || null,
          logoPosition: 'left' as const,
          showRestaurantName: true,
          customHeader: null,
          footerText: null
        }
      };

      await themeService.createTheme(customThemeRequest);

      // If a logo is uploaded, also update the business logo_url
      if (logoUrl && logoUrl !== restaurantInfo.logo_url) {
        await updateBusinessLogo(logoUrl);
      }

      setSelectedPreset(null); // Clear preset since this is custom

      // Force theme context to reload
      window.location.reload();

      toast({
        title: "Custom Theme Saved",
        description: "Your custom theme has been applied to your restaurant.",
      });
    } catch (error) {
      console.error('Error saving custom theme:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to save custom theme. Please try again.",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleResetToDefault = async () => {
    setIsLoading(true);
    try {
      // Get the correct restaurant_details.id from business.id
      const restaurantDetailsId = await getRestaurantDetailsId(restaurantInfo.id);

      if (!restaurantDetailsId) {
        throw new Error('Could not find restaurant details for theme creation');
      }

      // Apply modern preset as default (scoped to current restaurant only) using restaurant_details.id
      await themeService.applyPresetTheme(restaurantDetailsId, 'modern');

      const defaultPreset = PRESET_THEMES.find(p => p.id === 'modern')!;
      setSelectedPreset('modern');
      setCustomColors(defaultPreset.colors);

      // Force theme context to reload
      window.location.reload();

      toast({
        title: "Theme Reset",
        description: "Theme has been reset to default modern style.",
      });
    } catch (error) {
      console.error('Error resetting theme:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to reset theme. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to update business logo_url
  const updateBusinessLogo = async (newLogoUrl: string) => {
    try {
      const { error } = await supabase
        .from('businesses')
        .update({ logo_url: newLogoUrl })
        .eq('id', restaurantInfo.id);

      if (error) {
        console.error('Error updating business logo:', error);
        throw error;
      }

    } catch (error) {
      console.error('Failed to update business logo:', error);
      throw error;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Theme Customization
          </CardTitle>
          <CardDescription>
            Customize your restaurant's menu appearance. Changes only apply to{' '}
            <span className="font-semibold text-primary">{restaurantInfo.name}</span>'s menu.
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Preset Themes */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            Preset Themes
          </CardTitle>
          <CardDescription>
            Choose from professionally designed themes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {PRESET_THEMES.map((preset) => (
              <div
                key={preset.id}
                className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                  selectedPreset === preset.id
                    ? 'border-primary bg-primary/5'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => handlePresetSelect(preset.id)}
              >
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold">{preset.name}</h3>
                    {selectedPreset === preset.id && (
                      <Badge variant="default">Active</Badge>
                    )}
                  </div>

                  <p className="text-sm text-gray-600">{preset.description}</p>

                  {/* Color Preview */}
                  <div className="flex gap-2">
                    <div
                      className="w-6 h-6 rounded-full border"
                      style={{ backgroundColor: preset.colors.primary }}
                      title="Primary Color"
                    />
                    <div
                      className="w-6 h-6 rounded-full border"
                      style={{ backgroundColor: preset.colors.secondary }}
                      title="Secondary Color"
                    />
                    <div
                      className="w-6 h-6 rounded-full border"
                      style={{ backgroundColor: preset.colors.accent }}
                      title="Accent Color"
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Custom Colors */}
      <Card>
        <CardHeader>
          <CardTitle>Custom Colors</CardTitle>
          <CardDescription>
            Fine-tune your restaurant's color scheme
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(customColors).map(([colorKey, colorValue]) => (
              <div key={colorKey} className="space-y-2">
                <Label htmlFor={colorKey} className="capitalize">
                  {colorKey.replace(/([A-Z])/g, ' $1').trim()}
                </Label>
                <div className="flex gap-2">
                  <Input
                    id={colorKey}
                    type="color"
                    value={colorValue}
                    onChange={(e) => setCustomColors(prev => ({
                      ...prev,
                      [colorKey]: e.target.value
                    }))}
                    className="w-16 h-10 p-1 border"
                  />
                  <Input
                    value={colorValue}
                    onChange={(e) => setCustomColors(prev => ({
                      ...prev,
                      [colorKey]: e.target.value
                    }))}
                    placeholder="#000000"
                    className="flex-1"
                  />
                </div>
              </div>
            ))}
          </div>

          <div className="flex gap-3 pt-4">
            <Button
              onClick={handleCustomColorSave}
              disabled={isSaving}
              className="flex items-center gap-2"
            >
              <Save className="h-4 w-4" />
              {isSaving ? 'Saving...' : 'Save Custom Theme'}
            </Button>

            <Button
              onClick={handleResetToDefault}
              variant="outline"
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              Reset to Default
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Logo Uploader */}
      <Card>
        <CardHeader>
          <CardTitle>Logo Uploader</CardTitle>
          <CardDescription>
            Upload your restaurant's logo
          </CardDescription>
        </CardHeader>
        <CardContent>
          <LogoUploader
            currentLogoUrl={logoUrl}
            onLogoChange={(newLogoUrl) => setLogoUrl(newLogoUrl)}
          />
        </CardContent>
      </Card>

      {/* Live Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Live Preview
          </CardTitle>
          <CardDescription>
            See how your theme looks on menu items
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 p-4 rounded-lg border bg-gradient-to-br from-background to-muted/20">
            {/* Preview with current theme colors */}
            <div
              className="space-y-4"
              style={{
                '--restaurant-primary': customColors.primary,
                '--restaurant-secondary': customColors.secondary,
                '--restaurant-accent': customColors.accent,
                '--restaurant-background': customColors.background,
                '--restaurant-card': customColors.cardBackground,
                '--restaurant-text': customColors.textPrimary,
                '--restaurant-text-secondary': customColors.textSecondary,
              } as React.CSSProperties}
            >
              <ThemeAwareCard variant="menu-item" className="max-w-sm">
                <div className="space-y-3">
                  <h3 className="font-semibold text-lg" style={{ color: customColors.textPrimary }}>
                    Sample Menu Item
                  </h3>
                  <p className="text-sm" style={{ color: customColors.textSecondary }}>
                    A delicious example of how your menu items will look with this theme.
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="font-bold text-lg" style={{ color: customColors.primary }}>
                      €12.50
                    </span>
                    <ThemeAwareButton variant="primary" size="sm">
                      Add to Cart
                    </ThemeAwareButton>
                  </div>
                </div>
              </ThemeAwareCard>

              <div className="flex gap-2">
                <ThemeAwareButton variant="primary">Primary Button</ThemeAwareButton>
                <ThemeAwareButton variant="secondary">Secondary</ThemeAwareButton>
                <ThemeAwareButton variant="accent">Accent</ThemeAwareButton>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ThemeCustomization;
