import React, { useState, useEffect } from 'react';
import { useRestaurant } from '@/contexts/RestaurantContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import { ChefHat, Star, TrendingUp, Save, Loader2 } from 'lucide-react';
import { MenuItemType } from '@/components/MenuItem';

interface FeaturedItem extends MenuItemType {
  is_featured: boolean;
  featured_priority: number;
  chef_notes: string;
}

const FeaturedItemsManager: React.FC = () => {
  const { restaurantInfo } = useRestaurant();
  const [menuItems, setMenuItems] = useState<FeaturedItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    fetchMenuItems();
  }, [restaurantInfo]);

  const fetchMenuItems = async () => {
    if (!restaurantInfo?.id) return;

    setIsLoading(true);
    try {
      // Get menu items for this restaurant
      const { data: menuData, error: menuError } = await supabase
        .from('menus')
        .select('id')
        .eq('restaurant_id', restaurantInfo.restaurant_details_id)
        .single();

      if (menuError) throw menuError;

      const { data: items, error: itemsError } = await supabase
        .from('menu_items')
        .select('*')
        .eq('menu_id', menuData.id)
        .eq('is_available', true)
        .order('name');

      if (itemsError) throw itemsError;

      setMenuItems(items || []);
    } catch (error) {
      console.error('Error fetching menu items:', error);
      toast.error('Failed to load menu items');
    } finally {
      setIsLoading(false);
    }
  };

  const updateFeaturedStatus = async (itemId: string, updates: Partial<FeaturedItem>) => {
    setIsSaving(true);
    try {
      const { error } = await supabase
        .from('menu_items')
        .update(updates)
        .eq('id', itemId);

      if (error) throw error;

      // Update local state
      setMenuItems(prev => prev.map(item => 
        item.id === itemId ? { ...item, ...updates } : item
      ));

      toast.success('Featured item updated successfully');
    } catch (error) {
      console.error('Error updating featured status:', error);
      toast.error('Failed to update featured item');
    } finally {
      setIsSaving(false);
    }
  };

  const handleToggleFeatured = (item: FeaturedItem) => {
    updateFeaturedStatus(item.id, {
      is_featured: !item.is_featured,
      featured_priority: item.is_featured ? 0 : 5,
      chef_notes: item.is_featured ? '' : item.chef_notes || 'Chef recommended!'
    });
  };

  const handlePriorityChange = (item: FeaturedItem, priority: number) => {
    updateFeaturedStatus(item.id, { featured_priority: priority });
  };

  const handleNotesChange = (item: FeaturedItem, notes: string) => {
    updateFeaturedStatus(item.id, { chef_notes: notes });
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <ChefHat className="w-5 h-5" />
            <span>Featured Items Manager</span>
          </CardTitle>
          <CardDescription>
            Manage which menu items appear in the recommendations banner
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  const featuredItems = menuItems.filter(item => item.is_featured);
  const availableItems = menuItems.filter(item => !item.is_featured);

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <ChefHat className="w-5 h-5 text-sky-500" />
            <span>Featured Items Manager</span>
          </CardTitle>
          <CardDescription>
            Select menu items to feature in the recommendations banner on your customer menu.
            Featured items will appear as "Chef's Recommendations" to customers.
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Currently Featured Items */}
      {featuredItems.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Star className="w-4 h-4 text-yellow-500" />
              <span>Currently Featured ({featuredItems.length})</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {featuredItems.map((item) => (
              <div key={item.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Badge variant="default" className="bg-sky-500">
                      <Star className="w-3 h-3 mr-1" />
                      Featured
                    </Badge>
                    <span className="font-medium">{item.name}</span>
                    <span className="text-sm text-gray-500">€{item.current_price}</span>
                  </div>
                  <Switch
                    checked={item.is_featured}
                    onCheckedChange={() => handleToggleFeatured(item)}
                    disabled={isSaving}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor={`priority-${item.id}`}>Priority (1-10)</Label>
                    <Input
                      id={`priority-${item.id}`}
                      type="number"
                      min="1"
                      max="10"
                      value={item.featured_priority || 5}
                      onChange={(e) => handlePriorityChange(item, parseInt(e.target.value))}
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor={`notes-${item.id}`}>Chef Notes</Label>
                    <Textarea
                      id={`notes-${item.id}`}
                      value={item.chef_notes || ''}
                      onChange={(e) => handleNotesChange(item, e.target.value)}
                      placeholder="Why do you recommend this dish?"
                      className="mt-1"
                      rows={2}
                    />
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Available Items to Feature */}
      {availableItems.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="w-4 h-4 text-gray-500" />
              <span>Available Items ({availableItems.length})</span>
            </CardTitle>
            <CardDescription>
              Click to feature items in your recommendations banner
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {availableItems.map((item) => (
                <div
                  key={item.id}
                  className="border rounded-lg p-3 flex items-center justify-between hover:bg-gray-50 transition-colors"
                >
                  <div>
                    <span className="font-medium">{item.name}</span>
                    <div className="text-sm text-gray-500">
                      {item.category} • €{item.current_price}
                    </div>
                  </div>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleToggleFeatured(item)}
                    disabled={isSaving}
                    className="border-sky-500 text-sky-500 hover:bg-sky-50"
                  >
                    <Star className="w-3 h-3 mr-1" />
                    Feature
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {menuItems.length === 0 && (
        <Card>
          <CardContent className="py-8 text-center">
            <ChefHat className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No menu items found. Add some menu items first to feature them.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default FeaturedItemsManager;
