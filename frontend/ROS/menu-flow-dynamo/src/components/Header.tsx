import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ShoppingCart, Menu } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { OrderHistoryDrawer } from './OrderHistoryDrawer';

interface HeaderProps {
  title?: string;
  showCart?: boolean;
  cartItemCount?: number;
  onCartClick?: () => void;
  showBackButton?: boolean;
}

const Header: React.FC<HeaderProps> = ({
  title = "MenuFlow",
  showCart = false,
  cartItemCount = 0,
  onCartClick,
  showBackButton = false
}) => {
  const navigate = useNavigate();

  return (
    <header className="sticky top-0 z-10 bg-restaurant-card shadow-sm">
      <div className="container mx-auto px-4 py-3 flex items-center justify-between">
        <div className="flex items-center">
          {showBackButton && (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate(-1)}
              className="mr-2"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="m15 18-6-6 6-6" />
              </svg>
            </Button>
          )}
          <h1 className="text-xl font-semibold text-restaurant-text">{title}</h1>
        </div>

        <div className="flex items-center space-x-2">
          <OrderHistoryDrawer />
          {showCart && (
            <Button
              variant="ghost"
              size="icon"
              onClick={onCartClick}
              className="relative"
            >
              <ShoppingCart className="h-6 w-6" />
              {cartItemCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-restaurant-primary text-white w-5 h-5 rounded-full flex items-center justify-center text-xs">
                  {cartItemCount}
                </span>
              )}
            </Button>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;
