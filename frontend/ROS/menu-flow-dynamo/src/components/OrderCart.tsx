
import React from 'react';
import { MenuItemType } from './MenuItem';
import { Button } from '@/components/ui/button';
import { ShoppingCart, Plus, Minus, Trash2, MessageSquare } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Textarea } from '@/components/ui/textarea';
import { useLanguage } from '@/contexts/LanguageContext';

interface CartItem extends MenuItemType {
  quantity: number;
}

interface OrderCartProps {
  items: CartItem[];
  onIncreaseQuantity: (itemId: string) => void;
  onDecreaseQuantity: (itemId: string) => void;
  onRemoveItem: (itemId: string) => void;
  onCheckout: () => void;
  isOpen: boolean;
  onClose: () => void;
  orderComment?: string;
  onCommentChange?: (comment: string) => void;
}

const OrderCart: React.FC<OrderCartProps> = ({
  items,
  onIncreaseQuantity,
  onDecreaseQuantity,
  onRemoveItem,
  onCheckout,
  isOpen,
  onClose,
  orderComment = '',
  onCommentChange
}) => {
  const { t } = useLanguage();
  const totalPrice = items.reduce((sum, item) => sum + item.price * item.quantity, 0);
  
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-end backdrop-blur-sm">
      <div className="bg-white w-full max-w-md flex flex-col h-full shadow-2xl transform transition-all duration-300 ease-out animate-in slide-in-from-right">
        <div className="px-4 py-3 border-b flex justify-between items-center">
          <div className="flex items-center">
            <ShoppingCart className="h-5 w-5 mr-2" />
            <h2 className="text-xl font-semibold">{t('yourOrder')}</h2>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            aria-label="Close cart"
            className="hover:bg-gray-100 focus:ring-2 focus:ring-gray-300 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M18 6 6 18" />
              <path d="m6 6 12 12" />
            </svg>
          </Button>
        </div>
        
        {items.length === 0 ? (
          <div className="flex-1 flex flex-col items-center justify-center p-8">
            <div className="bg-gray-50 rounded-full p-6 mb-6">
              <ShoppingCart className="h-16 w-16 text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold mb-3 text-gray-900">{t('yourCartIsEmpty')}</h3>
            <p className="text-gray-500 text-center leading-relaxed max-w-sm">
              {t('addItemsToStart')}
            </p>
            <div className="mt-6 text-center">
              <div className="inline-flex items-center text-sm text-gray-400">
                <span>💡 Tip: Browse our recommendations above</span>
              </div>
            </div>
          </div>
        ) : (
          <>
            <ScrollArea className="flex-1">
              <div className="p-4">
                {items.map((item) => (
                  <div key={item.id} className="py-4 border-b last:border-b-0 hover:bg-gray-50/50 transition-colors rounded-lg px-2 -mx-2">
                    <div className="flex justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="font-semibold text-gray-900 truncate">{item.name}</div>
                        <div className="text-sm text-gray-600 mt-1 flex items-center gap-2">
                          <span className="font-medium">€{item.price.toFixed(2)}</span>
                          {item.category && (
                            <span className="text-xs bg-gray-100 px-2 py-1 rounded-full capitalize">
                              {item.category}
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-7 w-7 hover:bg-white hover:shadow-sm transition-all"
                          onClick={() => onDecreaseQuantity(item.id)}
                          aria-label={`${t('decreaseQuantity')} ${item.name}`}
                        >
                          <Minus className="h-3 w-3" />
                        </Button>
                        <span className="w-8 text-center font-medium text-sm" aria-label={`Quantity: ${item.quantity}`}>{item.quantity}</span>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-7 w-7 hover:bg-white hover:shadow-sm transition-all"
                          onClick={() => onIncreaseQuantity(item.id)}
                          aria-label={`${t('increaseQuantity')} ${item.name}`}
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    <div className="mt-3 flex justify-between items-center">
                      <div className="text-sm font-medium text-gray-700">
                        {t('subtotal')}: <span className="text-gray-900">€{(item.price * item.quantity).toFixed(2)}</span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full transition-all"
                        onClick={() => onRemoveItem(item.id)}
                        aria-label={`${t('removeItem')} ${item.name}`}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
            
            <div className="border-t bg-gray-50/50 p-6">
              {/* Optional Comment Section */}
              <div className="mb-6">
                <div className="flex items-center mb-3">
                  <MessageSquare className="h-4 w-4 mr-2 text-gray-600" />
                  <label className="text-sm font-medium text-gray-700">
                    {t('specialInstructions')} <span className="text-gray-500 font-normal">({t('optional')})</span>
                  </label>
                </div>
                <Textarea
                  placeholder={t('specialInstructionsPlaceholder')}
                  value={orderComment}
                  onChange={(e) => onCommentChange?.(e.target.value)}
                  className="min-h-[80px] resize-none border-gray-200 focus:border-sky-500 focus:ring-sky-500 rounded-lg text-sm"
                  maxLength={500}
                />
                <div className="flex justify-between items-center mt-1">
                  <p className="text-xs text-gray-500">
                    {t('specialInstructionsHint')}
                  </p>
                  <span className="text-xs text-gray-400">
                    {orderComment.length}/500
                  </span>
                </div>
              </div>
              
              <div className="flex justify-between items-center mb-6">
                <span className="text-lg font-bold text-gray-900">{t('total')}</span>
                <span className="text-2xl font-bold text-gray-900">€{totalPrice.toFixed(2)}</span>
              </div>
              <Button
                className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]"
                onClick={onCheckout}
                aria-label={`${t('placeOrder')} - Total: €${totalPrice.toFixed(2)}`}
              >
                <div className="flex items-center justify-center gap-2">
                  <span>{t('placeOrder')}</span>
                  <span className="text-sm opacity-90">→</span>
                </div>
              </Button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default OrderCart;
