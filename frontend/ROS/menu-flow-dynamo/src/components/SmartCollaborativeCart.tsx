/**
 * Smart Collaborative Cart - Phase 4 AI Enhancement
 *
 * Enhanced UX on top of the fixed collaborative cart sync system
 * Provides intelligent recommendations, real-time insights, and smart ordering features
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Users,
  Brain,
  TrendingUp,
  Clock,
  DollarSign,
  ShoppingCart,
  UserPlus,
  Zap,
  Target,
  Star,
  AlertCircle,
  CheckCircle,
  RefreshCw,
  Activity,
  Sparkles
} from 'lucide-react';
import { SharedCartService, SharedCart, TableParticipant } from '@/services/sharedCartService';
import { SharedCartRealtimeService, CartChangeEvent } from '@/services/sharedCartRealtimeService';
import AIInsightsEngine, { MenuInsight } from '@/services/aiInsightsEngine';
import { MenuItemType } from '@/components/MenuItem';

interface SmartCartState {
  cart: SharedCart | null;
  participants: TableParticipant[];
  smartRecommendations: SmartRecommendation[];
  orderInsights: OrderInsight[];
  loading: boolean;
  error: string | null;
}

interface SmartRecommendation {
  id: string;
  type: 'complement' | 'upsell' | 'trending' | 'group_favorite' | 'value_bundle';
  title: string;
  description: string;
  items: MenuItemType[];
  confidence: number;
  potential_savings: number;
  reasoning: string;
}

interface OrderInsight {
  type: 'spending_analysis' | 'time_estimate' | 'group_preferences' | 'optimal_timing';
  title: string;
  value: string;
  description: string;
  action?: string;
}

interface SmartCollaborativeCartProps {
  tableId: string;
  customerSessionId: string;
  customerDisplayName: string;
  restaurantId: string;
  onCartUpdate?: (cart: SharedCart) => void;
}

export function SmartCollaborativeCart({
  tableId,
  customerSessionId,
  customerDisplayName,
  restaurantId,
  onCartUpdate
}: SmartCollaborativeCartProps) {
  const [state, setState] = useState<SmartCartState>({
    cart: null,
    participants: [],
    smartRecommendations: [],
    orderInsights: [],
    loading: true,
    error: null
  });

  const [isJoining, setIsJoining] = useState(false);
  const [hasJoined, setHasJoined] = useState(false);

  // Initialize cart and real-time connection
  useEffect(() => {
    initializeSmartCart();
    return () => {
      // Cleanup real-time subscriptions
      SharedCartRealtimeService.cleanupAllSubscriptions();
    };
  }, [tableId, customerSessionId]);

  // Update cart recommendations when cart changes
  useEffect(() => {
    if (state.cart && hasJoined) {
      generateSmartRecommendations();
      generateOrderInsights();
    }
  }, [state.cart, hasJoined]);

  const initializeSmartCart = async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      // Join the collaborative cart
      setIsJoining(true);
      await SharedCartService.joinTableCart(tableId, customerSessionId, restaurantId);
      setHasJoined(true);
      setIsJoining(false);

      // Load initial cart data
      const [cart, participants] = await Promise.all([
        SharedCartService.getSharedCart(tableId),
        SharedCartService.getTableParticipants(tableId)
      ]);

      // Set up real-time subscriptions
      SharedCartRealtimeService.subscribeToTableCart(
        tableId,
        handleCartUpdate
      );

      setState(prev => ({
        ...prev,
        cart,
        participants,
        loading: false
      }));

      if (cart && onCartUpdate) {
        onCartUpdate(cart);
      }

    } catch (error) {

      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to initialize cart'
      }));
      setIsJoining(false);
    }
  };

  const handleCartUpdate = useCallback((event: CartChangeEvent) => {

    setState(prev => ({
      ...prev,
      cart: event.data as SharedCart
    }));

    if (onCartUpdate && event.data) {
      onCartUpdate(event.data as SharedCart);
    }

    // Trigger smart recommendations update
    if (event.type === 'item_added' || event.type === 'item_removed') {
      generateSmartRecommendations();
    }
  }, [onCartUpdate]);


  const generateSmartRecommendations = async () => {
    if (!state.cart || !restaurantId) return;

    try {
      // Get menu insights for recommendations
      const menuInsights = await AIInsightsEngine.getPredictiveMenuInsights(restaurantId);

      // Generate smart recommendations based on current cart
      const recommendations = createSmartRecommendations(state.cart, menuInsights);

      setState(prev => ({ ...prev, smartRecommendations: recommendations }));

    } catch (error) {

    }
  };

  const generateOrderInsights = () => {
    if (!state.cart || !state.participants.length) return;

    const insights: OrderInsight[] = [];

    // Spending analysis
    const totalAmount = state.cart.total_amount || 0;
    const avgPerPerson = totalAmount / Math.max(state.participants.length, 1);

    insights.push({
      type: 'spending_analysis',
      title: 'Group Spending',
      value: `$${avgPerPerson.toFixed(2)} per person`,
      description: `Total order: $${totalAmount.toFixed(2)} for ${state.participants.length} people`
    });

    // Time estimate
    const itemCount = (state.cart.cart_items || []).length;
    const estimatedTime = Math.max(15, itemCount * 3 + Math.random() * 10);

    insights.push({
      type: 'time_estimate',
      title: 'Estimated Prep Time',
      value: `${Math.round(estimatedTime)} minutes`,
      description: `Based on ${itemCount} items and current kitchen load`
    });

    // Group preferences
    if (state.participants.length > 1) {
      insights.push({
        type: 'group_preferences',
        title: 'Collaboration Status',
        value: `${state.participants.length} active members`,
        description: 'Everyone can add, modify, or remove items from the shared cart',
        action: 'Invite more friends'
      });
    }

    // Optimal timing
    const currentHour = new Date().getHours();
    let timingInsight = '';

    if (currentHour >= 11 && currentHour <= 14) {
      timingInsight = 'Lunch rush - expect slightly longer wait times';
    } else if (currentHour >= 17 && currentHour <= 20) {
      timingInsight = 'Dinner peak - kitchen is at full capacity';
    } else {
      timingInsight = 'Off-peak hours - faster service expected';
    }

    insights.push({
      type: 'optimal_timing',
      title: 'Service Timing',
      value: timingInsight.split(' - ')[0],
      description: timingInsight
    });

    setState(prev => ({ ...prev, orderInsights: insights }));
  };

  const createSmartRecommendations = (cart: SharedCart, menuInsights: MenuInsight[]): SmartRecommendation[] => {
    const recommendations: SmartRecommendation[] = [];
    const cartItems = cart.cart_items || [];

    // Trending items recommendation
    const trendingItems = menuInsights
      .filter(insight => insight.performance_score > 80)
      .slice(0, 3);

    if (trendingItems.length > 0) {
      recommendations.push({
        id: 'trending',
        type: 'trending',
        title: 'Trending This Week',
        description: 'Popular items other groups are ordering',
        items: trendingItems.map(insight => ({
          id: insight.item_id,
          name: insight.item_name,
          price: 15.99, // Mock price
          description: 'Highly rated menu item',
          image_url: '',
          category: 'Popular',
          is_available: true
        })),
        confidence: 90,
        potential_savings: 0,
        reasoning: 'Based on recent ordering patterns and customer satisfaction'
      });
    }

    // Value bundle recommendation
    if (cartItems.length >= 2) {
      const bundleValue = cartItems.reduce((sum, item) => sum + (item.price || 0), 0) * 0.15;

      recommendations.push({
        id: 'value_bundle',
        type: 'value_bundle',
        title: 'Complete Your Meal',
        description: 'Add a drink and dessert for the perfect combo',
        items: [
          {
            id: 'drink_combo',
            name: 'Premium Beverage',
            price: 4.99,
            description: 'Complements your meal perfectly',
            image_url: '',
            category: 'Drinks',
            is_available: true
          },
          {
            id: 'dessert_combo',
            name: 'Signature Dessert',
            price: 7.99,
            description: 'Perfect way to end your meal',
            image_url: '',
            category: 'Desserts',
            is_available: true
          }
        ],
        confidence: 85,
        potential_savings: bundleValue,
        reasoning: 'Save 15% by completing your meal with drinks and desserts'
      });
    }

    // Group favorite (if multiple participants)
    if (state.participants.length > 1) {
      recommendations.push({
        id: 'group_favorite',
        type: 'group_favorite',
        title: 'Great for Sharing',
        description: 'Perfect items for your group size',
        items: [
          {
            id: 'sharing_platter',
            name: 'Sharing Platter',
            price: 24.99,
            description: `Perfect for ${state.participants.length} people`,
            image_url: '',
            category: 'Sharing',
            is_available: true
          }
        ],
        confidence: 80,
        potential_savings: state.participants.length * 3,
        reasoning: `Optimized for groups of ${state.participants.length} people`
      });
    }

    return recommendations;
  };

  const handleAddRecommendation = async (recommendation: SmartRecommendation) => {
    if (!recommendation.items.length) return;

    try {
      setState(prev => ({ ...prev, loading: true }));

      // Add first item from recommendation
      const item = recommendation.items[0];
      await SharedCartService.addItemToSharedCart(
        tableId,
        customerSessionId,
        item,
        1
      );

      setState(prev => ({ ...prev, loading: false }));

    } catch (error) {

      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to add item'
      }));
    }
  };

  const getConnectionStatusColor = () => {
    if (!state.connectionHealth) return 'gray';
    if (state.connectionHealth.isConnected && state.connectionHealth.latency < 100) return 'green';
    if (state.connectionHealth.isConnected) return 'yellow';
    return 'red';
  };

  const getConnectionStatusText = () => {
    if (!state.connectionHealth) return 'Connecting...';
    if (state.connectionHealth.isConnected) {
      return `Connected (${state.connectionHealth.latency}ms)`;
    }
    return 'Reconnecting...';
  };

  if (state.loading && !hasJoined) {
    return (
      <Card className="p-8">
        <div className="flex items-center justify-center space-x-2">
          <Users className="h-6 w-6 animate-pulse text-blue-500" />
          <span className="text-lg font-medium">
            {isJoining ? 'Joining collaborative cart...' : 'Loading smart cart...'}
          </span>
        </div>
      </Card>
    );
  }

  if (state.error) {
    return (
      <Card className="p-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{state.error}</AlertDescription>
        </Alert>
        <Button onClick={initializeSmartCart} className="mt-4">
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Smart Cart Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                <ShoppingCart className="h-6 w-6 text-blue-500" />
                <Sparkles className="h-5 w-5 text-purple-500" />
              </div>
              <div>
                <CardTitle>Smart Collaborative Cart</CardTitle>
                <CardDescription>
                  AI-powered ordering with real-time collaboration
                </CardDescription>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <div className={`h-2 w-2 rounded-full bg-${getConnectionStatusColor()}-500`} />
              <span className="text-sm text-gray-500">{getConnectionStatusText()}</span>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Participants Display */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <span className="text-sm font-medium">Active Participants:</span>
              <div className="flex -space-x-2">
                {state.presence.slice(0, 5).map((participant, index) => (
                  <Avatar key={participant.customer_session_id} className="border-2 border-white h-8 w-8">
                    <AvatarFallback className="text-xs">
                      {participant.customer_display_name.slice(0, 2).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                ))}
                {state.presence.length > 5 && (
                  <div className="h-8 w-8 rounded-full bg-gray-200 border-2 border-white flex items-center justify-center">
                    <span className="text-xs text-gray-600">+{state.presence.length - 5}</span>
                  </div>
                )}
              </div>
            </div>
            <Button variant="outline" size="sm">
              <UserPlus className="h-4 w-4 mr-2" />
              Invite Friends
            </Button>
          </div>

          {/* Cart Summary */}
          {state.cart && (
            <div className="grid grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
              <div className="text-center">
                <p className="text-sm text-gray-600">Items</p>
                <p className="text-lg font-bold">{(state.cart.cart_items || []).length}</p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-600">Total</p>
                <p className="text-lg font-bold">${(state.cart.total_amount || 0).toFixed(2)}</p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-600">Per Person</p>
                <p className="text-lg font-bold">
                  ${((state.cart.total_amount || 0) / Math.max(state.participants.length, 1)).toFixed(2)}
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Smart Recommendations */}
      {state.smartRecommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Brain className="h-5 w-5 text-purple-500" />
              <span>AI Recommendations</span>
            </CardTitle>
            <CardDescription>
              Personalized suggestions based on your group and preferences
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {state.smartRecommendations.map((recommendation) => (
              <div key={recommendation.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {recommendation.type === 'trending' && <TrendingUp className="h-4 w-4 text-green-500" />}
                    {recommendation.type === 'value_bundle' && <DollarSign className="h-4 w-4 text-blue-500" />}
                    {recommendation.type === 'group_favorite' && <Users className="h-4 w-4 text-purple-500" />}
                    <h3 className="font-medium">{recommendation.title}</h3>
                  </div>
                  <Badge variant="outline">
                    {recommendation.confidence}% confident
                  </Badge>
                </div>

                <p className="text-sm text-gray-600">{recommendation.description}</p>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <p className="text-xs text-gray-500">AI Reasoning:</p>
                    <p className="text-sm">{recommendation.reasoning}</p>
                    {recommendation.potential_savings > 0 && (
                      <p className="text-sm text-green-600 font-medium">
                        Save ${recommendation.potential_savings.toFixed(2)}
                      </p>
                    )}
                  </div>
                  <Button
                    onClick={() => handleAddRecommendation(recommendation)}
                    size="sm"
                    disabled={state.loading}
                  >
                    <Zap className="h-4 w-4 mr-2" />
                    Add to Cart
                  </Button>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Order Insights */}
      {state.orderInsights.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Target className="h-5 w-5 text-blue-500" />
              <span>Order Insights</span>
            </CardTitle>
            <CardDescription>
              Smart analysis of your group order
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {state.orderInsights.map((insight, index) => (
                <div key={index} className="p-4 border rounded-lg space-y-2">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium">{insight.title}</h3>
                    {insight.type === 'spending_analysis' && <DollarSign className="h-4 w-4 text-green-500" />}
                    {insight.type === 'time_estimate' && <Clock className="h-4 w-4 text-blue-500" />}
                    {insight.type === 'group_preferences' && <Users className="h-4 w-4 text-purple-500" />}
                    {insight.type === 'optimal_timing' && <Activity className="h-4 w-4 text-orange-500" />}
                  </div>
                  <p className="text-lg font-bold text-blue-600">{insight.value}</p>
                  <p className="text-sm text-gray-600">{insight.description}</p>
                  {insight.action && (
                    <Button variant="outline" size="sm" className="mt-2">
                      {insight.action}
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Real-time Activity Feed */}
      {state.presence.length > 1 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Activity className="h-5 w-5 text-green-500" />
              <span>Live Activity</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {state.presence.map((participant) => (
                <div key={participant.customer_session_id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <div className="flex items-center space-x-2">
                    <div className="h-2 w-2 bg-green-500 rounded-full" />
                    <span className="text-sm font-medium">{participant.customer_display_name}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    {participant.is_typing && (
                      <Badge variant="secondary" className="text-xs">
                        typing...
                      </Badge>
                    )}
                    <span className="text-xs text-gray-500 capitalize">
                      {participant.current_action || 'browsing'}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export default SmartCollaborativeCart;