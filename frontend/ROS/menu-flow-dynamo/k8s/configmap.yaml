apiVersion: v1
kind: ConfigMap
metadata:
  name: restaurant-frontend-config
  namespace: sme-analytica
  labels:
    app: restaurant-frontend
data:
  nginx.conf: |
    events {
        worker_connections 1024;
    }

    http {
        include       /etc/nginx/mime.types;
        default_type  application/octet-stream;

        # Logging
        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for"';

        access_log /var/log/nginx/access.log main;
        error_log /var/log/nginx/error.log warn;

        # Performance optimizations
        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;

        # Gzip compression
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_proxied any;
        gzip_comp_level 6;
        gzip_types
            text/plain
            text/css
            text/xml
            text/javascript
            application/json
            application/javascript
            application/xml+rss
            application/atom+xml
            image/svg+xml;

        # WebSocket support for Safari compatibility
        map $http_upgrade $connection_upgrade {
            default upgrade;
            '' close;
        }

        server {
            listen 80;
            server_name localhost;
            root /usr/share/nginx/html;
            index index.html;

            # Security headers
            add_header X-Frame-Options "SAMEORIGIN" always;
            add_header X-Content-Type-Options "nosniff" always;
            add_header X-XSS-Protection "1; mode=block" always;
            add_header Referrer-Policy "strict-origin-when-cross-origin" always;

            # CORS headers for API calls
            add_header Access-Control-Allow-Origin "*" always;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
            add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;

            # Handle preflight requests
            location / {
                try_files $uri $uri/ /index.html;
                
                # Handle CORS preflight
                if ($request_method = 'OPTIONS') {
                    add_header Access-Control-Allow-Origin "*";
                    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
                    add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization";
                    add_header Access-Control-Max-Age 1728000;
                    add_header Content-Type 'text/plain; charset=utf-8';
                    add_header Content-Length 0;
                    return 204;
                }
            }

            # Cache static assets
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
                try_files $uri =404;
            }

            # Health check endpoint
            location /health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }

            # Error pages
            error_page 404 /index.html;
            error_page 500 502 503 504 /50x.html;
            location = /50x.html {
                root /usr/share/nginx/html;
            }
        }
    }

---
apiVersion: v1
kind: Secret
metadata:
  name: supabase-config
  namespace: sme-analytica
  labels:
    app: restaurant-frontend
type: Opaque
stringData:
  url: "https://kpbfajbqblcezsxstvug.supabase.co"
  anon-key: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************._9ODxewqHlC9AnNFmt50Iaj7cvLTbGSf_TD5CfdIIUY"
