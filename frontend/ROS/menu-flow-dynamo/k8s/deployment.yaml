apiVersion: apps/v1
kind: Deployment
metadata:
  name: restaurant-frontend
  namespace: sme-analytica
  labels:
    app: restaurant-frontend
    component: frontend
    tier: web
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: restaurant-frontend
  template:
    metadata:
      labels:
        app: restaurant-frontend
        component: frontend
        tier: web
    spec:
      containers:
      - name: restaurant-frontend
        image: olayeenca/restaurant-frontend:latest
        ports:
        - containerPort: 80
          name: http
        env:
        - name: VITE_SUPABASE_URL
          valueFrom:
            secretKeyRef:
              name: supabase-config
              key: url
        - name: VITE_SUPABASE_ANON_KEY
          valueFrom:
            secretKeyRef:
              name: supabase-config
              key: anon-key
        - name: NODE_ENV
          value: "production"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: nginx-logs
          mountPath: /var/log/nginx
      volumes:
      - name: nginx-logs
        emptyDir: {}
      restartPolicy: Always
      imagePullPolicy: Always

---
apiVersion: v1
kind: Service
metadata:
  name: restaurant-frontend-service
  namespace: sme-analytica
  labels:
    app: restaurant-frontend
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
    name: http
  selector:
    app: restaurant-frontend

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: restaurant-frontend-ingress
  namespace: sme-analytica
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "86400"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "86400"
    nginx.ingress.kubernetes.io/websocket-services: "restaurant-frontend-service"
spec:
  tls:
  - hosts:
    - restaurant.smeanalytica.dev
    secretName: restaurant-frontend-tls
  rules:
  - host: restaurant.smeanalytica.dev
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: restaurant-frontend-service
            port:
              number: 80
