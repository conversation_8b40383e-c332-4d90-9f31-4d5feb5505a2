-- Migration: Add AI insights columns to customer_feedback table
-- Date: 2025-07-08
-- Purpose: Support background AI processing for feedback analysis

-- Add AI-enhanced analysis columns
ALTER TABLE public.customer_feedback 
ADD COLUMN IF NOT EXISTS ai_insights JSONB NULL;

ALTER TABLE public.customer_feedback 
ADD COLUMN IF NOT EXISTS key_phrases JSONB NULL;

ALTER TABLE public.customer_feedback 
ADD COLUMN IF NOT EXISTS emotions JSONB NULL;

-- Add comments for documentation
COMMENT ON COLUMN public.customer_feedback.ai_insights IS 'AI-generated business insights processed in background';
COMMENT ON COLUMN public.customer_feedback.key_phrases IS 'Extracted key phrases from customer feedback';
COMMENT ON COLUMN public.customer_feedback.emotions IS 'Emotion analysis results from AI processing';

-- Create indexes for AI insights queries (optional, for future analytics)
CREATE INDEX IF NOT EXISTS idx_customer_feedback_ai_insights_gin ON public.customer_feedback USING GIN (ai_insights);
CREATE INDEX IF NOT EXISTS idx_customer_feedback_key_phrases_gin ON public.customer_feedback USING GIN (key_phrases);
CREATE INDEX IF NOT EXISTS idx_customer_feedback_emotions_gin ON public.customer_feedback USING GIN (emotions);

-- Verify the changes
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'customer_feedback' 
    AND table_schema = 'public'
    AND column_name IN ('ai_insights', 'key_phrases', 'emotions')
ORDER BY column_name;
