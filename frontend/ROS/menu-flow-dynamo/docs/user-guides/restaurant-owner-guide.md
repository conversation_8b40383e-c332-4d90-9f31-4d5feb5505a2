# Menu Flow Dynamo: Restaurant Owner Guide

![Analytica Logo](../../public/analytics-logo.png)

## Introduction

Welcome to **Menu Flow Dynamo**, part of the SME Analytica suite of restaurant management tools. This guide will help you get the most out of your Menu Flow Dynamo system, designed to streamline your restaurant operations through QR code ordering, dynamic pricing, and seamless integration with your existing POS system.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Dashboard Overview](#dashboard-overview)
3. [Menu Management](#menu-management)
4. [Table Management](#table-management)
5. [Order Management](#order-management)
6. [Dynamic Pricing](#dynamic-pricing)
7. [Analytics & Reports](#analytics--reports)
8. [Settings & Integrations](#settings--integrations)
9. [Troubleshooting](#troubleshooting)

## Getting Started

### First-Time Setup

1. **Login**: Use your provided credentials to log in at your custom domain (typically `yourrestaurant.menuflow.com/admin`)
2. **Complete Restaurant Profile**: Fill in your restaurant information, including:
   - Restaurant name, address, and contact information
   - Opening hours
   - Restaurant type and cuisine
   - Upload logo and banner images
3. **Add Menu Items**: Import existing menus or create your menu from scratch
4. **Set Up Tables**: Add your restaurant tables with respective QR codes
5. **Configure POS Integration**: Connect with your existing POS system if applicable

## Dashboard Overview

The Admin Dashboard is your central command center, providing a quick overview of your restaurant's operations.

### Main Dashboard Elements

- **Quick Actions Panel**: One-click access to common tasks
- **Notification Center**: Real-time alerts for orders, inventory, and system updates
- **Restaurant Activity Dashboard**: Today's performance metrics including:
  - Occupancy rate
  - Total sales
  - Orders processed
  - Average order value
- **Business Hours Progress**: Visual indicator of where you are in your business day
- **Traffic Heatmap**: Real-time visualization of foot traffic patterns

### Quick Actions

The Quick Actions panel provides immediate access to your most commonly used features:

- Add menu items
- Manage menus
- Manage tables
- View analytics
- Generate QR codes
- Sync with POS system
- Access reports
- Export data
- Configure settings

## Menu Management

### Creating and Editing Items

1. Navigate to **Menu Management** from the dashboard
2. Click **Add Item** to create a new menu item
3. Fill in the required information:
   - Name and description
   - Price
   - Category
   - Dietary information (vegetarian, vegan, gluten-free, etc.)
   - Allergens
   - Preparation time
   - Upload image
4. Enable/disable dynamic pricing for individual items
5. Click **Save** to add the item to your menu

### Organizing Your Menu

- **Categories**: Group similar items for easier navigation
- **Recommended Items**: Mark items as recommended for increased visibility
- **Special Tags**: Add tags like "New," "Popular," or "Chef's Special"
- **Seasonal Menus**: Create and activate seasonal menus
- **Daily Specials**: Set up recurring or one-time specials

### Inventory Management

- Set inventory levels for menu items
- Receive alerts when items are running low
- Automatically hide out-of-stock items

## Table Management

### Setting Up Tables

1. Navigate to **Table Management** from the dashboard
2. Click **Add Table** to create a new table
3. Specify table details:
   - Table number/name
   - Seating capacity
   - Location (optional)
4. Save to generate a unique QR code for the table

### QR Code Management

- **Generate QR Codes**: Create unique QR codes for each table
- **Download QR Codes**: Available in various formats (PNG, PDF)
- **Print QR Codes**: Print directly or send to professional printing
- **Table Cards**: Generate table cards with QR codes for display

### Table Status Monitoring

View real-time status of all tables:
- Available
- Occupied
- Reserved
- Needs attention
- Cleaning

## Order Management

### Viewing Orders

1. Access the **Orders** section from the dashboard
2. Filter orders by:
   - Status (New, In Progress, Completed, Cancelled)
   - Time period
   - Table number
   - Server
3. Click on an order to view details

### Processing Orders

1. When a new order arrives, you'll receive a notification
2. Review the order details
3. Accept or reject the order
4. Assign the order to kitchen staff
5. Update the order status as it progresses
6. Mark as complete when delivered to the customer

### Order History

- Access complete order history
- Search for specific orders
- Export order data for accounting purposes
- View order statistics and patterns

## Dynamic Pricing

Dynamic pricing automatically adjusts item prices based on demand, time of day, and foot traffic.

### Configuring Dynamic Pricing

1. Navigate to **Dynamic Pricing** in settings
2. Enable/disable dynamic pricing system-wide
3. Configure pricing rules:
   - **Time-Based Rules**: Different pricing during peak/off-peak hours
   - **Traffic-Based Rules**: Adjust based on restaurant occupancy
   - **Category-Specific Rules**: Apply different strategies to different menu categories
4. Set minimum and maximum adjustment percentages
5. Preview pricing changes before activation

### Monitoring Performance

- View real-time pricing adjustments
- Analyze impact on sales and profitability
- Adjust rules based on performance data

## Analytics & Reports

### Dashboard Analytics

The Analytics Dashboard provides visual representations of your restaurant's performance:

- Sales trends (daily, weekly, monthly)
- Popular items
- Peak hours
- Average order value
- Table turnover rate
- Customer satisfaction metrics

### Generating Reports

1. Navigate to **Reports** in the Analytics section
2. Select report type:
   - Sales reports
   - Inventory reports
   - Employee performance
   - Dynamic pricing impact
   - Customer behavior
3. Set date range and parameters
4. Generate and download reports in PDF, CSV, or Excel format

## Settings & Integrations

### Restaurant Settings

Customize your restaurant profile:
- Basic information
- Operating hours
- Payment methods
- Delivery options
- Languages and currencies

### POS Integration

Connect Menu Flow Dynamo with your existing POS system:
1. Go to **Settings > Integrations**
2. Select your POS provider
3. Enter API credentials
4. Test the connection
5. Configure synchronization settings

Supported POS systems include:
- Oracle Micros
- Square
- Clover
- Toast
- Lightspeed
- And more...

### User Management

Create and manage staff accounts with appropriate permission levels:
- Administrators
- Managers
- Servers
- Kitchen Staff

## Troubleshooting

### Common Issues

- **QR Code Scanning Problems**: Ensure proper lighting and print quality
- **Menu Not Updating**: Check your sync status with POS system
- **Order Notifications Not Appearing**: Verify notification settings
- **Integration Issues**: Check API credentials and connection status

### Getting Help

- Access the built-in help center
- Contact <NAME_EMAIL>
- Schedule a consultation with your account manager

---

## Additional Resources

- [Video Tutorials](https://learn.menuflow.com/videos)
- [FAQ](https://help.menuflow.com/faq)
- [Community Forum](https://community.menuflow.com)
- [Training Webinars](https://learn.menuflow.com/webinars)

---

© 2025 SME Analytica | Menu Flow Dynamo
