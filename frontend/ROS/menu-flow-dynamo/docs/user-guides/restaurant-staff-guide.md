# Menu Flow Dynamo: Staff Quick Guide

![Analytica Logo](../../public/analytics-logo.png)

## Introduction

Welcome to the **Menu Flow Dynamo** staff guide. This document provides essential information for restaurant staff to effectively use the Menu Flow Dynamo system for day-to-day operations. Whether you're a server, host, or kitchen staff, this guide will help you understand how to manage orders, tables, and customer interactions through our digital system.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Staff Dashboard](#staff-dashboard)
3. [Managing Orders](#managing-orders)
4. [Table Management](#table-management)
5. [Helping Customers](#helping-customers)
6. [Kitchen Display System](#kitchen-display-system)
7. [End of Shift Procedures](#end-of-shift-procedures)
8. [Troubleshooting](#troubleshooting)

## Getting Started

### Logging In

1. Use your assigned username and password to log in
2. Select your role (Server, Host, Kitchen Staff)
3. If you're using a shared device, always remember to log out at the end of your shift

### Staff Roles and Permissions

- **Servers**: Order management, table status updates, customer assistance
- **Hosts**: Table assignments, queue management, traffic monitoring
- **Kitchen Staff**: Order fulfillment, inventory updates, preparation timing

## Staff Dashboard

Your personalized dashboard provides quick access to the tools you need for your specific role.

### Common Dashboard Elements

- **Active Orders**: Orders that require your attention
- **Table Map**: Visual display of all tables and their current status
- **Notifications**: Real-time alerts for new orders, kitchen updates, etc.
- **Quick Actions**: Fast access to common tasks for your role

## Managing Orders

### Viewing New Orders

1. New orders appear in the "New Orders" section of your dashboard
2. A notification sound will alert you to new orders
3. Review order details including:
   - Table number
   - Order items and specifications
   - Any special requests
   - Time of order

### Processing Orders

1. **Accept** the order to let the customer know it's being processed
2. Send the order to the kitchen using the **Send to Kitchen** button
3. Update order status as it progresses:
   - In Preparation
   - Ready for Pickup
   - Delivered
   - Completed

### Modifying Orders

1. Select the order you need to modify
2. Click **Modify Order**
3. Make necessary changes:
   - Add or remove items
   - Update quantities
   - Add special instructions
4. Save changes and update the kitchen if needed

### Order Issues and Refunds

If there's an issue with an order:
1. Select the order
2. Choose **Report Issue**
3. Select the reason for the issue
4. Process refund if necessary (requires manager approval)

## Table Management

### Table Status Updates

Keep table status up to date to help with seating and service:
1. Access the table map from your dashboard
2. Select a table to update its status:
   - Available
   - Occupied
   - Needs Service
   - Finishing
   - Needs Cleaning

### Assisting with QR Code Orders

When customers need help with QR code ordering:
1. Explain how to scan the QR code with their smartphone camera
2. Show them how to access the digital menu
3. Demonstrate how to place orders and request service
4. Let them know they can still ask for assistance at any time

## Helping Customers

### QR Ordering Assistance

Some customers may need help with the digital ordering system:
1. Introduce yourself and offer assistance
2. Show them how to scan the QR code
3. Walk them through menu navigation
4. Explain how to place and modify orders
5. Demonstrate how to request service or call for assistance

### Common Customer Questions

Be prepared to answer these frequently asked questions:
- "How do I place an order?"
- "Can I modify my order after submitting it?"
- "How do I pay through the system?"
- "What if I need additional services?"
- "Why do some prices change throughout the day?"

## Kitchen Display System

For kitchen staff, the Kitchen Display System (KDS) is your main interface.

### Viewing Incoming Orders

1. Orders appear on the KDS screen as they come in
2. Orders are sorted by priority and time received
3. Color coding indicates:
   - Red: Orders waiting too long
   - Yellow: Orders approaching delay threshold
   - Green: New orders

### Managing Food Preparation

1. Select an order to view details
2. Mark items as "In Preparation" when you start working on them
3. Update to "Ready" when the item is complete
4. Communicate with servers through the KDS for special requests or issues

### Inventory Alerts

The system will notify you when:
- Items are running low in inventory
- Items need to be marked as unavailable
- Prep work is needed for specific items

## End of Shift Procedures

### Closing Out

1. Process any pending orders
2. Update inventory counts if required
3. Clean and reset assigned stations
4. Log out of the system

### Daily Reports

Staff leads should:
1. Generate end-of-day reports
2. Review performance metrics
3. Note any issues that occurred during the shift
4. Submit feedback through the system

## Troubleshooting

### Common Issues

- **System Unresponsive**: Refresh the page or restart the application
- **Order Not Sending to Kitchen**: Check your internet connection
- **Customer Can't Scan QR Code**: Provide a physical menu or take order manually
- **Payment Processing Error**: Contact a manager for assistance

### Getting Help

If you encounter technical issues:
1. Check the quick troubleshooting guide in the help section
2. Contact your shift manager
3. For urgent issues, call technical support at the number provided by your manager

---

## Staff Training Resources

- [Video Tutorials](https://learn.menuflow.com/staff/videos)
- [Practice Mode](https://training.menuflow.com) (Use this to practice without affecting real orders)
- [Daily Tips & Tricks](https://learn.menuflow.com/staff/tips)

Remember: The Menu Flow Dynamo system is designed to enhance your service, not replace the personal touch that makes your restaurant special. Use the system as a tool to provide even better customer experiences.

---

© 2025 SME Analytica | Menu Flow Dynamo
