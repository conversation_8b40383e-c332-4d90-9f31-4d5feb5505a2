# Restaurant Feedback Sync Architecture for AI Training

## Overview

This system manages the synchronization of customer feedback data from the local Restaurant Ordering System (ROS) database to the centralized SME Analytica database for AI model training.

## Architecture Components

### 1. Two-Tier Database Architecture

#### Local Database (ROS) - `yqtdsxrwfxyzwbkhxfka`
- **Table**: `customer_feedback`
- **Purpose**: Immediate storage for restaurant operations
- **Access**: Anonymous customers, restaurant staff
- **Features**: 
  - Basic sentiment analysis
  - Local analytics
  - Order feedback tracking

#### Centralized Database (SME Analytica) - `kpbfajbqblcezsxstvug`
- **Table**: `restaurant_feedback`
- **Purpose**: AI training and cross-restaurant analytics
- **Access**: AI systems, business intelligence
- **Features**:
  - Advanced sentiment processing
  - Cross-restaurant insights
  - AI model training data
  - Restaurant sentiment summaries

### 2. Sync Mechanisms

#### Automatic Sync (Real-time)
- **When**: Every time a customer submits new feedback
- **Process**: 
  1. Customer submits feedback via UI
  2. Data stored in local `customer_feedback` table
  3. Background sync to centralized `restaurant_feedback` table
  4. Sentiment summary updated automatically
- **Location**: Handled in `feedbackService.ts`
- **Admin Action**: None required

#### Manual Migration (One-time)
- **When**: For existing historical feedback data
- **Process**:
  1. Admin accesses sync dashboard
  2. Clicks "Migrate Data" button
  3. All existing feedback synced to centralized database
- **Location**: Admin dashboard at `/admin/feedback-sync`
- **Admin Action**: Manual trigger required

## Admin Access Points

### Location: Admin Dashboard → AI Training Sync

**Path**: `/admin/feedback-sync`

**Access**: 
1. Login to admin panel (`/admin`)
2. Navigate to "AI Training Sync" in sidebar
3. Use dashboard to manage sync

### Dashboard Features

#### Connection Status
- Real-time connection test to centralized database
- Visual indicator (green/red/yellow)

#### Sync Status
- Shows count of local feedback records
- Indicates if migration is needed
- Connection summary

#### Actions Available

1. **📊 Check Status**
   - Refresh sync status
   - Check connection health
   - Count feedback records

2. **🧪 Test Sync**
   - Send sample data to centralized database
   - Verify sync functionality
   - Test AI training pipeline

3. **🔄 Migrate Data**
   - One-time migration of all existing feedback
   - Progress tracking with detailed results
   - Error handling and retry logic

4. **⚙️ Setup Auto-Sync**
   - Verify automatic sync configuration
   - Test real-time sync functionality

## Technical Implementation

### Services

#### `FeedbackSyncService`
- Direct database-to-database sync
- UUID conversion for centralized database
- Sentiment summary calculation
- Retry queue management

#### `FeedbackMigrationService`
- Bulk migration utilities
- Status checking
- Auto-sync verification

#### `feedbackService.ts`
- Customer feedback submission
- Automatic background sync
- Local storage and fallbacks

### Key Features

#### UUID Handling
- Converts string IDs to UUIDs for centralized database
- Deterministic UUID generation ensures consistency
- Prevents duplicate records

#### Sentiment Analysis
- Local sentiment calculation from text and ratings
- Centralized sentiment processing for AI training
- Restaurant-wide sentiment summaries

#### Error Handling
- Retry queue for failed syncs
- Detailed error logging
- Graceful fallbacks

## Business Impact

### For AI Training
- ✅ Real-time feedback data available for AI models
- ✅ Cross-restaurant sentiment analysis
- ✅ Customer behavior pattern recognition
- ✅ Predictive analytics for service improvement

### For Restaurant Operations
- ✅ Immediate feedback visibility
- ✅ Local sentiment tracking
- ✅ Order-specific feedback history
- ✅ Staff performance insights

## Monitoring and Maintenance

### Admin Responsibilities

1. **Initial Setup** (One-time)
   - Run migration for existing feedback
   - Verify auto-sync is working

2. **Ongoing Monitoring** (Optional)
   - Check sync status periodically
   - Review error logs if issues arise
   - Test sync functionality after system updates

### Automatic Processes

- New feedback automatically syncs
- Sentiment summaries auto-update
- Retry queue processes failed syncs
- Connection health monitoring

## Security Considerations

- Customer email data is optional and encrypted
- Anonymous feedback supported
- No sensitive payment information stored
- Cross-database access uses service roles

## Getting Started

### For New Installations
1. Admin logs into dashboard
2. Navigate to `/admin/feedback-sync`
3. Click "Setup Auto-Sync" to verify configuration
4. Click "Test Sync" to verify functionality

### For Existing Installations
1. Admin logs into dashboard
2. Navigate to `/admin/feedback-sync`
3. Click "Migrate Data" for historical feedback
4. Click "Setup Auto-Sync" to enable real-time sync

## Support

For technical issues or questions about the feedback sync system:
- Check the dashboard error messages
- Review the detailed migration results
- Contact system administrator for database access issues 