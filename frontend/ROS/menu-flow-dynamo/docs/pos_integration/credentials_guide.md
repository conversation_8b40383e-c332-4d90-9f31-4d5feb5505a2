# POS Integration Credentials Guide

## Overview

This document explains how to obtain and manage credentials for integrating Menu Flow Dynamo with Spanish POS systems. The integration we've built allows restaurants to maintain their existing POS systems while gaining the benefits of your dynamic pricing, QR ordering, and table management features.

## Credential Storage

All POS provider credentials should be stored securely in environment variables or a secure vault. **Never commit credentials to the codebase**.

```python
# Example of how credentials are used in the integration
import os
from app.integrations.pos_systems import OracleMicrosIntegration

# Credentials loaded from environment variables
integration = OracleMicrosIntegration(
    client_id=os.getenv("ORACLE_MICROS_CLIENT_ID"),
    client_secret=os.getenv("ORACLE_MICROS_CLIENT_SECRET"),
    restaurant_id=restaurant_id
)
```

## Supported POS Systems

### Oracle MICROS
**Status:** Application template prepared  
**Difficulty:** Medium-High (corporate approval process)  
**Timeframe:** 2-4 weeks for approval  
**Documentation:** [Oracle MICROS API Documentation](https://docs.oracle.com/en/industries/food-beverage/)

### Deliverect (Middleware)
**Status:** Application template prepared  
**Difficulty:** Medium (faster approval than direct POS integrations)  
**Timeframe:** 1-2 weeks for approval  
**Documentation:** [Deliverect API Documentation](https://developer.deliverect.com/)

### Other Spanish POS Systems
- **Cashlogy**: Direct contact required via sales team
- **Cuiner**: Partner program application needed
- **TPV's**: Various regional systems may require individual partnerships

## Application Process

1. **Complete the application templates** provided in this directory
2. **Submit applications** through each provider's developer portal
3. **Schedule technical calls** with the provider's integration team
4. **Receive credentials** and securely store them
5. **Update configuration** in Menu Flow Dynamo's integration layer

## Configuration File

When you receive credentials, update the configuration in:
```
/Users/<USER>/Analytica/app/core/config.py
```

Add the following variables:
```python
# POS Integration Settings
POS_INTEGRATION_ENABLED = True
POS_PROVIDER_CREDENTIALS = {
    "ORACLE_MICROS": {
        "client_id": os.getenv("ORACLE_MICROS_CLIENT_ID"),
        "client_secret": os.getenv("ORACLE_MICROS_CLIENT_SECRET"),
        "api_url": os.getenv("ORACLE_MICROS_API_URL")
    },
    "DELIVERECT": {
        "api_key": os.getenv("DELIVERECT_API_KEY"),
        "merchant_id": os.getenv("DELIVERECT_MERCHANT_ID"),
        "api_url": os.getenv("DELIVERECT_API_URL")
    }
}
```

## Testing Credentials

Use the integration test endpoint to verify credentials are working:

```
POST /api/v1/pos-integration/test-connection
{
    "provider": "ORACLE_MICROS",
    "restaurant_id": "your_restaurant_id"
}
```

A successful response indicates the credentials are valid and the connection is working.

## Support Contacts

If you encounter issues obtaining credentials:

- **Oracle MICROS**: <EMAIL>
- **Deliverect**: <EMAIL>

## Next Steps

After obtaining credentials:

1. Run integration tests
2. Configure webhook endpoints for real-time updates
3. Train staff on monitoring the integration status page
4. Set up alerting for any integration issues
