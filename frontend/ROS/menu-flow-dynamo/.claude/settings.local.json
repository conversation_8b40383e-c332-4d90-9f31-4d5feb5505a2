{"permissions": {"allow": ["Bash(npm run dev:*)", "Bash(grep:*)", "Bash(npm run lint)", "<PERSON><PERSON>(pkill:*)", "Bash(find:*)", "Bash(node:*)", "Bash(supabase functions deploy:*)", "Bash(npm test:*)", "Bash(npm run:*)", "Bash(npm audit:*)", "Bash(rg:*)", "WebFetch(domain:localhost)", "Bash(lsof:*)", "<PERSON><PERSON>(curl:*)", "Bash(ls:*)", "<PERSON><PERSON>(task-master:*)", "Bash(rm:*)", "Bash(git add:*)", "Bash(git push:*)", "Bash(git commit:*)", "Bash(git grep:*)", "Bash(git checkout:*)", "Bash(npx tsc:*)", "Bash(git revert:*)", "<PERSON><PERSON>(docker-compose up:*)", "<PERSON><PERSON>(docker-compose restart:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(docker build:*)", "<PERSON><PERSON>(docker run:*)", "Bash(docker logs:*)", "<PERSON><PERSON>(docker stop:*)", "<PERSON><PERSON>(docker rm:*)", "Bash(docker tag:*)", "<PERSON><PERSON>(render-cli:*)", "<PERSON><PERSON>(render --help)", "Bash(render services list)", "Bash(render services:*)", "Bash(render workspace list:*)", "Bash(render workspace current:*)", "<PERSON><PERSON>(render whoami:*)", "<PERSON><PERSON>(docker push:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(docker pull:*)", "Bash(render workspace set:*)", "Bash(render deploys create:*)", "Bash(render restart:*)", "Bash(render deploy:*)"], "deny": []}}